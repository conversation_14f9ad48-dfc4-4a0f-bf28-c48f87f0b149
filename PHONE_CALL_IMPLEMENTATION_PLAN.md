# Phone Call Implementation Plan for Talkloop Voice Agent

## Overview

This document outlines the step-by-step plan to enable phone call answering for your Talkloop voice agent system. Based on analysis of your existing codebase, most infrastructure is already in place.

## Current State Analysis

### ✅ Already Implemented
- Twilio integration with webhook handling (`/api/integrations/twilio/webhook`)
- OpenAI Realtime API integration for voice conversations
- Call logging and management system
- Agent management with custom prompts
- Database schema for calls and agents
- WebSocket streaming for real-time audio
- TwiML generation for call routing

### ❌ Issues to Fix
- OpenAI service has mock implementation instead of real realtime provider
- Missing audio format conversion (Twilio mulaw ↔ OpenAI PCM16)
- No environment configuration template
- Audio sample rate conversion needed (8kHz ↔ 24kHz)

## Step-by-Step Implementation Plan

### Phase 1: External Service Setup (No Code Changes)

#### 1.1 Twilio Account Setup
- Sign up at [Twilio Console](https://console.twilio.com/)
- Purchase a phone number that supports Voice calls
- Note your Account SID (starts with AC) and Auth Token
- Configure webhook URL: `https://your-domain.com/api/integrations/twilio/webhook`

#### 1.2 OpenAI API Access
- Ensure you have OpenAI Realtime API access
- Get your API key (starts with sk-)

#### 1.3 Public Domain Setup
- **For Testing**: Use ngrok to expose localhost (`ngrok http 3000`)
- **For Production**: Deploy to cloud service (Heroku, Railway, etc.)

### Phase 2: Environment Configuration

#### 2.1 Create Environment Template
Create `server/.env.example` with:
```env
# Database Configuration
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=postgres
DATABASE_NAME=talkloop

# Application Configuration
NODE_ENV=development
PORT=3000
HOST=your-domain.com

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key

# Twilio Configuration
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key
```

#### 2.2 Environment Setup
- Copy `.env.example` to `.env`
- Fill in actual values for all variables

### Phase 3: Code Implementation

#### 3.1 Fix OpenAI Service Integration
**File**: `server/src/integrations/openai/openai.service.ts`

**Changes needed**:
- Replace mock realtime implementation with actual RealtimeService
- Add RealtimeService import and injection
- Update `handleWebSocketConnection` method to use real provider
- Handle Twilio media stream format properly

#### 3.2 Add Audio Conversion Utilities
**File**: `server/src/integrations/realtime/utils/audio-converter.ts`

**Functionality needed**:
- Convert mulaw (8kHz) to PCM16 (24kHz) for OpenAI input
- Convert PCM16 (24kHz) to mulaw (8kHz) for Twilio output
- Handle sample rate conversion and resampling
- Audio format validation

#### 3.3 Update Module Dependencies
**File**: `server/src/integrations/openai/openai.module.ts`

**Changes needed**:
- Import RealtimeModule
- Add to module imports array

### Phase 4: Configuration & Testing

#### 4.1 Twilio Webhook Configuration
- In Twilio Console, go to Phone Numbers → Active Numbers
- Click your purchased number
- Set Voice webhook URL to your public endpoint
- Set HTTP method to POST

#### 4.2 Agent Creation
- Start your application (`npm run start:dev`)
- Use existing dashboard to create an agent
- Set agent's `twilioPhoneNumber` to your purchased number
- Configure agent prompt for phone conversations

#### 4.3 Testing
- Call your Twilio phone number
- Verify webhook receives call data
- Test voice conversation with AI
- Check call logging in database

## Call Flow Architecture

```
Incoming Call → Twilio → Webhook → Agent Lookup → Call Record Creation
                                        ↓
TwiML Response ← OpenAI Realtime API ← WebSocket Connection
                                        ↓
Voice Conversation ← Audio Conversion ← Media Stream
                                        ↓
Call Completion → Database Update → Recording/Transcription Storage
```

## Technical Details

### Audio Processing
- **Twilio Format**: mulaw, 8kHz, mono
- **OpenAI Format**: PCM16, 24kHz, mono
- **Conversion Required**: Format + sample rate conversion both ways

### WebSocket Handling
- Twilio sends media events with base64 encoded audio
- OpenAI expects PCM16 audio in specific message format
- Bidirectional audio streaming with proper format conversion

### Error Handling
- Connection failures → Graceful fallback
- Audio conversion errors → Logging and recovery
- API rate limits → Proper error responses

## Estimated Implementation Time

- **Phase 1 (External Setup)**: 15 minutes
- **Phase 2 (Environment)**: 10 minutes
- **Phase 3 (Code Changes)**: 45 minutes
- **Phase 4 (Testing)**: 15 minutes
- **Total**: ~1.5 hours

## Success Criteria

✅ Phone calls are received and processed
✅ Real-time voice conversation works
✅ Audio quality is acceptable
✅ Calls are properly logged in database
✅ Agent prompts are followed during conversations
✅ Function calling works (calendar integration)

## Next Steps

1. Review this plan
2. Gather required API keys and accounts
3. Decide on deployment strategy (ngrok vs cloud)
4. Execute phases in order
5. Test thoroughly before production use

## Notes

- Your existing codebase is well-structured for this implementation
- Most complex parts (realtime API, database, agents) are already built
- Main work is connecting existing pieces and fixing the mock implementation
- Audio conversion is the most technically complex part but manageable
