import { Link, useLocation } from "react-router-dom";
import {
  Home as HomeIcon,
  Users as UserGroupIcon,
  Phone as PhoneIcon,
  Settings as SettingsIcon,
  ShieldCheck as AdminIcon,
  Menu,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet";
import {
  Sidebar as SidebarComponent,
  SidebarHeader,
  SidebarContent,
  SidebarNav,
  SidebarNavItem,
} from "@/components/ui/sidebar";
import { useAuth } from "../../contexts/AuthContext";

interface SidebarProps {
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
}

// Base navigation items
const baseNavigation = [
  { name: "Dashboard", href: "/", icon: HomeIcon },
  { name: "Agents", href: "/agents", icon: UserGroupIcon },
  { name: "Calls", href: "/calls", icon: PhoneIcon },
  { name: "Settings", href: "/settings", icon: SettingsIcon },
];

// Admin navigation item
const adminNavItem = { name: "Admin", href: "/admin", icon: AdminIcon };

const Sidebar = ({ sidebarOpen, setSidebarOpen }: SidebarProps) => {
  const location = useLocation();
  const { user } = useAuth();

  // Check if user is admin (for now, just check if <NAME_EMAIL>)
  const isAdmin = user?.email === "<EMAIL>";

  // Create navigation items based on user role
  const navigation = isAdmin
    ? [...baseNavigation, adminNavItem]
    : baseNavigation;

  return (
    <>
      {/* Mobile sidebar */}
      <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
        <SheetTrigger asChild>
          <Button variant="ghost" size="icon" className="md:hidden">
            <Menu className="h-6 w-6" />
            <span className="sr-only">Toggle menu</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="p-0">
          <SidebarComponent>
            <SidebarHeader className="h-16">
              <Link to="/" className="text-xl font-bold text-primary">
                Talkloop
              </Link>
            </SidebarHeader>
            <SidebarContent>
              <SidebarNav>
                {navigation.map((item) => {
                  const Icon = item.icon;
                  return (
                    <Link
                      key={item.name}
                      to={item.href}
                      onClick={() => setSidebarOpen(false)}
                    >
                      <SidebarNavItem
                        active={location.pathname === item.href}
                        icon={<Icon className="h-5 w-5" />}
                        label={item.name}
                      />
                    </Link>
                  );
                })}
              </SidebarNav>
            </SidebarContent>
          </SidebarComponent>
        </SheetContent>
      </Sheet>

      {/* Desktop sidebar */}
      <div className="hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0">
        <SidebarComponent>
          <SidebarHeader className="h-16">
            <Link to="/" className="text-xl font-bold text-primary">
              Talkloop
            </Link>
          </SidebarHeader>
          <SidebarContent>
            <SidebarNav>
              {navigation.map((item) => {
                const Icon = item.icon;
                return (
                  <Link key={item.name} to={item.href}>
                    <SidebarNavItem
                      active={location.pathname === item.href}
                      icon={<Icon className="h-5 w-5" />}
                      label={item.name}
                    />
                  </Link>
                );
              })}
            </SidebarNav>
          </SidebarContent>
        </SidebarComponent>
      </div>
    </>
  );
};

export default Sidebar;
