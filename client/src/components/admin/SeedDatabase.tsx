import { useState } from 'react';
import { Button } from '@/components/ui/button';
import api from '../../services/api';

const SeedDatabase = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<{
    success?: boolean;
    message?: string;
    details?: string;
    error?: string;
  } | null>(null);

  const handleSeedDatabase = async () => {
    if (!confirm('Are you sure you want to seed the database? This will add dummy data.')) {
      return;
    }

    setIsLoading(true);
    setResult(null);

    try {
      const response = await api.post('/seed');
      setResult(response.data);
    } catch (error) {
      console.error('Error seeding database:', error);
      setResult({
        success: false,
        message: 'Error seeding database',
        error: error.response?.data?.message || error.message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-card shadow-sm rounded-lg border p-6 space-y-4">
      <div>
        <h2 className="text-xl font-semibold">Database Seeder</h2>
        <p className="text-sm text-muted-foreground mt-1">
          Seed the database with dummy data including an admin user (<EMAIL> / password)
        </p>
      </div>

      <Button 
        onClick={handleSeedDatabase} 
        disabled={isLoading}
        className="w-full"
      >
        {isLoading ? 'Seeding Database...' : 'Seed Database'}
      </Button>

      {result && (
        <div className={`p-4 rounded-md ${result.success ? 'bg-green-50 border border-green-200' : 'bg-destructive/10 border border-destructive/20'}`}>
          <p className={`text-sm font-medium ${result.success ? 'text-green-800' : 'text-destructive'}`}>
            {result.message}
          </p>
          {result.details && (
            <pre className="mt-2 text-xs bg-background p-2 rounded overflow-auto max-h-40">
              {result.details}
            </pre>
          )}
          {result.error && (
            <pre className="mt-2 text-xs bg-background p-2 rounded overflow-auto max-h-40 text-destructive">
              {result.error}
            </pre>
          )}
        </div>
      )}
    </div>
  );
};

export default SeedDatabase;
