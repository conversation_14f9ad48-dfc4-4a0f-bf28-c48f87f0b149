import React from 'react';
import { cn } from '../../lib/utils';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'dots' | 'pulse' | 'bars' | 'orbit';
  className?: string;
  text?: string;
  fullScreen?: boolean;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  variant = 'default',
  className,
  text,
  fullScreen = false,
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16',
  };

  const containerClasses = fullScreen
    ? 'fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50'
    : 'flex items-center justify-center';

  const renderSpinner = () => {
    switch (variant) {
      case 'dots':
        return (
          <div className="flex space-x-1">
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                className={cn(
                  'bg-primary rounded-full animate-pulse',
                  size === 'sm' ? 'w-2 h-2' : size === 'md' ? 'w-3 h-3' : size === 'lg' ? 'w-4 h-4' : 'w-5 h-5'
                )}
                style={{
                  animationDelay: `${i * 0.2}s`,
                  animationDuration: '1.4s',
                }}
              />
            ))}
          </div>
        );

      case 'pulse':
        return (
          <div className="relative">
            <div
              className={cn(
                'rounded-full bg-primary/20 animate-ping',
                sizeClasses[size]
              )}
            />
            <div
              className={cn(
                'absolute inset-0 rounded-full bg-primary/40 animate-ping',
                sizeClasses[size]
              )}
              style={{ animationDelay: '0.5s' }}
            />
            <div
              className={cn(
                'absolute inset-2 rounded-full bg-primary animate-pulse',
                size === 'sm' ? 'inset-1' : size === 'xl' ? 'inset-3' : 'inset-2'
              )}
            />
          </div>
        );

      case 'bars':
        return (
          <div className="flex space-x-1">
            {[0, 1, 2, 3, 4].map((i) => (
              <div
                key={i}
                className={cn(
                  'bg-primary animate-pulse',
                  size === 'sm' ? 'w-1 h-4' : size === 'md' ? 'w-1 h-6' : size === 'lg' ? 'w-2 h-8' : 'w-2 h-10'
                )}
                style={{
                  animationDelay: `${i * 0.1}s`,
                  animationDuration: '1.2s',
                }}
              />
            ))}
          </div>
        );

      case 'orbit':
        return (
          <div className={cn('relative', sizeClasses[size])}>
            <div className="absolute inset-0 rounded-full border-2 border-primary/20" />
            <div className="absolute inset-0 rounded-full border-2 border-transparent border-t-primary animate-spin" />
            <div
              className="absolute inset-1 rounded-full border-2 border-transparent border-t-primary/60 animate-spin"
              style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}
            />
            <div className="absolute inset-0 flex items-center justify-center">
              <div className={cn(
                'rounded-full bg-primary animate-pulse',
                size === 'sm' ? 'w-1 h-1' : size === 'md' ? 'w-2 h-2' : size === 'lg' ? 'w-3 h-3' : 'w-4 h-4'
              )} />
            </div>
          </div>
        );

      default:
        return (
          <div
            className={cn(
              'animate-spin rounded-full border-4 border-primary/20 border-t-primary',
              sizeClasses[size],
              className
            )}
          />
        );
    }
  };

  return (
    <div className={containerClasses}>
      <div className="flex flex-col items-center space-y-4">
        {renderSpinner()}
        {text && (
          <div className="text-center">
            <p className="text-sm font-medium text-muted-foreground animate-pulse">
              {text}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default LoadingSpinner;
