import React, { useState, useEffect } from 'react';
import { cn } from '../../lib/utils';

interface AuthLoadingProps {
  className?: string;
}

const AuthLoading: React.FC<AuthLoadingProps> = ({ className }) => {
  const [dots, setDots] = useState('');
  const [currentMessage, setCurrentMessage] = useState(0);

  const messages = [
    'Authenticating...',
    'Verifying credentials...',
    'Loading your workspace...',
    'Almost ready...',
  ];

  // Animated dots effect
  useEffect(() => {
    const interval = setInterval(() => {
      setDots(prev => {
        if (prev.length >= 3) return '';
        return prev + '.';
      });
    }, 500);

    return () => clearInterval(interval);
  }, []);

  // Cycling messages effect
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentMessage(prev => (prev + 1) % messages.length);
    }, 2000);

    return () => clearInterval(interval);
  }, [messages.length]);

  return (
    <div className={cn(
      'fixed inset-0 bg-gradient-to-br from-background via-background to-muted/20 flex items-center justify-center z-50',
      className
    )}>
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(120,119,198,0.3),transparent_50%)]" />
        <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(120,119,198,0.1)_50%,transparent_75%)] bg-[length:20px_20px] animate-pulse" />
      </div>

      <div className="relative flex flex-col items-center space-y-8 p-8">
        {/* Main Loading Animation */}
        <div className="relative float-animation">
          {/* Outer Ring */}
          <div className="w-24 h-24 rounded-full border-4 border-primary/20 relative glow-animation">
            {/* Spinning Ring */}
            <div className="absolute inset-0 rounded-full border-4 border-transparent border-t-primary border-r-primary/60 animate-spin" />

            {/* Inner Pulsing Circle */}
            <div className="absolute inset-3 rounded-full bg-primary/10 animate-pulse">
              <div className="absolute inset-2 rounded-full bg-primary/20 animate-ping" />
              <div className="absolute inset-4 rounded-full bg-primary animate-pulse" />
            </div>

            {/* Orbiting Dots */}
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                className="absolute w-3 h-3 bg-primary rounded-full animate-spin"
                style={{
                  top: '50%',
                  left: '50%',
                  transformOrigin: '0 0',
                  transform: `rotate(${i * 120}deg) translateX(40px) translateY(-6px)`,
                  animationDuration: '3s',
                  animationDelay: `${i * 0.5}s`,
                }}
              >
                <div className="w-full h-full bg-primary rounded-full animate-pulse" />
              </div>
            ))}
          </div>

          {/* Floating Particles */}
          {[...Array(6)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-primary/40 rounded-full animate-bounce"
              style={{
                top: `${20 + Math.sin(i) * 30}%`,
                left: `${20 + Math.cos(i) * 30}%`,
                animationDelay: `${i * 0.3}s`,
                animationDuration: '2s',
              }}
            />
          ))}
        </div>

        {/* Loading Text */}
        <div className="text-center space-y-3">
          <h2 className="text-2xl font-semibold text-foreground">
            TalkLoop
          </h2>

          <div className="h-6 flex items-center justify-center">
            <p className="text-lg font-medium text-primary transition-all duration-500 ease-in-out">
              {messages[currentMessage]}{dots}
            </p>
          </div>

          <p className="text-sm text-muted-foreground max-w-xs">
            Setting up your AI-powered communication workspace
          </p>
        </div>

        {/* Progress Indicator */}
        <div className="w-64 h-1 bg-muted rounded-full overflow-hidden">
          <div className="h-full bg-gradient-to-r from-primary to-primary/60 rounded-full animate-pulse"
               style={{
                 width: '100%',
                 animation: 'loading-progress 3s ease-in-out infinite',
               }} />
        </div>
      </div>

      {/* Custom CSS for smooth animations */}
      <style jsx>{`
        @keyframes loading-progress {
          0% { width: 0%; }
          50% { width: 70%; }
          100% { width: 100%; }
        }

        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-10px); }
        }

        @keyframes glow {
          0%, 100% { box-shadow: 0 0 5px rgba(120, 119, 198, 0.5); }
          50% { box-shadow: 0 0 20px rgba(120, 119, 198, 0.8), 0 0 30px rgba(120, 119, 198, 0.6); }
        }

        .float-animation {
          animation: float 3s ease-in-out infinite;
        }

        .glow-animation {
          animation: glow 2s ease-in-out infinite;
        }
      `}</style>
    </div>
  );
};

export default AuthLoading;
