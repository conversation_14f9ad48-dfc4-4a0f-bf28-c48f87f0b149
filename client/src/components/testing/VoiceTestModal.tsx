import { useState, useEffect } from "react";
import { Dialog } from "@headlessui/react";
import {
  XMarkIcon,
  MicrophoneIcon,
  StopIcon,
  PaperAirplaneIcon,
  PlayIcon,
} from "@heroicons/react/24/outline";
import { useVoiceTest, type VoiceTestState } from "../../hooks/useVoiceTest";
import { Button } from "../ui/button";
import { Input } from "../ui/input";

interface Agent {
  id: string;
  name: string;
  prompt: string;
}

interface VoiceTestModalProps {
  agent: Agent;
  isOpen: boolean;
  onClose: () => void;
  userId: string;
}

export const VoiceTestModal = ({
  agent,
  isOpen,
  onClose,
  userId,
}: VoiceTestModalProps) => {
  const [textInput, setTextInput] = useState("");
  const [selectedProvider, setSelectedProvider] = useState<"openai" | "gemini">(
    "gemini"
  );
  const [voiceTestState, voiceTestControls] = useVoiceTest(userId);

  const {
    isConnected,
    isTestActive,
    messages,
    error,
    isLoading,
    isRecording,
    audioError,
  } = voiceTestState as VoiceTestState & {
    isRecording: boolean;
    audioError: string | null;
  };

  const {
    startTest,
    endTest,
    sendTextMessage,
    clearError,
    toggleRecording,
    playAudio,
  } = voiceTestControls;

  // Start test when modal opens
  useEffect(() => {
    if (isOpen && isConnected && !isTestActive && !isLoading) {
      startTest(agent.id, selectedProvider);
    }
  }, [
    isOpen,
    isConnected,
    isTestActive,
    isLoading,
    agent.id,
    selectedProvider,
    startTest,
  ]);

  // Clean up when modal closes
  useEffect(() => {
    if (!isOpen && isTestActive) {
      endTest();
    }
  }, [isOpen, isTestActive, endTest]);

  const handleSendText = () => {
    if (textInput.trim()) {
      sendTextMessage(textInput.trim());
      setTextInput("");
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendText();
    }
  };

  const handleClose = () => {
    if (isTestActive) {
      endTest();
    }
    onClose();
  };

  return (
    <Dialog open={isOpen} onClose={handleClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="mx-auto max-w-2xl w-full bg-white rounded-lg shadow-xl">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b">
            <div>
              <Dialog.Title className="text-lg font-semibold text-gray-900">
                Testing: {agent.name}
              </Dialog.Title>
              <p className="text-sm text-gray-500 mt-1">
                Voice chat with your AI agent
              </p>
              <div className="mt-2">
                <label className="text-xs text-gray-500">Provider:</label>
                <select
                  value={selectedProvider}
                  onChange={(e) =>
                    setSelectedProvider(e.target.value as "openai" | "gemini")
                  }
                  disabled={isTestActive}
                  className="ml-2 text-xs border rounded px-2 py-1"
                >
                  <option value="openai">OpenAI Realtime</option>
                  <option value="gemini">Google Gemini Live</option>
                </select>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-500"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          {/* Connection Status */}
          <div className="px-6 py-2 bg-gray-50 border-b">
            <div className="flex items-center space-x-2">
              <div
                className={`w-2 h-2 rounded-full ${
                  isConnected ? "bg-green-500" : "bg-red-500"
                }`}
              />
              <span className="text-sm text-gray-600">
                {isConnected ? "Connected" : "Connecting..."}
              </span>
              {isTestActive && (
                <>
                  <span className="text-gray-400">•</span>
                  <span className="text-sm text-green-600">Test Active</span>
                </>
              )}
            </div>
          </div>

          {/* Error Display */}
          {(error || audioError) && (
            <div className="px-6 py-3 bg-red-50 border-b">
              <div className="flex items-center justify-between">
                <p className="text-sm text-red-600">{error || audioError}</p>
                <button
                  onClick={clearError}
                  className="text-red-400 hover:text-red-500 text-sm"
                >
                  Dismiss
                </button>
              </div>
            </div>
          )}

          {/* Messages */}
          <div className="h-96 overflow-y-auto p-6 space-y-4">
            {isLoading && (
              <div className="text-center py-8">
                <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-indigo-500 border-t-transparent"></div>
                <p className="mt-2 text-gray-500">Starting test...</p>
              </div>
            )}

            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${
                  message.speaker === "user" ? "justify-end" : "justify-start"
                }`}
              >
                <div
                  className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    message.speaker === "user"
                      ? "bg-indigo-600 text-white"
                      : "bg-gray-200 text-gray-900"
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <p className="text-sm">{message.content}</p>
                    {message.type === "audio" && message.audioData && (
                      <button
                        onClick={() => playAudio(message.audioData)}
                        className={`ml-2 p-1 rounded-full hover:bg-opacity-20 hover:bg-gray-500 ${
                          message.speaker === "user"
                            ? "text-indigo-200 hover:text-white"
                            : "text-gray-600 hover:text-gray-900"
                        }`}
                        title="Play audio"
                      >
                        <PlayIcon className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                  <p
                    className={`text-xs mt-1 ${
                      message.speaker === "user"
                        ? "text-indigo-200"
                        : "text-gray-500"
                    }`}
                  >
                    {new Date(message.timestamp).toLocaleTimeString()}
                    {message.type === "audio" && " (audio)"}
                  </p>
                </div>
              </div>
            ))}

            {messages.length === 0 && !isLoading && (
              <div className="text-center py-8 text-gray-500">
                <p>Start talking or type a message to begin the conversation</p>
              </div>
            )}
          </div>

          {/* Controls */}
          <div className="p-6 border-t bg-gray-50">
            {/* Voice Controls */}
            <div className="flex items-center justify-center mb-4">
              <Button
                onClick={toggleRecording}
                disabled={!isTestActive}
                className={`flex items-center space-x-2 ${
                  isRecording
                    ? "bg-red-600 hover:bg-red-700"
                    : "bg-indigo-600 hover:bg-indigo-700"
                }`}
              >
                {isRecording ? (
                  <>
                    <StopIcon className="h-5 w-5" />
                    <span>Stop Recording</span>
                  </>
                ) : (
                  <>
                    <MicrophoneIcon className="h-5 w-5" />
                    <span>Start Recording</span>
                  </>
                )}
              </Button>
            </div>

            {/* Text Input */}
            <div className="flex space-x-2">
              <Input
                value={textInput}
                onChange={(e) => setTextInput(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Type a message..."
                disabled={!isTestActive}
                className="flex-1"
              />
              <Button
                onClick={handleSendText}
                disabled={!isTestActive || !textInput.trim()}
                size="sm"
              >
                <PaperAirplaneIcon className="h-4 w-4" />
              </Button>
            </div>

            <p className="text-xs text-gray-500 mt-2 text-center">
              Use voice recording for natural conversation or type messages for
              testing
            </p>
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
};
