import React from "react";
import { MicrophoneIcon, StopIcon } from "@heroicons/react/24/outline";
import type {
  SessionStatus,
  ProviderType,
} from "../../contexts/TestingContext";

interface BottomToolbarProps {
  sessionStatus: SessionStatus;
  onToggleConnection: () => void;
  onStartTest: () => void;
  onSwitchProvider?: (provider: ProviderType) => Promise<void>;
  isRecording: boolean;
  onToggleRecording: () => void;
  isEventsPaneExpanded: boolean;
  setIsEventsPaneExpanded: (val: boolean) => void;
  provider: ProviderType;
  onProviderChange: (provider: ProviderType) => void;
  disabled?: boolean;
}

function BottomToolbar({
  sessionStatus,
  onToggleConnection,
  onStartTest,
  onSwitchProvider,
  isRecording,
  onToggleRecording,
  isEventsPaneExpanded,
  setIsEventsPaneExpanded,
  provider,
  onProviderChange,
  disabled = false,
}: BottomToolbarProps) {
  const isConnected = sessionStatus === "CONNECTED";
  const isConnecting = sessionStatus === "CONNECTING";

  const handleProviderChange = async (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newProvider = e.target.value as ProviderType;
    
    if (isConnected && onSwitchProvider) {
      // If connected and we have a switch function, use runtime switching
      try {
        await onSwitchProvider(newProvider);
      } catch (error) {
        console.error("Failed to switch provider:", error);
        // Reset to current provider on error
        e.target.value = provider;
      }
    } else {
      // Otherwise use the normal provider change (for pre-connection)
      onProviderChange(newProvider);
    }
  };

  function getConnectionButtonLabel() {
    if (isConnected) return "Disconnect";
    if (isConnecting) return "Connecting...";
    return "Connect";
  }

  function getConnectionButtonClasses() {
    const baseClasses = "text-white text-base p-2 w-36 rounded-md h-full";
    const cursorClass =
      isConnecting || disabled ? "cursor-not-allowed" : "cursor-pointer";

    if (isConnected) {
      // Connected -> label "Disconnect" -> red
      return `bg-red-600 hover:bg-red-700 ${cursorClass} ${baseClasses}`;
    }
    // Disconnected or connecting -> label is either "Connect" or "Connecting" -> black
    return `bg-black hover:bg-gray-900 ${cursorClass} ${baseClasses}`;
  }

  function getRecordingButtonClasses() {
    const baseClasses =
      "flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium";

    if (isRecording) {
      return `${baseClasses} bg-red-600 text-white hover:bg-red-700`;
    }

    if (!isConnected) {
      return `${baseClasses} bg-gray-200 text-gray-400 cursor-not-allowed`;
    }

    return `${baseClasses} bg-green-600 text-white hover:bg-green-700`;
  }

  return (
    <div className="p-4 bg-white border-t flex flex-row items-center justify-center gap-x-8">
      {/* Connection Button */}
      <button
        onClick={onToggleConnection}
        className={getConnectionButtonClasses()}
        disabled={isConnecting || disabled}
      >
        {getConnectionButtonLabel()}
      </button>

      {/* Start Test Button */}
      <button
        onClick={onStartTest}
        disabled={!isConnected || disabled}
        className={`text-white text-base p-2 w-36 rounded-md h-full ${
          !isConnected || disabled
            ? "bg-gray-400 cursor-not-allowed"
            : "bg-blue-600 hover:bg-blue-700 cursor-pointer"
        }`}
      >
        Start Test
      </button>

      {/* Recording Button */}
      <button
        onClick={onToggleRecording}
        disabled={!isConnected || disabled}
        className={getRecordingButtonClasses()}
      >
        {isRecording ? (
          <>
            <StopIcon className="h-4 w-4" />
            Stop Recording
          </>
        ) : (
          <>
            <MicrophoneIcon className="h-4 w-4" />
            Start Recording
          </>
        )}
      </button>

      {/* Provider Selection */}
      <div className="flex flex-row items-center gap-2">
        <label className="text-sm font-medium text-gray-700">Provider:</label>
        <select
          value={provider}
          onChange={handleProviderChange}
          disabled={disabled}
          className="border border-gray-300 rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 cursor-pointer disabled:bg-gray-100 disabled:cursor-not-allowed"
        >
          <option value="openai">OpenAI Realtime</option>
          <option value="gemini">Google Gemini Live</option>
        </select>
      </div>

      {/* Events Panel Toggle */}
      <div className="flex flex-row items-center gap-2">
        <input
          id="events-panel"
          type="checkbox"
          checked={isEventsPaneExpanded}
          onChange={(e) => setIsEventsPaneExpanded(e.target.checked)}
          className="w-4 h-4"
        />
        <label
          htmlFor="events-panel"
          className="text-sm font-medium text-gray-700 cursor-pointer"
        >
          Show Events
        </label>
      </div>
    </div>
  );
}

export default BottomToolbar;
