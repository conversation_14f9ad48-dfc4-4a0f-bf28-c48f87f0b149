import React, { useEffect, useRef } from "react";
import { ChevronDownIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import { useEvent } from "../../contexts/EventContext";

interface EventsProps {
  isExpanded: boolean;
}

export default function Events({ isExpanded }: EventsProps) {
  const { events, toggleEventExpand } = useEvent();
  const scrollRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new events arrive
  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [events]);

  if (!isExpanded) {
    return null;
  }

  const getEventTypeColor = (direction: "client" | "server") => {
    return direction === "client"
      ? "bg-blue-50 border-blue-200 text-blue-800"
      : "bg-green-50 border-green-200 text-green-800";
  };

  const getDirectionIcon = (direction: "client" | "server") => {
    return direction === "client" ? "→" : "←";
  };

  const renderEventData = (data: Record<string, unknown>) => {
    try {
      return JSON.stringify(data, null, 2);
    } catch {
      return "Error serializing event data";
    }
  };

  return (
    <div className="w-1/3 bg-white rounded-lg shadow-sm border flex flex-col">
      {/* Header */}
      <div className="p-4 border-b">
        <h2 className="text-lg font-semibold text-gray-900">
          WebSocket Events
        </h2>
        <p className="text-sm text-gray-500">
          Real-time event logging ({events.length} events)
        </p>
      </div>

      {/* Events List */}
      <div
        ref={scrollRef}
        className="flex-1 overflow-y-auto p-2"
        style={{ maxHeight: "calc(100vh - 300px)" }}
      >
        {events.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <p>No events yet. Connect to start logging.</p>
          </div>
        ) : (
          <div className="space-y-2">
            {events.map((event) => (
              <div
                key={event.id}
                className={`border rounded-lg p-3 ${getEventTypeColor(
                  event.direction
                )}`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 flex-1 min-w-0">
                    <span className="text-lg font-mono">
                      {getDirectionIcon(event.direction)}
                    </span>
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-sm truncate">
                        {event.eventName}
                      </div>
                      <div className="text-xs opacity-70">
                        {event.timestamp}
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={() => toggleEventExpand(event.id)}
                    className="flex-shrink-0 p-1 hover:bg-black hover:bg-opacity-10 rounded"
                  >
                    {event.expanded ? (
                      <ChevronDownIcon className="h-4 w-4" />
                    ) : (
                      <ChevronRightIcon className="h-4 w-4" />
                    )}
                  </button>
                </div>

                {event.expanded && (
                  <div className="mt-3 pt-3 border-t border-current border-opacity-20">
                    <pre className="text-xs bg-black bg-opacity-5 p-2 rounded overflow-auto max-h-48 whitespace-pre-wrap">
                      {renderEventData(event.eventData)}
                    </pre>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
