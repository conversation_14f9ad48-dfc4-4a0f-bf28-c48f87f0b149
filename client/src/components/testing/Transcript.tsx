import React, { useEffect, useRef } from "react";
import {
  PlayIcon,
  ChevronDownIcon,
  ChevronRightIcon,
} from "@heroicons/react/24/outline";
import {
  useTranscript,
  type TranscriptItem,
} from "../../contexts/TranscriptContext";
import { Button } from "../ui/button";
import { Input } from "../ui/input";

interface TranscriptProps {
  userText: string;
  setUserText: (text: string) => void;
  onSendMessage: () => void;
  onPlayAudio?: (audioData: string) => void;
  canSend: boolean;
}

export default function Transcript({
  userText,
  setUserText,
  onSendMessage,
  onPlayAudio,
  canSend,
}: TranscriptProps) {
  const { transcriptItems, toggleTranscriptItemExpand } = useTranscript();
  const scrollRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [transcriptItems]);

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      onSendMessage();
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return timestamp;
  };

  const renderTranscriptItem = (item: TranscriptItem) => {
    if (item.type === "BREADCRUMB") {
      return (
        <div
          key={item.itemId}
          className="flex items-center gap-2 py-2 px-3 bg-blue-50 border-l-4 border-blue-400 my-2"
        >
          <button
            onClick={() => toggleTranscriptItemExpand(item.itemId)}
            className="flex-shrink-0"
          >
            {item.expanded ? (
              <ChevronDownIcon className="h-4 w-4 text-blue-600" />
            ) : (
              <ChevronRightIcon className="h-4 w-4 text-blue-600" />
            )}
          </button>
          <div className="flex-1">
            <div className="text-sm font-medium text-blue-800">
              {item.title}
            </div>
            <div className="text-xs text-blue-600">
              {formatTimestamp(item.timestamp)}
            </div>
            {item.expanded && item.data && (
              <pre className="mt-2 text-xs bg-blue-100 p-2 rounded overflow-auto max-h-32">
                {JSON.stringify(item.data, null, 2)}
              </pre>
            )}
          </div>
        </div>
      );
    }

    // MESSAGE type
    const isUser = item.role === "user";
    const isInProgress = item.status === "IN_PROGRESS";

    return (
      <div
        key={item.itemId}
        className={`flex ${isUser ? "justify-end" : "justify-start"} mb-4`}
      >
        <div
          className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
            isUser ? "bg-indigo-600 text-white" : "bg-gray-200 text-gray-900"
          } ${isInProgress ? "opacity-70" : ""}`}
        >
          <div className="flex items-start justify-between gap-2">
            <div className="flex-1">
              <p className="text-sm">{item.title || ""}</p>
              {isInProgress && (
                <div className="flex items-center mt-1">
                  <div className="animate-pulse flex space-x-1">
                    <div className="w-1 h-1 bg-current rounded-full"></div>
                    <div className="w-1 h-1 bg-current rounded-full"></div>
                    <div className="w-1 h-1 bg-current rounded-full"></div>
                  </div>
                  <span className="text-xs ml-2 opacity-70">typing...</span>
                </div>
              )}
            </div>
            {item.audioData && onPlayAudio && (
              <button
                onClick={() => onPlayAudio(item.audioData)}
                className={`flex-shrink-0 p-1 rounded-full hover:bg-opacity-20 hover:bg-gray-500 ${
                  isUser
                    ? "text-indigo-200 hover:text-white"
                    : "text-gray-600 hover:text-gray-900"
                }`}
                title="Play audio"
              >
                <PlayIcon className="h-4 w-4" />
              </button>
            )}
          </div>
          <div className="text-xs opacity-70 mt-1">
            {formatTimestamp(item.timestamp)}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="flex flex-col h-full bg-white rounded-lg shadow-sm border">
      {/* Header */}
      <div className="p-4 border-b">
        <h2 className="text-lg font-semibold text-gray-900">Conversation</h2>
        <p className="text-sm text-gray-500">Voice chat with your AI agent</p>
      </div>

      {/* Transcript */}
      <div
        ref={scrollRef}
        className="flex-1 overflow-y-auto p-4 space-y-2"
        style={{ maxHeight: "calc(100vh - 300px)" }}
      >
        {transcriptItems.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <p>No messages yet. Start a conversation!</p>
          </div>
        ) : (
          transcriptItems.map(renderTranscriptItem)
        )}
      </div>

      {/* Input */}
      <div className="p-4 border-t">
        <div className="flex space-x-2">
          <Input
            value={userText}
            onChange={(e) => setUserText(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type a message..."
            disabled={!canSend}
            className="flex-1"
          />
          <Button
            onClick={onSendMessage}
            disabled={!canSend || !userText.trim()}
            size="sm"
          >
            Send
          </Button>
        </div>
        <p className="text-xs text-gray-500 mt-2">
          Press Enter to send, or use voice recording for natural conversation
        </p>
      </div>
    </div>
  );
}
