import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import { TranscriptProvider } from "../contexts/TranscriptContext";
import { EventProvider } from "../contexts/EventContext";
import {
  TestingProvider,
  useTesting,
  type ProviderType,
} from "../contexts/TestingContext";
import Transcript from "../components/testing/Transcript";
import Events from "../components/testing/Events";
import BottomToolbar from "../components/testing/BottomToolbar";
import { useVoiceTestImproved } from "../hooks/useVoiceTestImproved";
import { getAgent } from "../services/agents";

interface Agent {
  id: string;
  name: string;
  prompt: string;
  twilioPhoneNumber: string;
  isActive: boolean;
  googleCalendarId: string | null;
  createdAt: string;
}

function TestingPageContent() {
  const { agentId } = useParams<{ agentId: string }>();
  const navigate = useNavigate();
  const [agent, setAgent] = useState<Agent | null>(null);
  const [userText, setUserText] = useState("");
  const [isEventsPaneExpanded, setIsEventsPaneExpanded] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasConnected, setHasConnected] = useState(false);

  const { testingState, setCurrentAgent, setCurrentProvider } = useTesting();
  const voiceTestControls = useVoiceTestImproved();

  // Load agent data
  useEffect(() => {
    const loadAgent = async () => {
      if (!agentId) {
        setError("No agent ID provided");
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        const agentData = await getAgent(agentId);
        setAgent(agentData);
        setCurrentAgent(agentId);
      } catch (err) {
        console.error("Error loading agent:", err);
        setError("Failed to load agent");
      } finally {
        setIsLoading(false);
      }
    };

    loadAgent();
  }, [agentId, setCurrentAgent]);

  // Auto-connect when agent is loaded (only once)
  useEffect(() => {
    if (
      agent &&
      testingState.sessionStatus === "DISCONNECTED" &&
      !hasConnected
    ) {
      setHasConnected(true);
      voiceTestControls.connect();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [agent, testingState.sessionStatus, hasConnected]);

  const handleSendMessage = () => {
    if (!userText.trim()) return;
    voiceTestControls.sendTextMessage(userText.trim());
    setUserText("");
  };

  const handleProviderChange = (provider: ProviderType) => {
    setCurrentProvider(provider);
  };

  const handleBack = () => {
    if (testingState.sessionStatus === "CONNECTED") {
      voiceTestControls.disconnect();
    }
    navigate("/calls");
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-indigo-500 border-t-transparent"></div>
          <p className="mt-2 text-gray-500">Loading agent...</p>
        </div>
      </div>
    );
  }

  if (error || !agent) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error || "Agent not found"}</p>
          <button
            onClick={handleBack}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Call History
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen bg-gray-100">
      {/* Header */}
      <div className="p-5 bg-white shadow-sm border-b flex items-center justify-between">
        <div className="flex items-center gap-4">
          <button
            onClick={handleBack}
            className="p-2 hover:bg-gray-100 rounded-md"
            title="Back to Call History"
          >
            <ArrowLeftIcon className="h-5 w-5 text-gray-600" />
          </button>
          <div>
            <h1 className="text-xl font-semibold text-gray-900">
              Testing: {agent.name}
            </h1>
            <p className="text-sm text-gray-500">
              Voice chat with your AI agent • Provider:{" "}
              {testingState.currentProvider}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <div
            className={`px-3 py-1 rounded-full text-sm font-medium ${
              testingState.sessionStatus === "CONNECTED"
                ? "bg-green-100 text-green-800"
                : testingState.sessionStatus === "CONNECTING"
                ? "bg-yellow-100 text-yellow-800"
                : "bg-gray-100 text-gray-800"
            }`}
          >
            {testingState.sessionStatus}
          </div>
          {testingState.isRecording && (
            <div className="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 flex items-center gap-1">
              <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
              VAD Active
            </div>
          )}
        </div>
      </div>

      {/* Error Display */}
      {testingState.error && (
        <div className="mx-5 mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">{testingState.error}</p>
        </div>
      )}

      {/* Main Content */}
      <div className="flex flex-1 gap-4 p-4 overflow-hidden">
        <div
          className={`${
            isEventsPaneExpanded ? "w-2/3" : "w-full"
          } transition-all duration-200`}
        >
          <Transcript
            userText={userText}
            setUserText={setUserText}
            onSendMessage={handleSendMessage}
            onPlayAudio={voiceTestControls.playAudio}
            canSend={testingState.sessionStatus === "CONNECTED"}
          />
        </div>

        <Events isExpanded={isEventsPaneExpanded} />
      </div>

      {/* Bottom Toolbar */}
      <BottomToolbar
        sessionStatus={testingState.sessionStatus}
        onToggleConnection={voiceTestControls.toggleConnection}
        onStartTest={voiceTestControls.startTest}
        onSwitchProvider={voiceTestControls.switchProvider}
        isRecording={testingState.isRecording}
        onToggleRecording={voiceTestControls.toggleRecording}
        isEventsPaneExpanded={isEventsPaneExpanded}
        setIsEventsPaneExpanded={setIsEventsPaneExpanded}
        provider={testingState.currentProvider}
        onProviderChange={handleProviderChange}
        disabled={testingState.isLoading}
      />
    </div>
  );
}

export default function TestingPage() {
  return (
    <TestingProvider>
      <TranscriptProvider>
        <EventProvider>
          <TestingPageContent />
        </EventProvider>
      </TranscriptProvider>
    </TestingProvider>
  );
}
