import { useState, useEffect } from "react";
import { useNavigate, useParams, Link } from "react-router-dom";
import { getAgent, updateAgent } from "../services/agents";
import { getCalendarConnections } from "../services/calendar";
import type { CalendarConnection } from "../services/calendar";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

interface Agent {
  id: string;
  name: string;
  prompt: string;
  twilioPhoneNumber: string;
  isActive: boolean;
  googleCalendarId: string | null;
  createdAt: string;
  updatedAt: string;
}

interface FormData {
  name: string;
  prompt: string;
  twilioPhoneNumber: string;
  isActive: boolean;
  googleCalendarId: string;
}

const EditAgent = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [agent, setAgent] = useState<Agent | null>(null);
  const [formData, setFormData] = useState<FormData>({
    name: "",
    prompt: "",
    twilioPhoneNumber: "",
    isActive: true,
    googleCalendarId: "",
  });
  const [calendarConnections, setCalendarConnections] = useState<
    CalendarConnection[]
  >([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingCalendars, setIsLoadingCalendars] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (!id) {
        setError("Agent ID is required");
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // Fetch agent data and calendar connections in parallel
        const [agentData, calendarData] = await Promise.all([
          getAgent(id),
          getCalendarConnections(),
        ]);

        setAgent(agentData);
        setFormData({
          name: agentData.name,
          prompt: agentData.prompt,
          twilioPhoneNumber: agentData.twilioPhoneNumber,
          isActive: agentData.isActive,
          googleCalendarId: agentData.googleCalendarId || "",
        });
        setCalendarConnections(calendarData);
        setIsLoadingCalendars(false);
      } catch (err) {
        console.error("Error fetching agent data:", err);
        setError("Failed to load agent data. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [id]);

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value, type } = e.target;

    if (type === "checkbox") {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData((prev) => ({ ...prev, [name]: checked }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!id) return;

    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      // Update the agent
      await updateAgent(id, formData);
      setSuccess("Agent updated successfully!");

      // Navigate back to agent detail page after a short delay
      setTimeout(() => {
        navigate(`/agents/${id}`);
      }, 1500);
    } catch (err) {
      console.error("Error updating agent:", err);
      setError("Failed to update agent. Please try again later.");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="text-center py-10">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-primary border-t-transparent"></div>
        <p className="mt-2 text-muted-foreground">Loading agent data...</p>
      </div>
    );
  }

  if (!agent) {
    return (
      <div className="text-center py-10">
        <h3 className="text-lg font-medium text-gray-900">Agent not found</h3>
        <p className="mt-1 text-gray-500">
          The agent you're looking for doesn't exist or you don't have
          permission to edit it.
        </p>
        <div className="mt-6">
          <Link
            to="/agents"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
          >
            Back to Agents
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold">Edit Agent</h1>
            <p className="mt-1 text-sm text-muted-foreground">
              Update your AI phone agent settings
            </p>
          </div>
          <Button variant="outline" asChild>
            <Link to={`/agents/${id}`}>Cancel</Link>
          </Button>
        </div>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-destructive/10 border border-destructive/20 rounded-md flex items-center gap-2">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-destructive"
          >
            <circle cx="12" cy="12" r="10" />
            <line x1="12" y1="8" x2="12" y2="12" />
            <line x1="12" y1="16" x2="12.01" y2="16" />
          </svg>
          <p className="text-sm text-destructive font-medium">{error}</p>
        </div>
      )}

      {success && (
        <div className="mb-4 p-4 bg-green-100 border border-green-200 rounded-md flex items-center gap-2">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-green-600"
          >
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
            <polyline points="22 4 12 14.01 9 11.01" />
          </svg>
          <p className="text-sm text-green-600 font-medium">{success}</p>
        </div>
      )}

      <div className="bg-card shadow-sm overflow-hidden rounded-lg border">
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div className="space-y-2">
            <Label htmlFor="name">Agent Name</Label>
            <Input
              type="text"
              name="name"
              id="name"
              value={formData.name}
              onChange={handleChange}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="prompt">Agent Prompt</Label>
            <Textarea
              name="prompt"
              id="prompt"
              rows={5}
              value={formData.prompt}
              onChange={handleChange}
              required
              placeholder="You are a helpful receptionist for a dental clinic. Your job is to answer calls, provide information about services, and book appointments. Be friendly and professional."
            />
            <p className="text-sm text-muted-foreground">
              This is the instruction given to the AI to define its role and
              behavior.
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="twilioPhoneNumber">Phone Number</Label>
            <Input
              type="text"
              name="twilioPhoneNumber"
              id="twilioPhoneNumber"
              value={formData.twilioPhoneNumber}
              onChange={handleChange}
              placeholder="+1234567890"
            />
            <p className="text-sm text-muted-foreground">
              The Twilio phone number assigned to this agent. Leave empty if not
              configured yet.
            </p>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="googleCalendarId">Google Calendar</Label>
              {calendarConnections.length > 0 && (
                <span className="text-xs text-green-600 font-medium">
                  ✓ {calendarConnections.length} calendar{calendarConnections.length > 1 ? 's' : ''} connected
                </span>
              )}
            </div>

            {isLoadingCalendars ? (
              <div className="flex items-center space-x-2 p-3 border rounded-md bg-gray-50">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-primary border-t-transparent"></div>
                <span className="text-sm text-muted-foreground">Loading calendars...</span>
              </div>
            ) : calendarConnections.length === 0 ? (
              <div className="p-4 border rounded-md bg-yellow-50 border-yellow-200">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-sm font-medium text-yellow-800">
                      No calendars connected
                    </h3>
                    <p className="text-sm text-yellow-700 mt-1">
                      This agent won't be able to book appointments without a connected calendar.
                    </p>
                    <div className="mt-3">
                      <Button variant="outline" size="sm" asChild>
                        <Link to="/settings" className="text-yellow-800 border-yellow-300 hover:bg-yellow-100">
                          Connect Calendar in Settings
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-2">
                <Select
                  value={formData.googleCalendarId || "none"}
                  onValueChange={(value) => {
                    const calendarId = value === "none" ? "" : value;
                    setFormData(prev => ({ ...prev, googleCalendarId: calendarId }));
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="No calendar selected" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">No calendar selected</SelectItem>
                    {calendarConnections.map((calendar) => (
                      <SelectItem key={calendar.id} value={calendar.calendarId}>
                        {calendar.calendarName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <div className="flex items-center justify-between">
                  <p className="text-sm text-muted-foreground">
                    {formData.googleCalendarId
                      ? "✅ Appointment booking enabled - events will be created in the selected calendar."
                      : "⚠️ Appointment booking disabled - select a calendar to enable this feature."
                    }
                  </p>
                  <Button variant="ghost" size="sm" asChild>
                    <Link to="/settings" className="text-xs text-primary hover:underline">
                      Manage Calendars
                    </Link>
                  </Button>
                </div>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="isActive"
              name="isActive"
              checked={formData.isActive}
              onCheckedChange={(checked) =>
                setFormData((prev) => ({ ...prev, isActive: checked === true }))
              }
            />
            <div>
              <Label htmlFor="isActive" className="text-sm font-medium">
                Active
              </Label>
              <p className="text-sm text-muted-foreground">
                When active, the agent will answer calls to its assigned phone
                number.
              </p>
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            <Button variant="outline" asChild>
              <Link to={`/agents/${id}`}>Cancel</Link>
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Updating..." : "Update Agent"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditAgent;
