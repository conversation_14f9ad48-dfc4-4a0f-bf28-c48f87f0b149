import { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { Phone, Clock, User, ArrowLeft } from "lucide-react";
import { getCall } from "../services/calls";
import type { Call } from "../services/calls";

const CallDetails: React.FC = () => {
  const { callId } = useParams<{ callId: string }>();
  const navigate = useNavigate();
  const [call, setCall] = useState<Call | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<
    "transcript" | "extracted" | "analysis"
  >("transcript");
  const [editingExtraction, setEditingExtraction] = useState<string | null>(
    null
  );

  useEffect(() => {
    const fetchCallDetails = async () => {
      try {
        if (!callId) return;
        const callData = await getCall(callId);
        setCall(callData);
      } catch (error) {
        console.error("Error fetching call details:", error);
      } finally {
        setLoading(false);
      }
    };

    if (callId) {
      fetchCallDetails();
    }
  }, [callId]);

  const formatDuration = (seconds?: number) => {
    if (!seconds) return "N/A";
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  const getSpeakerIcon = (speaker: string) => {
    switch (speaker) {
      case "user":
        return <User className="h-4 w-4" />;
      case "agent":
        return <User className="h-4 w-4" />;
      case "system":
        return <User className="h-4 w-4" />;
      default:
        return null;
    }
  };

  const getSpeakerColor = (speaker: string) => {
    switch (speaker) {
      case "user":
        return "text-blue-600 bg-blue-50";
      case "agent":
        return "text-green-600 bg-green-50";
      case "system":
        return "text-gray-600 bg-gray-50";
      default:
        return "text-gray-600 bg-gray-50";
    }
  };

  const getExtractionTypeIcon = (type: string) => {
    switch (type) {
      case "contact_info":
        return <User className="h-4 w-4" />;
      case "appointment":
        return <Clock className="h-4 w-4" />;
      case "problem_description":
        return <User className="h-4 w-4" />;
      case "action_items":
        return <User className="h-4 w-4" />;
      default:
        return <User className="h-4 w-4" />;
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return "text-green-600";
    if (confidence >= 0.6) return "text-yellow-600";
    return "text-red-600";
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-indigo-500 border-t-transparent"></div>
          <p className="mt-2 text-gray-500">Loading call details...</p>
        </div>
      </div>
    );
  }

  if (!call) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <p className="text-gray-500">Call not found</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => navigate('/calls')}
            className="flex items-center text-indigo-600 hover:text-indigo-800 mb-4 transition-colors"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Call History
          </button>

          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Call with {call.callerName || call.callerNumber || "Unknown"}
              </h1>
              <div className="mt-2 flex items-center gap-4 text-sm text-gray-500">
                <span className="flex items-center gap-1">
                  <Phone className="h-4 w-4" />
                  {call.type} call
                </span>
                <span className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  {formatDuration(call.duration)}
                </span>
                <span>Agent: {call.agent.name}</span>
                <span>Provider: {call.provider}</span>
                <span>Status: {call.status}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <User className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Messages
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {call.transcripts?.length || 0}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <User className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Extracted Items
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {call.extractedInformation?.length || 0}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <User className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Sentiment
                    </dt>
                    <dd className="text-lg font-medium text-gray-900 capitalize">
                      {call.analysis?.sentiment?.overall || "N/A"}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <User className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Outcome
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {call.analysis?.outcome?.replace("_", " ") || "N/A"}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {(
                [
                  {
                    id: "transcript" as const,
                    name: "Transcript",
                    icon: User,
                  },
                  {
                    id: "extracted" as const,
                    name: "Extracted Information",
                    icon: User,
                  },
                  { id: "analysis" as const, name: "Analysis", icon: User },
                ] as const
              ).map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`${
                    activeTab === tab.id
                      ? "border-indigo-500 text-indigo-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2`}
                >
                  <tab.icon className="h-4 w-4" />
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        <div className="bg-white shadow rounded-lg">
          {activeTab === "transcript" && (
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Call Transcript
              </h3>
              <div className="space-y-4">
                {(call.transcripts || []).map((transcript) => (
                  <div
                    key={transcript.id}
                    className={`flex gap-3 p-3 rounded-lg ${getSpeakerColor(
                      transcript.speaker
                    )}`}
                  >
                    <div className="flex-shrink-0 mt-1">
                      {getSpeakerIcon(transcript.speaker)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium capitalize">
                          {transcript.speaker}
                        </span>
                        <span className="text-xs text-gray-500">
                          {new Date(transcript.timestamp).toLocaleTimeString()}
                        </span>
                        {transcript.messageType !== "text" && (
                          <span className="text-xs bg-gray-200 text-gray-700 px-2 py-1 rounded">
                            {transcript.messageType}
                          </span>
                        )}
                      </div>
                      <p className="text-sm">{transcript.content}</p>
                      {transcript.metadata && (
                        <div className="mt-2 text-xs text-gray-600 bg-gray-100 p-2 rounded">
                          <pre>
                            {JSON.stringify(transcript.metadata, null, 2)}
                          </pre>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === "extracted" && (
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Extracted Information
              </h3>
              <div className="space-y-4">
                {(call.extractedInformation || []).map((item) => (
                  <div key={item.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3 flex-1">
                        <div className="flex-shrink-0 mt-1 text-indigo-600">
                          {getExtractionTypeIcon(item.extractionType)}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h4 className="font-medium text-gray-900">
                              {item.key.replace("_", " ")}
                            </h4>
                            <span className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
                              {item.extractionType}
                            </span>
                            <span
                              className={`text-xs font-medium ${getConfidenceColor(
                                item.confidence
                              )}`}
                            >
                              {Math.round(item.confidence * 100)}% confidence
                            </span>
                            {item.verified && (
                              <User className="h-4 w-4 text-green-500" />
                            )}
                          </div>
                          <p className="text-sm text-gray-900 mb-2">
                            <strong>Value:</strong>{" "}
                            {typeof item.value === "object"
                              ? JSON.stringify(item.value)
                              : item.value}
                          </p>
                          {item.context && (
                            <p className="text-xs text-gray-600">
                              <strong>Context:</strong> {item.context}
                            </p>
                          )}
                        </div>
                      </div>
                      <button
                        onClick={() =>
                          setEditingExtraction(
                            editingExtraction === item.id ? null : item.id
                          )
                        }
                        className="text-gray-400 hover:text-gray-600"
                      >
                        <User className="h-4 w-4" />
                      </button>
                    </div>
                    {editingExtraction === item.id && (
                      <div className="mt-4 pt-4 border-t">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-xs font-medium text-gray-700 mb-1">
                              Verified
                            </label>
                            <input
                              type="checkbox"
                              checked={item.verified}
                              className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                            />
                          </div>
                          <div>
                            <label className="block text-xs font-medium text-gray-700 mb-1">
                              Confidence
                            </label>
                            <input
                              type="number"
                              min="0"
                              max="1"
                              step="0.01"
                              value={item.confidence}
                              className="block w-full rounded-md border-gray-300 text-xs focus:border-indigo-500 focus:ring-indigo-500"
                            />
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === "analysis" && call.analysis && (
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-6">
                Call Analysis
              </h3>

              <div className="space-y-6">
                {/* Summary */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Summary</h4>
                  <p className="text-gray-700">
                    {call.analysis?.summary || "No summary available"}
                  </p>
                </div>

                {/* Key Takeaways */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">
                    Key Takeaways
                  </h4>
                  <p className="text-gray-700">
                    {call.analysis?.keyTakeaways ||
                      "No key takeaways available"}
                  </p>
                </div>

                {/* Action Items */}
                {(call.analysis?.actionItems?.length || 0) > 0 && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">
                      Action Items
                    </h4>
                    <ul className="list-disc list-inside space-y-1">
                      {(call.analysis?.actionItems || []).map((item, index) => (
                        <li key={index} className="text-gray-700">
                          {item}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Topics */}
                {(call.analysis?.topics?.length || 0) > 0 && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">
                      Topics Discussed
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {(call.analysis?.topics || []).map((topic, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-indigo-100 text-indigo-800"
                        >
                          {topic.topic} ({topic.mentions} mentions)
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Call Quality */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">
                    Call Quality Metrics
                  </h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center p-3 bg-gray-50 rounded">
                      <div className="text-lg font-semibold text-gray-900">
                        {Math.round(
                          (call.analysis?.callQuality?.audioQuality || 0) * 100
                        )}
                        %
                      </div>
                      <div className="text-xs text-gray-600">Audio Quality</div>
                    </div>
                    <div className="text-center p-3 bg-gray-50 rounded">
                      <div className="text-lg font-semibold text-gray-900">
                        {call.analysis?.callQuality?.interruptionCount || 0}
                      </div>
                      <div className="text-xs text-gray-600">Interruptions</div>
                    </div>
                    <div className="text-center p-3 bg-gray-50 rounded">
                      <div className="text-lg font-semibold text-gray-900">
                        {formatDuration(
                          call.analysis?.callQuality?.speakingTime?.user
                        )}
                      </div>
                      <div className="text-xs text-gray-600">User Speaking</div>
                    </div>
                    <div className="text-center p-3 bg-gray-50 rounded">
                      <div className="text-lg font-semibold text-gray-900">
                        {formatDuration(
                          call.analysis?.callQuality?.speakingTime?.agent
                        )}
                      </div>
                      <div className="text-xs text-gray-600">
                        Agent Speaking
                      </div>
                    </div>
                  </div>
                </div>

                {/* Recommendations */}
                {(call.analysis?.recommendations?.length || 0) > 0 && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">
                      Recommendations
                    </h4>
                    <ul className="list-disc list-inside space-y-1">
                      {(call.analysis?.recommendations || []).map(
                        (rec, index) => (
                          <li key={index} className="text-gray-700">
                            {rec}
                          </li>
                        )
                      )}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CallDetails;
