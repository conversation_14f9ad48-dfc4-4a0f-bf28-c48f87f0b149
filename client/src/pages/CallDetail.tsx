import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, Link } from "react-router-dom";
import { Phone, Clock, Calendar, User } from "lucide-react";
import { format } from "date-fns";
import { getCall } from "../services/calls";

interface Call {
  id: string;
  twilioCallSid?: string;
  callerNumber?: string;
  callerName?: string;
  status: "ongoing" | "completed" | "failed" | "analyzing";
  duration?: number;
  recordingUrl?: string;
  transcriptionUrl?: string;
  agent: {
    id: string;
    name: string;
  };
  callData?: {
    id: string;
    customerInfo?: {
      name?: string;
      email?: string;
      phone?: string;
      [key: string]: string | undefined;
    };
    appointmentDetails?: {
      date?: string;
      service?: string;
      duration?: number;
      [key: string]: string | number | undefined;
    };
    requestDetails?: {
      type?: string;
      description?: string;
      urgency?: string;
      [key: string]: string | undefined;
    };
    summary?: string;
    googleCalendarEventId?: string;
  };
  createdAt: string;
  updatedAt: string;
}

const CallDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [call, setCall] = useState<Call | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCallDetails = async () => {
      if (!id) return;

      setIsLoading(true);
      setError(null);

      try {
        const callData = await getCall(id);
        setCall(callData);
      } catch (err) {
        console.error("Error fetching call details:", err);
        setError("Failed to load call details. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchCallDetails();
  }, [id]);

  if (isLoading) {
    return (
      <div className="text-center py-10">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-primary border-t-transparent"></div>
        <p className="mt-2 text-gray-500">Loading call details...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-10">
        <h3 className="text-lg font-medium text-destructive">Error</h3>
        <p className="mt-1 text-gray-500">{error}</p>
        <div className="mt-6">
          <Link
            to="/calls"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            Back to Calls
          </Link>
        </div>
      </div>
    );
  }

  if (!call) {
    return (
      <div className="text-center py-10">
        <h3 className="text-lg font-medium text-gray-900">Call not found</h3>
        <p className="mt-1 text-gray-500">
          The call you're looking for doesn't exist or has been deleted.
        </p>
        <div className="mt-6">
          <Link
            to="/calls"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            Back to Calls
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Call Details</h1>
          <p className="mt-1 text-sm text-gray-500">
            {format(new Date(call.createdAt), "MMMM d, yyyy")} at{" "}
            {format(new Date(call.createdAt), "h:mm a")}
          </p>
        </div>
        <Link
          to="/calls"
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Back to Calls
        </Link>
      </div>

      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Call Information
          </h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">
            Details about the call.
          </p>
        </div>
        <div className="border-t border-gray-200 px-4 py-5 sm:p-0">
          <dl className="sm:divide-y sm:divide-gray-200">
            <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Caller</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2 flex items-center">
                <User className="h-5 w-5 text-gray-400 mr-2" />
                {call.callerName
                  ? `${call.callerName} (${call.callerNumber})`
                  : call.callerNumber}
              </dd>
            </div>
            <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Agent</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2 flex items-center">
                <Phone className="h-5 w-5 text-gray-400 mr-2" />
                <Link
                  to={`/agents/${call.agent.id}`}
                  className="text-primary hover:text-primary/80"
                >
                  {call.agent.name}
                </Link>
              </dd>
            </div>
            <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Duration</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2 flex items-center">
                <Clock className="h-5 w-5 text-gray-400 mr-2" />
                {call.duration ? (
                  <>
                    {Math.floor(call.duration / 60)}:
                    {call.duration % 60 < 10 ? "0" : ""}
                    {call.duration % 60}
                  </>
                ) : (
                  "Not available"
                )}
              </dd>
            </div>
            <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Status</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                  {call.status}
                </span>
              </dd>
            </div>
            {call.recordingUrl && (
              <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Recording</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  <audio controls className="w-full">
                    <source src={call.recordingUrl} type="audio/mpeg" />
                    Your browser does not support the audio element.
                  </audio>
                </dd>
              </div>
            )}
          </dl>
        </div>
      </div>

      {call.callData && (
        <>
          {call.callData.summary && (
            <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
              <div className="px-4 py-5 sm:px-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Call Summary
                </h3>
              </div>
              <div className="border-t border-gray-200 px-4 py-5 sm:px-6">
                <p className="text-sm text-gray-900">{call.callData.summary}</p>
              </div>
            </div>
          )}

          {call.callData.customerInfo && (
            <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
              <div className="px-4 py-5 sm:px-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Customer Information
                </h3>
              </div>
              <div className="border-t border-gray-200 px-4 py-5 sm:p-0">
                <dl className="sm:divide-y sm:divide-gray-200">
                  {Object.entries(call.callData.customerInfo).map(
                    ([key, value]) => (
                      <div
                        key={key}
                        className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6"
                      >
                        <dt className="text-sm font-medium text-gray-500">
                          {key.charAt(0).toUpperCase() + key.slice(1)}
                        </dt>
                        <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                          {value}
                        </dd>
                      </div>
                    )
                  )}
                </dl>
              </div>
            </div>
          )}

          {call.callData.appointmentDetails && (
            <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
              <div className="px-4 py-5 sm:px-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Appointment Details
                </h3>
              </div>
              <div className="border-t border-gray-200 px-4 py-5 sm:p-0">
                <dl className="sm:divide-y sm:divide-gray-200">
                  {Object.entries(call.callData.appointmentDetails).map(
                    ([key, value]) => (
                      <div
                        key={key}
                        className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6"
                      >
                        <dt className="text-sm font-medium text-gray-500">
                          {key.charAt(0).toUpperCase() + key.slice(1)}
                        </dt>
                        <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                          {value}
                        </dd>
                      </div>
                    )
                  )}
                  {call.callData.googleCalendarEventId && (
                    <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                      <dt className="text-sm font-medium text-gray-500">
                        Calendar
                      </dt>
                      <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2 flex items-center">
                        <Calendar className="h-5 w-5 text-gray-400 mr-2" />
                        Added to Google Calendar
                      </dd>
                    </div>
                  )}
                </dl>
              </div>
            </div>
          )}

          {call.callData.requestDetails && (
            <div className="bg-white shadow overflow-hidden sm:rounded-lg">
              <div className="px-4 py-5 sm:px-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Request Details
                </h3>
              </div>
              <div className="border-t border-gray-200 px-4 py-5 sm:p-0">
                <dl className="sm:divide-y sm:divide-gray-200">
                  {Object.entries(call.callData.requestDetails).map(
                    ([key, value]) => (
                      <div
                        key={key}
                        className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6"
                      >
                        <dt className="text-sm font-medium text-gray-500">
                          {key.charAt(0).toUpperCase() + key.slice(1)}
                        </dt>
                        <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                          {value}
                        </dd>
                      </div>
                    )
                  )}
                </dl>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default CallDetail;
