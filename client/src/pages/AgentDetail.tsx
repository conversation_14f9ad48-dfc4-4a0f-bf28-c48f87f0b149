import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, Link } from "react-router-dom";
import {
  PhoneIcon,
  CalendarIcon,
  PencilIcon,
} from "@heroicons/react/24/outline";
import { getAgent } from "../services/agents";
import { getCalls } from "../services/calls";

interface Agent {
  id: string;
  name: string;
  prompt: string;
  twilioPhoneNumber: string;
  isActive: boolean;
  googleCalendarId: string | null;
  createdAt: string;
}

interface Call {
  id: string;
  twilioCallSid: string;
  callerNumber: string;
  callerName?: string;
  status: "ongoing" | "completed" | "failed";
  duration?: number;
  createdAt: string;
}

const AgentDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [agent, setAgent] = useState<Agent | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [recentCalls, setRecentCalls] = useState<Call[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAgentData = async () => {
      if (!id) return;

      setIsLoading(true);
      setError(null);

      try {
        // Fetch agent details
        const agentData = await getAgent(id);
        setAgent(agentData);

        // Fetch recent calls for this agent
        const callsData = await getCalls(id);
        setRecentCalls(callsData.slice(0, 3)); // Get only the 3 most recent calls
      } catch (err) {
        console.error("Error fetching agent details:", err);
        setError("Failed to load agent details. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchAgentData();
  }, [id]);

  if (isLoading) {
    return (
      <div className="text-center py-10">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-indigo-500 border-t-transparent"></div>
        <p className="mt-2 text-gray-500">Loading agent details...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-10">
        <h3 className="text-lg font-medium text-red-600">Error</h3>
        <p className="mt-1 text-gray-500">{error}</p>
        <div className="mt-6">
          <Link
            to="/agents"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Back to Agents
          </Link>
        </div>
      </div>
    );
  }

  if (!agent) {
    return (
      <div className="text-center py-10">
        <h3 className="text-lg font-medium text-gray-900">Agent not found</h3>
        <p className="mt-1 text-gray-500">
          The agent you're looking for doesn't exist or has been deleted.
        </p>
        <div className="mt-6">
          <Link
            to="/agents"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Back to Agents
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">{agent.name}</h1>
          <p className="mt-1 text-sm text-gray-500">
            Agent details and recent calls
          </p>
        </div>
        <Link
          to={`/agents/${agent.id}/edit`}
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <PencilIcon
            className="-ml-1 mr-2 h-5 w-5 text-gray-500"
            aria-hidden="true"
          />
          Edit Agent
        </Link>
      </div>

      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Agent Information
          </h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">
            Details and configuration.
          </p>
        </div>
        <div className="border-t border-gray-200 px-4 py-5 sm:p-0">
          <dl className="sm:divide-y sm:divide-gray-200">
            <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Status</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {agent.isActive ? (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Active
                  </span>
                ) : (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    Inactive
                  </span>
                )}
              </dd>
            </div>
            <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">
                Phone number
              </dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2 flex items-center">
                <PhoneIcon className="h-5 w-5 text-gray-400 mr-2" />
                {agent.twilioPhoneNumber}
              </dd>
            </div>
            <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">
                Google Calendar
              </dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2 flex items-center">
                <CalendarIcon className="h-5 w-5 text-gray-400 mr-2" />
                {agent.googleCalendarId ? (
                  <span>Connected</span>
                ) : (
                  <span className="text-gray-500">Not connected</span>
                )}
              </dd>
            </div>
            <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Prompt</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                <div className="bg-gray-50 p-3 rounded-md">{agent.prompt}</div>
              </dd>
            </div>
          </dl>
        </div>
      </div>

      <div className="mt-8">
        <h2 className="text-lg font-medium text-gray-900">Recent Calls</h2>
        <div className="mt-4 bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {recentCalls.length > 0 ? (
              recentCalls.map((call) => (
                <li key={call.id}>
                  <Link
                    to={`/calls/${call.id}`}
                    className="block hover:bg-gray-50"
                  >
                    <div className="px-4 py-4 sm:px-6">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-indigo-600 truncate">
                          Call from{" "}
                          {call.callerName
                            ? `${call.callerName} (${call.callerNumber})`
                            : call.callerNumber}
                        </p>
                        <div className="ml-2 flex-shrink-0 flex">
                          <p className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                            {call.status}
                          </p>
                        </div>
                      </div>
                      <div className="mt-2 sm:flex sm:justify-between">
                        <div className="sm:flex">
                          {call.duration && (
                            <p className="flex items-center text-sm text-gray-500">
                              Duration: {Math.floor(call.duration / 60)}:
                              {call.duration % 60 < 10 ? "0" : ""}
                              {call.duration % 60}
                            </p>
                          )}
                        </div>
                        <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                          <p>
                            {new Date(call.createdAt).toLocaleDateString()} at{" "}
                            {new Date(call.createdAt).toLocaleTimeString()}
                          </p>
                        </div>
                      </div>
                    </div>
                  </Link>
                </li>
              ))
            ) : (
              <li className="px-4 py-5 text-center text-sm text-gray-500">
                No calls yet for this agent.
              </li>
            )}
          </ul>
          {recentCalls.length > 0 && (
            <div className="bg-gray-50 px-4 py-3 text-right sm:px-6">
              <Link
                to={`/calls?agentId=${agent.id}`}
                className="text-sm font-medium text-indigo-600 hover:text-indigo-500"
              >
                View all calls
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AgentDetail;
