import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { getDashboardStats, getRecentActivity } from "../services/stats";
import type { DashboardStats, RecentActivity } from "../services/stats";
import LoadingSpinner from "../components/ui/loading-spinner";

const Dashboard = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalAgents: 0,
    totalCalls: 0,
    callsToday: 0,
    appointmentsBooked: 0,
  });
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Fetch dashboard stats
        const statsData = await getDashboardStats();
        setStats(statsData);

        // Fetch recent activity
        const activityData = await getRecentActivity(3);
        setRecentActivity(activityData);
      } catch (err) {
        console.error("Error fetching dashboard data:", err);
        // Use a more helpful error message for new users
        setError("Failed to load dashboard data. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-20">
        <LoadingSpinner
          size="lg"
          variant="orbit"
          text="Loading dashboard data..."
        />
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-gray-900">Dashboard</h1>
        <p className="mt-1 text-sm text-gray-500">
          Overview of your AI phone agents and call activity
        </p>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      {/* Welcome message for new users */}
      {stats.totalAgents === 0 && !error && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
          <h3 className="text-md font-medium text-blue-800">
            Welcome to Talkloop!
          </h3>
          <p className="mt-1 text-sm text-blue-600">
            To get started, create your first AI phone agent by clicking the
            "Create Agent" button below.
          </p>
          <div className="mt-3">
            <Link
              to="/agents/new"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Create Agent
            </Link>
          </div>
        </div>
      )}

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <dt className="text-sm font-medium text-gray-500 truncate">
              Total Agents
            </dt>
            <dd className="mt-1 text-3xl font-semibold text-gray-900">
              {stats.totalAgents}
            </dd>
          </div>
          <div className="bg-gray-50 px-4 py-4 sm:px-6">
            <div className="text-sm">
              <Link
                to="/agents"
                className="font-medium text-indigo-600 hover:text-indigo-500"
              >
                View all agents
              </Link>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <dt className="text-sm font-medium text-gray-500 truncate">
              Total Calls
            </dt>
            <dd className="mt-1 text-3xl font-semibold text-gray-900">
              {stats.totalCalls}
            </dd>
          </div>
          <div className="bg-gray-50 px-4 py-4 sm:px-6">
            <div className="text-sm">
              <Link
                to="/calls"
                className="font-medium text-indigo-600 hover:text-indigo-500"
              >
                View all calls
              </Link>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <dt className="text-sm font-medium text-gray-500 truncate">
              Calls Today
            </dt>
            <dd className="mt-1 text-3xl font-semibold text-gray-900">
              {stats.callsToday}
            </dd>
          </div>
          <div className="bg-gray-50 px-4 py-4 sm:px-6">
            <div className="text-sm">
              <Link
                to="/calls"
                className="font-medium text-indigo-600 hover:text-indigo-500"
              >
                View today's calls
              </Link>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <dt className="text-sm font-medium text-gray-500 truncate">
              Appointments Booked
            </dt>
            <dd className="mt-1 text-3xl font-semibold text-gray-900">
              {stats.appointmentsBooked}
            </dd>
          </div>
          <div className="bg-gray-50 px-4 py-4 sm:px-6">
            <div className="text-sm">
              <a
                href="#"
                className="font-medium text-indigo-600 hover:text-indigo-500"
              >
                View calendar
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Recent activity */}
      <div className="mt-8">
        <h2 className="text-lg font-medium text-gray-900">Recent Activity</h2>
        <div className="mt-4 bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {recentActivity.length > 0 ? (
              recentActivity.map((activity) => (
                <li key={activity.id}>
                  <div className="px-4 py-4 sm:px-6">
                    <p className="text-sm font-medium text-indigo-600 truncate">
                      {activity.title}
                    </p>
                    <p className="mt-1 text-sm text-gray-500">
                      {activity.description} •{" "}
                      {new Date(activity.timestamp).toLocaleString()}
                    </p>
                  </div>
                </li>
              ))
            ) : (
              <li>
                <div className="px-4 py-4 sm:px-6">
                  <p className="text-sm text-gray-500 text-center">
                    No recent activity
                  </p>
                </div>
              </li>
            )}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
