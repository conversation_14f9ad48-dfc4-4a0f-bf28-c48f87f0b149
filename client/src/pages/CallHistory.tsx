import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import {
  PhoneIcon,
  ClockIcon,
  UserIcon,
  ChartBarIcon,
  EyeIcon,
  FunnelIcon,
} from "@heroicons/react/24/outline";
import { getCalls } from "../services/calls";
import type { Call } from "../services/calls";

const CallHistory: React.FC = () => {
  const [calls, setCalls] = useState<Call[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<"all" | "phone" | "test">("all");
  const [statusFilter, setStatusFilter] = useState<
    "all" | "completed" | "ongoing"
  >("all");

  useEffect(() => {
    const fetchCalls = async () => {
      try {
        const callsData = await getCalls();
        setCalls(callsData);
      } catch (error) {
        console.error("Error fetching calls:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchCalls();
  }, []);

  const filteredCalls = calls.filter((call) => {
    if (filter !== "all" && call.type !== filter) return false;
    if (statusFilter !== "all" && call.status !== statusFilter) return false;
    return true;
  });

  const formatDuration = (seconds?: number) => {
    if (!seconds) return "N/A";
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "ongoing":
        return "bg-blue-100 text-blue-800";
      case "failed":
        return "bg-red-100 text-red-800";
      case "analyzing":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getSentimentColor = (sentiment?: string) => {
    switch (sentiment) {
      case "positive":
        return "text-green-600";
      case "negative":
        return "text-red-600";
      case "neutral":
        return "text-gray-600";
      default:
        return "text-gray-400";
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-indigo-500 border-t-transparent"></div>
          <p className="mt-2 text-gray-500">Loading call history...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Call History</h1>
          <p className="mt-2 text-gray-600">
            View and analyze your call recordings, transcripts, and extracted
            information
          </p>
        </div>

        {/* Filters */}
        <div className="mb-6 flex flex-wrap gap-4">
          <div className="flex items-center gap-2">
            <FunnelIcon className="h-5 w-5 text-gray-400" />
            <span className="text-sm font-medium text-gray-700">
              Filter by type:
            </span>
            <select
              value={filter}
              onChange={(e) =>
                setFilter(e.target.value as "all" | "phone" | "test")
              }
              className="rounded-md border-gray-300 text-sm focus:border-indigo-500 focus:ring-indigo-500"
            >
              <option value="all">All Calls</option>
              <option value="phone">Phone Calls</option>
              <option value="test">Test Calls</option>
            </select>
          </div>

          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-700">Status:</span>
            <select
              value={statusFilter}
              onChange={(e) =>
                setStatusFilter(
                  e.target.value as "all" | "completed" | "ongoing"
                )
              }
              className="rounded-md border-gray-300 text-sm focus:border-indigo-500 focus:ring-indigo-500"
            >
              <option value="all">All Status</option>
              <option value="completed">Completed</option>
              <option value="ongoing">Ongoing</option>
            </select>
          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <PhoneIcon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Total Calls
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {calls.length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ClockIcon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Avg Duration
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {formatDuration(
                        Math.round(
                          calls.reduce(
                            (sum, call) => sum + (call.duration || 0),
                            0
                          ) / calls.length
                        )
                      )}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <UserIcon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Completed
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {calls.filter((c) => c.status === "completed").length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ChartBarIcon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Analyzed
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {calls.filter((c) => c.analysis).length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Call List */}
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {filteredCalls.map((call) => (
              <li key={call.id}>
                <Link
                  to={`/calls/${call.id}`}
                  className="block hover:bg-gray-50 px-4 py-4 sm:px-6"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <PhoneIcon className="h-6 w-6 text-gray-400" />
                      </div>
                      <div className="ml-4">
                        <div className="flex items-center">
                          <p className="text-sm font-medium text-indigo-600">
                            {call.callerName ||
                              call.callerNumber ||
                              `${call.type} call`}
                          </p>
                          <span
                            className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                              call.status
                            )}`}
                          >
                            {call.status}
                          </span>
                          {call.analysis?.sentiment && (
                            <span
                              className={`ml-2 text-xs ${getSentimentColor(
                                call.analysis.sentiment.overall
                              )}`}
                            >
                              {call.analysis.sentiment.overall}
                            </span>
                          )}
                        </div>
                        <div className="mt-1 flex items-center gap-4 text-sm text-gray-500">
                          <span>Agent: {call.agent.name}</span>
                          <span>Duration: {formatDuration(call.duration)}</span>
                          <span>Provider: {call.provider || "N/A"}</span>
                          <span>
                            {call.extractedInformation?.length || 0} items
                            extracted
                          </span>
                        </div>
                        {call.analysis?.summary && (
                          <p className="mt-1 text-sm text-gray-600 truncate max-w-2xl">
                            {call.analysis.summary}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center">
                      <div className="text-right text-sm text-gray-500">
                        <p>{new Date(call.createdAt).toLocaleDateString()}</p>
                        <p>{new Date(call.createdAt).toLocaleTimeString()}</p>
                      </div>
                      <EyeIcon className="ml-4 h-5 w-5 text-gray-400" />
                    </div>
                  </div>
                </Link>
              </li>
            ))}
          </ul>

          {filteredCalls.length === 0 && (
            <div className="text-center py-12">
              <PhoneIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                No calls found
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                No calls match your current filters.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CallHistory;
