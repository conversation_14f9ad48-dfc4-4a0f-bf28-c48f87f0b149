import React, { useState } from 'react';
import LoadingSpinner from '../components/ui/loading-spinner';
import AuthLoading from '../components/ui/auth-loading';
import { Button } from '../components/ui/button';

const LoadingTest: React.FC = () => {
  const [showAuthLoading, setShowAuthLoading] = useState(false);

  const spinnerVariants = ['default', 'dots', 'pulse', 'bars', 'orbit'] as const;
  const sizes = ['sm', 'md', 'lg', 'xl'] as const;

  return (
    <div className="p-8 space-y-12">
      <div>
        <h1 className="text-3xl font-bold mb-6">Loading Components Test</h1>
        <p className="text-muted-foreground mb-8">
          Test all the different loading states and animations
        </p>
      </div>

      {/* Auth Loading Test */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Auth Loading (Full Screen)</h2>
        <Button 
          onClick={() => setShowAuthLoading(true)}
          className="bg-primary hover:bg-primary/90"
        >
          Show Auth Loading Screen
        </Button>
        {showAuthLoading && (
          <div className="relative">
            <AuthLoading />
            <Button 
              onClick={() => setShowAuthLoading(false)}
              className="fixed top-4 right-4 z-[60] bg-red-500 hover:bg-red-600"
            >
              Close
            </Button>
          </div>
        )}
      </div>

      {/* Spinner Variants */}
      <div className="space-y-6">
        <h2 className="text-2xl font-semibold">Spinner Variants</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {spinnerVariants.map((variant) => (
            <div key={variant} className="p-6 border rounded-lg space-y-4">
              <h3 className="text-lg font-medium capitalize">{variant}</h3>
              <div className="flex items-center justify-center h-24">
                <LoadingSpinner variant={variant} size="lg" />
              </div>
              <div className="text-center">
                <LoadingSpinner 
                  variant={variant} 
                  size="md" 
                  text={`Loading with ${variant}...`} 
                />
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Size Variations */}
      <div className="space-y-6">
        <h2 className="text-2xl font-semibold">Size Variations</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {sizes.map((size) => (
            <div key={size} className="p-4 border rounded-lg text-center space-y-3">
              <h3 className="text-sm font-medium uppercase">{size}</h3>
              <div className="flex justify-center">
                <LoadingSpinner variant="orbit" size={size} />
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* In-Context Examples */}
      <div className="space-y-6">
        <h2 className="text-2xl font-semibold">In-Context Examples</h2>
        
        {/* Button Loading */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Button Loading States</h3>
          <div className="flex space-x-4">
            <Button disabled className="flex items-center space-x-2">
              <LoadingSpinner size="sm" variant="dots" />
              <span>Signing in...</span>
            </Button>
            <Button disabled className="flex items-center space-x-2">
              <LoadingSpinner size="sm" variant="default" />
              <span>Processing...</span>
            </Button>
            <Button disabled className="flex items-center space-x-2">
              <LoadingSpinner size="sm" variant="pulse" />
              <span>Saving...</span>
            </Button>
          </div>
        </div>

        {/* Card Loading */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Card Loading State</h3>
          <div className="bg-white border rounded-lg p-6 max-w-md">
            <div className="flex items-center justify-center py-8">
              <LoadingSpinner 
                size="lg" 
                variant="orbit" 
                text="Loading dashboard data..." 
              />
            </div>
          </div>
        </div>

        {/* Inline Loading */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Inline Loading</h3>
          <div className="space-y-2">
            <p className="flex items-center space-x-2">
              <LoadingSpinner size="sm" variant="dots" />
              <span>Fetching user data...</span>
            </p>
            <p className="flex items-center space-x-2">
              <LoadingSpinner size="sm" variant="bars" />
              <span>Syncing with server...</span>
            </p>
            <p className="flex items-center space-x-2">
              <LoadingSpinner size="sm" variant="pulse" />
              <span>Updating preferences...</span>
            </p>
          </div>
        </div>
      </div>

      {/* Performance Note */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-lg font-medium text-blue-800 mb-2">Performance Note</h3>
        <p className="text-blue-700 text-sm">
          All animations use CSS transforms and opacity for optimal performance. 
          The auth loading screen includes backdrop blur and multiple animation layers 
          for a premium feel while maintaining 60fps performance.
        </p>
      </div>
    </div>
  );
};

export default LoadingTest;
