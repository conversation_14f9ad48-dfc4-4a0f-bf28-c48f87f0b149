import { useState, useEffect } from "react";
import type { FormEvent } from "react";
import { Dialog } from "@headlessui/react";
import { ExclamationTriangleIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { getProfile, updateProfile, changePassword } from "../services/auth";
import {
  getCalendarConnections,
  getAuthUrl,
  disconnectCalendar,
} from "../services/calendar";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  companyName?: string;
  phoneNumber?: string;
}

interface CalendarConnection {
  id: string;
  calendarId: string;
  calendarName: string;
  isActive: boolean;
  createdAt: string;
}

interface PasswordFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

const Settings = () => {
  const [user, setUser] = useState<User | null>(null);
  const [calendarConnections, setCalendarConnections] = useState<
    CalendarConnection[]
  >([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingCalendars, setIsLoadingCalendars] = useState(false);
  const [activeTab, setActiveTab] = useState("profile"); // profile, calendar, security
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [passwordFormData, setPasswordFormData] = useState<PasswordFormData>({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });
  const [disconnectDialog, setDisconnectDialog] = useState<{
    isOpen: boolean;
    connectionId: string | null;
    connectionName: string | null;
  }>({
    isOpen: false,
    connectionId: null,
    connectionName: null,
  });

  // Function to fetch calendar connections (can be called multiple times)
  const fetchCalendarConnections = async () => {
    setIsLoadingCalendars(true);
    try {
      console.log('Fetching calendar connections...');
      const connections = await getCalendarConnections();
      console.log('Calendar connections fetched:', connections);
      setCalendarConnections(connections);
    } catch (err) {
      console.error("Error fetching calendar connections:", err);
    } finally {
      setIsLoadingCalendars(false);
    }
  };

  useEffect(() => {
    // Fetch user profile and calendar connections
    const fetchUserProfile = async () => {
      try {
        const userData = await getProfile();
        setUser(userData);
        setIsLoading(false);
      } catch (err) {
        console.error("Error fetching user profile:", err);
        setError("Failed to load user profile. Please try again later.");
        setIsLoading(false);
      }
    };

    fetchUserProfile();
    fetchCalendarConnections();
  }, []);

  // Listen for window focus to refresh calendar connections
  // This will trigger when user returns from OAuth flow
  useEffect(() => {
    const handleWindowFocus = () => {
      console.log('Window focused, refreshing calendar connections...');
      fetchCalendarConnections();
    };

    window.addEventListener('focus', handleWindowFocus);
    return () => window.removeEventListener('focus', handleWindowFocus);
  }, []);

  const handleProfileSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);

    if (!user) return;

    try {
      const formElement = e.target as HTMLFormElement;
      const formData = new FormData(formElement);

      const updatedUser = {
        firstName: formData.get("firstName") as string,
        lastName: formData.get("lastName") as string,
        email: formData.get("email") as string,
        companyName: formData.get("companyName") as string,
        phoneNumber: formData.get("phoneNumber") as string,
      };

      await updateProfile(updatedUser);
      setUser({ ...user, ...updatedUser });
      setSuccess("Profile updated successfully!");
    } catch (err) {
      console.error("Error updating profile:", err);
      setError("Failed to update profile. Please try again.");
    }
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswordFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handlePasswordSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);

    if (passwordFormData.newPassword !== passwordFormData.confirmPassword) {
      setError("New password and confirmation do not match");
      return;
    }

    try {
      await changePassword({
        currentPassword: passwordFormData.currentPassword,
        newPassword: passwordFormData.newPassword,
      });

      setSuccess("Password changed successfully!");
      setPasswordFormData({
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      });
    } catch (err) {
      console.error("Error changing password:", err);
      setError(
        "Failed to change password. Please check your current password and try again."
      );
    }
  };

  const handleConnectCalendar = async () => {
    try {
      const { url } = await getAuthUrl();
      window.open(url, "_blank");
    } catch (err) {
      console.error("Error getting auth URL:", err);
      setError("Failed to connect to Google Calendar. Please try again later.");
    }
  };

  const handleDisconnectCalendar = (id: string, name: string) => {
    setDisconnectDialog({
      isOpen: true,
      connectionId: id,
      connectionName: name,
    });
  };

  const confirmDisconnectCalendar = async () => {
    if (!disconnectDialog.connectionId) return;

    try {
      await disconnectCalendar(disconnectDialog.connectionId);

      // Remove the disconnected calendar from the UI
      const updatedConnections = calendarConnections.filter(
        (conn) => conn.id !== disconnectDialog.connectionId
      );
      setCalendarConnections(updatedConnections);

      // Show appropriate success message
      const calendarName = disconnectDialog.connectionName || "Calendar";
      setSuccess(`${calendarName} has been disconnected. You can reconnect it anytime.`);

      setDisconnectDialog({ isOpen: false, connectionId: null, connectionName: null });
    } catch (err) {
      console.error("Error disconnecting calendar:", err);
      setError("Failed to disconnect calendar. Please try again later.");
      setDisconnectDialog({ isOpen: false, connectionId: null, connectionName: null });
    }
  };

  const cancelDisconnectCalendar = () => {
    setDisconnectDialog({ isOpen: false, connectionId: null, connectionName: null });
  };

  if (isLoading) {
    return (
      <div className="text-center py-10">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-primary border-t-transparent"></div>
        <p className="mt-2 text-muted-foreground">Loading settings...</p>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-semibold">Settings</h1>
        <p className="mt-1 text-sm text-muted-foreground">
          Manage your account settings and preferences
        </p>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-destructive/10 border border-destructive/20 rounded-md flex items-center gap-2">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-destructive"
          >
            <circle cx="12" cy="12" r="10" />
            <line x1="12" y1="8" x2="12" y2="12" />
            <line x1="12" y1="16" x2="12.01" y2="16" />
          </svg>
          <p className="text-sm text-destructive font-medium">{error}</p>
        </div>
      )}

      {success && (
        <div className="mb-4 p-4 bg-green-100 border border-green-200 rounded-md flex items-center gap-2">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-green-600"
          >
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
            <polyline points="22 4 12 14.01 9 11.01" />
          </svg>
          <p className="text-sm text-green-600 font-medium">{success}</p>
        </div>
      )}

      <div className="bg-card shadow-sm overflow-hidden rounded-lg border">
        <Tabs
          defaultValue={activeTab}
          onValueChange={setActiveTab}
          className="w-full"
        >
          <div className="border-b">
            <TabsList className="w-full justify-start rounded-none bg-transparent p-0">
              <TabsTrigger
                value="profile"
                className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent"
              >
                Profile
              </TabsTrigger>
              <TabsTrigger
                value="calendar"
                className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent"
              >
                Calendar
              </TabsTrigger>
              <TabsTrigger
                value="security"
                className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent"
              >
                Security
              </TabsTrigger>
            </TabsList>
          </div>

          <div className="px-4 py-5 sm:p-6">
            <TabsContent value="profile">
              {user && (
                <form onSubmit={handleProfileSubmit}>
                  <div className="space-y-6">
                    <div className="grid grid-cols-6 gap-6">
                      <div className="col-span-6 sm:col-span-3">
                        <div className="space-y-2">
                          <Label htmlFor="firstName">First name</Label>
                          <Input
                            type="text"
                            name="firstName"
                            id="firstName"
                            defaultValue={user.firstName}
                          />
                        </div>
                      </div>

                      <div className="col-span-6 sm:col-span-3">
                        <div className="space-y-2">
                          <Label htmlFor="lastName">Last name</Label>
                          <Input
                            type="text"
                            name="lastName"
                            id="lastName"
                            defaultValue={user.lastName}
                          />
                        </div>
                      </div>

                      <div className="col-span-6 sm:col-span-4">
                        <div className="space-y-2">
                          <Label htmlFor="email">Email address</Label>
                          <Input
                            type="email"
                            name="email"
                            id="email"
                            defaultValue={user.email}
                          />
                        </div>
                      </div>

                      <div className="col-span-6 sm:col-span-4">
                        <div className="space-y-2">
                          <Label htmlFor="companyName">Company name</Label>
                          <Input
                            type="text"
                            name="companyName"
                            id="companyName"
                            defaultValue={user.companyName || ""}
                          />
                        </div>
                      </div>

                      <div className="col-span-6 sm:col-span-4">
                        <div className="space-y-2">
                          <Label htmlFor="phoneNumber">Phone number</Label>
                          <Input
                            type="text"
                            name="phoneNumber"
                            id="phoneNumber"
                            defaultValue={user.phoneNumber || ""}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="flex justify-end">
                      <Button type="submit">Save</Button>
                    </div>
                  </div>
                </form>
              )}
            </TabsContent>

            <TabsContent value="calendar">
              <div>
                <div className="mb-5 flex justify-between items-start">
                  <div>
                    <h3 className="text-lg leading-6 font-medium">
                      Google Calendar Integration
                    </h3>
                    <p className="mt-1 text-sm text-muted-foreground">
                      Connect your Google Calendar to allow AI agents to book
                      appointments.
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={fetchCalendarConnections}
                    disabled={isLoadingCalendars}
                  >
                    {isLoadingCalendars ? "Refreshing..." : "Refresh"}
                  </Button>
                </div>

                {isLoadingCalendars ? (
                  <div className="text-center py-8">
                    <p className="text-sm text-muted-foreground">
                      Loading calendar connections...
                    </p>
                  </div>
                ) : calendarConnections.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                      <svg
                        className="w-8 h-8 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={1.5}
                          d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                        />
                      </svg>
                    </div>
                    <h3 className="text-sm font-medium text-gray-900 mb-2">
                      No calendars connected
                    </h3>
                    <p className="text-sm text-muted-foreground mb-6 max-w-sm mx-auto">
                      Connect your Google Calendar to enable AI agents to check availability and book appointments automatically.
                    </p>
                    <Button onClick={handleConnectCalendar}>
                      Connect Google Calendar
                    </Button>
                  </div>
                ) : (
                  <div>
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-green-600 mb-2">
                        ✅ Connected Calendars ({calendarConnections.length})
                      </h4>
                    </div>
                    <ul className="divide-y divide-border border rounded-lg">
                      {calendarConnections.map((connection) => (
                        <li
                          key={connection.id}
                          className="p-4 flex justify-between items-center"
                        >
                          <div>
                            <p className="text-sm font-medium">
                              {connection.calendarName}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {connection.isActive ? (
                                <span className="text-green-600">✓ Active</span>
                              ) : (
                                <span className="text-red-600">✗ Inactive</span>
                              )}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              Connected on {new Date(connection.createdAt).toLocaleDateString()}
                            </p>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              handleDisconnectCalendar(connection.id, connection.calendarName)
                            }
                          >
                            Disconnect
                          </Button>
                        </li>
                      ))}
                    </ul>
                    <div className="mt-4">
                      <Button onClick={handleConnectCalendar}>
                        Connect Another Calendar
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="security">
              <form onSubmit={handlePasswordSubmit}>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg leading-6 font-medium">
                      Change Password
                    </h3>
                    <p className="mt-1 text-sm text-muted-foreground">
                      Update your password to keep your account secure.
                    </p>
                  </div>

                  <div className="grid grid-cols-6 gap-6">
                    <div className="col-span-6 sm:col-span-4">
                      <div className="space-y-2">
                        <Label htmlFor="currentPassword">
                          Current password
                        </Label>
                        <Input
                          type="password"
                          name="currentPassword"
                          id="currentPassword"
                          value={passwordFormData.currentPassword}
                          onChange={handlePasswordChange}
                        />
                      </div>
                    </div>

                    <div className="col-span-6 sm:col-span-4">
                      <div className="space-y-2">
                        <Label htmlFor="newPassword">New password</Label>
                        <Input
                          type="password"
                          name="newPassword"
                          id="newPassword"
                          value={passwordFormData.newPassword}
                          onChange={handlePasswordChange}
                        />
                      </div>
                    </div>

                    <div className="col-span-6 sm:col-span-4">
                      <div className="space-y-2">
                        <Label htmlFor="confirmPassword">
                          Confirm new password
                        </Label>
                        <Input
                          type="password"
                          name="confirmPassword"
                          id="confirmPassword"
                          value={passwordFormData.confirmPassword}
                          onChange={handlePasswordChange}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button type="submit">Change Password</Button>
                  </div>
                </div>
              </form>
            </TabsContent>
          </div>
        </Tabs>
      </div>

      {/* Disconnect Calendar Confirmation Dialog */}
      <Dialog
        open={disconnectDialog.isOpen}
        onClose={cancelDisconnectCalendar}
        className="relative z-50"
      >
        <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

        <div className="fixed inset-0 flex items-center justify-center p-4">
          <Dialog.Panel className="mx-auto max-w-md w-full bg-white rounded-lg shadow-xl">
            <div className="p-6">
              <div className="flex items-center">
                <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100">
                  <ExclamationTriangleIcon className="h-6 w-6 text-red-600" aria-hidden="true" />
                </div>
                <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left flex-1">
                  <Dialog.Title as="h3" className="text-lg font-semibold leading-6 text-gray-900">
                    Disconnect Calendar
                  </Dialog.Title>
                  <div className="mt-2">
                    <p className="text-sm text-gray-500">
                      Are you sure you want to disconnect{" "}
                      <span className="font-medium">{disconnectDialog.connectionName}</span>?
                      You'll need to reconnect it if you want to use calendar features again.
                    </p>
                  </div>
                </div>
              </div>

              <div className="mt-6 flex gap-3 justify-end">
                <Button
                  variant="outline"
                  onClick={cancelDisconnectCalendar}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={confirmDisconnectCalendar}
                >
                  Disconnect
                </Button>
              </div>
            </div>
          </Dialog.Panel>
        </div>
      </Dialog>
    </div>
  );
};

export default Settings;
