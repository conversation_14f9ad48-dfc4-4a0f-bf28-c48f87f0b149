import { useState, useEffect } from "react";
import { useNavigate, Link } from "react-router-dom";
import { createAgent } from "../services/agents";
import { getCalendarConnections } from "../services/calendar";
import type { CalendarConnection } from "../services/calendar";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

interface FormData {
  name: string;
  prompt: string;
  twilioPhoneNumber: string;
  isActive: boolean;
  googleCalendarId: string;
}

const NewAgent = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState<FormData>({
    name: "",
    prompt: "",
    twilioPhoneNumber: "",
    isActive: true,
    googleCalendarId: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [calendarConnections, setCalendarConnections] = useState<
    CalendarConnection[]
  >([]);
  const [isLoadingCalendars, setIsLoadingCalendars] = useState(true);

  // Fetch calendar connections when the component mounts
  useEffect(() => {
    const fetchCalendarConnections = async () => {
      try {
        const connections = await getCalendarConnections();
        setCalendarConnections(connections);
      } catch (err) {
        console.error("Error fetching calendar connections:", err);
      } finally {
        setIsLoadingCalendars(false);
      }
    };

    fetchCalendarConnections();
  }, []);

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value, type } = e.target;

    if (type === "checkbox") {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData((prev) => ({ ...prev, [name]: checked }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      // Create the agent
      await createAgent(formData);

      // Navigate to the agents page
      navigate("/agents");
    } catch (err) {
      console.error("Error creating agent:", err);
      setError("Failed to create agent. Please try again later.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold">Create New Agent</h1>
          <p className="mt-1 text-sm text-muted-foreground">
            Set up a new AI phone agent
          </p>
        </div>
        <Button variant="outline" asChild>
          <Link to="/agents">Cancel</Link>
        </Button>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-destructive/10 border border-destructive/20 rounded-md flex items-center gap-2">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-destructive"
          >
            <circle cx="12" cy="12" r="10" />
            <line x1="12" y1="8" x2="12" y2="12" />
            <line x1="12" y1="16" x2="12.01" y2="16" />
          </svg>
          <p className="text-sm text-destructive font-medium">{error}</p>
        </div>
      )}

      <div className="bg-card shadow-sm overflow-hidden rounded-lg border">
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div className="space-y-2">
            <Label htmlFor="name">Agent Name</Label>
            <Input
              type="text"
              name="name"
              id="name"
              value={formData.name}
              onChange={handleChange}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="prompt">Agent Prompt</Label>
            <Textarea
              name="prompt"
              id="prompt"
              rows={5}
              value={formData.prompt}
              onChange={handleChange}
              required
              placeholder="You are a helpful receptionist for a dental clinic. Your job is to answer calls, provide information about services, and book appointments. Be friendly and professional."
            />
            <p className="text-sm text-muted-foreground">
              This is the instruction given to the AI to define its role and
              behavior.
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="twilioPhoneNumber">Twilio Phone Number</Label>
            <Input
              type="text"
              name="twilioPhoneNumber"
              id="twilioPhoneNumber"
              value={formData.twilioPhoneNumber}
              onChange={handleChange}
              placeholder="+****************"
            />
            <p className="text-sm text-muted-foreground">
              Optional. If left blank, a number will be assigned automatically.
            </p>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="googleCalendarId">Google Calendar</Label>
              {calendarConnections.length > 0 && (
                <span className="text-xs text-green-600 font-medium">
                  ✓ {calendarConnections.length} calendar{calendarConnections.length > 1 ? 's' : ''} connected
                </span>
              )}
            </div>

            {isLoadingCalendars ? (
              <div className="flex items-center space-x-2 p-3 border rounded-md bg-gray-50">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-primary border-t-transparent"></div>
                <span className="text-sm text-muted-foreground">Loading calendars...</span>
              </div>
            ) : calendarConnections.length === 0 ? (
              <div className="p-4 border rounded-md bg-blue-50 border-blue-200">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-sm font-medium text-blue-800">
                      Calendar integration available
                    </h3>
                    <p className="text-sm text-blue-700 mt-1">
                      Connect a Google Calendar to enable automatic appointment booking for this agent.
                    </p>
                    <div className="mt-3">
                      <Button variant="outline" size="sm" asChild>
                        <Link to="/settings" className="text-blue-800 border-blue-300 hover:bg-blue-100">
                          Connect Calendar in Settings
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-2">
                <Select
                  value={formData.googleCalendarId || "none"}
                  onValueChange={(value) => {
                    const calendarId = value === "none" ? "" : value;
                    setFormData(prev => ({ ...prev, googleCalendarId: calendarId }));
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="No calendar selected" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">No calendar selected</SelectItem>
                    {calendarConnections.map((connection) => (
                      <SelectItem key={connection.id} value={connection.calendarId}>
                        {connection.calendarName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground">
                  {formData.googleCalendarId
                    ? "✅ Appointment booking will be enabled - events will be created in the selected calendar."
                    : "Optional. Select a calendar to enable automatic appointment booking for this agent."
                  }
                </p>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="isActive"
              name="isActive"
              checked={formData.isActive}
              onCheckedChange={(checked) =>
                setFormData((prev) => ({ ...prev, isActive: checked === true }))
              }
            />
            <div>
              <Label htmlFor="isActive" className="text-sm font-medium">
                Active
              </Label>
              <p className="text-sm text-muted-foreground">
                When active, the agent will answer calls to its assigned phone
                number.
              </p>
            </div>
          </div>

          <div className="flex justify-end">
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Creating..." : "Create Agent"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default NewAgent;
