import { useState, useEffect, useCallback } from "react";
import { Link, useSearchParams } from "react-router-dom";
import { Phone, Clock, Calendar, Plus } from "lucide-react";
import { format } from "date-fns";
import { getCalls, seedCalls } from "../services/calls";
import { Button } from "@/components/ui/button";

interface Call {
  id: string;
  twilioCallSid: string;
  callerNumber: string;
  callerName?: string;
  status: "ongoing" | "completed" | "failed";
  duration?: number;
  recordingUrl?: string;
  transcriptionUrl?: string;
  agent: {
    id: string;
    name: string;
  };
  callData?: {
    id: string;
    customerInfo?: Record<string, unknown>;
    appointmentDetails?: Record<string, unknown>;
    requestDetails?: Record<string, unknown>;
    summary?: string;
    googleCalendarEventId?: string;
  };
  createdAt: string;
  updatedAt: string;
}

const Calls = () => {
  const [searchParams] = useSearchParams();
  const agentId = searchParams.get("agentId");

  const [calls, setCalls] = useState<Call[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState("all"); // all, today, week, month
  const [isSeeding, setIsSeeding] = useState(false);

  const fetchCalls = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await getCalls(agentId || undefined);
      setCalls(data);
    } catch (err) {
      console.error("Error fetching calls:", err);
      setError("Failed to load calls. Please try again later.");
    } finally {
      setIsLoading(false);
    }
  }, [agentId]);

  const handleSeedCalls = async () => {
    setIsSeeding(true);
    try {
      await seedCalls(5, agentId || undefined);
      // Refetch calls after seeding
      await fetchCalls();
    } catch (err) {
      console.error("Error seeding calls:", err);
      setError("Failed to seed calls. Please try again later.");
    } finally {
      setIsSeeding(false);
    }
  };

  useEffect(() => {
    fetchCalls();
  }, [agentId, fetchCalls]);

  // Filter calls based on the selected time period
  const filteredCalls = calls.filter((call) => {
    const callDate = new Date(call.createdAt);
    const today = new Date();

    if (filter === "all") return true;
    if (filter === "today") {
      return (
        callDate.getDate() === today.getDate() &&
        callDate.getMonth() === today.getMonth() &&
        callDate.getFullYear() === today.getFullYear()
      );
    }
    if (filter === "week") {
      const oneWeekAgo = new Date();
      oneWeekAgo.setDate(today.getDate() - 7);
      return callDate >= oneWeekAgo;
    }
    if (filter === "month") {
      const oneMonthAgo = new Date();
      oneMonthAgo.setMonth(today.getMonth() - 1);
      return callDate >= oneMonthAgo;
    }
    return true;
  });

  return (
    <div>
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Call History</h1>
          <p className="mt-1 text-sm text-gray-500">
            {agentId
              ? "Calls handled by this agent"
              : "All calls handled by your AI agents"}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleSeedCalls}
            disabled={isSeeding}
            className="mr-4"
          >
            <Plus className="h-4 w-4 mr-1" />
            {isSeeding ? "Seeding..." : "Seed Test Calls"}
          </Button>
          <button
            onClick={() => setFilter("all")}
            className={`px-3 py-1.5 text-sm font-medium rounded-md ${
              filter === "all"
                ? "bg-primary/10 text-primary"
                : "bg-background text-foreground/60 border border-border"
            }`}
          >
            All
          </button>
          <button
            onClick={() => setFilter("today")}
            className={`px-3 py-1.5 text-sm font-medium rounded-md ${
              filter === "today"
                ? "bg-primary/10 text-primary"
                : "bg-background text-foreground/60 border border-border"
            }`}
          >
            Today
          </button>
          <button
            onClick={() => setFilter("week")}
            className={`px-3 py-1.5 text-sm font-medium rounded-md ${
              filter === "week"
                ? "bg-primary/10 text-primary"
                : "bg-background text-foreground/60 border border-border"
            }`}
          >
            This Week
          </button>
          <button
            onClick={() => setFilter("month")}
            className={`px-3 py-1.5 text-sm font-medium rounded-md ${
              filter === "month"
                ? "bg-primary/10 text-primary"
                : "bg-background text-foreground/60 border border-border"
            }`}
          >
            This Month
          </button>
        </div>
      </div>

      {error && (
        <div className="mb-4 p-4 text-sm text-red-800 bg-red-100 rounded-md">
          {error}
        </div>
      )}

      {isLoading ? (
        <div className="text-center py-10">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-indigo-500 border-t-transparent"></div>
          <p className="mt-2 text-gray-500">Loading calls...</p>
        </div>
      ) : filteredCalls.length === 0 ? (
        <div className="text-center py-10 bg-white shadow rounded-lg">
          <h3 className="text-lg font-medium text-gray-900">No calls found</h3>
          <p className="mt-1 text-gray-500">
            {filter !== "all"
              ? `No calls found for the selected time period.`
              : agentId
              ? `This agent hasn't handled any calls yet.`
              : `Your AI agents haven't handled any calls yet.`}
          </p>
        </div>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {filteredCalls.map((call) => (
              <li key={call.id}>
                <Link
                  to={`/calls/${call.id}`}
                  className="block hover:bg-gray-50"
                >
                  <div className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-indigo-600 truncate">
                        {call.callerName
                          ? `${call.callerName} (${call.callerNumber})`
                          : call.callerNumber}
                      </p>
                      <div className="ml-2 flex-shrink-0 flex">
                        <p className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                          {call.status}
                        </p>
                      </div>
                    </div>
                    <div className="mt-2 sm:flex sm:justify-between">
                      <div className="sm:flex">
                        <p className="flex items-center text-sm text-gray-500">
                          <Phone
                            className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400"
                            aria-hidden="true"
                          />
                          {call.agent.name}
                        </p>
                        {call.duration && (
                          <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                            <Clock
                              className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400"
                              aria-hidden="true"
                            />
                            {Math.floor(call.duration / 60)}:
                            {call.duration % 60 < 10 ? "0" : ""}
                            {call.duration % 60}
                          </p>
                        )}
                        {call.callData?.appointmentDetails && (
                          <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                            <Calendar
                              className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400"
                              aria-hidden="true"
                            />
                            Appointment booked
                          </p>
                        )}
                      </div>
                      <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                        <p>
                          {format(new Date(call.createdAt), "MMM d, yyyy")} at{" "}
                          {format(new Date(call.createdAt), "h:mm a")}
                        </p>
                      </div>
                    </div>
                  </div>
                </Link>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default Calls;
