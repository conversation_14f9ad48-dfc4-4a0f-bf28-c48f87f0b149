import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import SeedDatabase from '../components/admin/SeedDatabase';

const Admin = () => {
  const { user, isAuthenticated } = useAuth();
  const navigate = useNavigate();

  // Check if user is admin (for now, just check if <NAME_EMAIL>)
  const isAdmin = user?.email === '<EMAIL>';

  useEffect(() => {
    // Redirect if not authenticated or not admin
    if (!isAuthenticated) {
      navigate('/login');
    } else if (!isAdmin) {
      navigate('/');
    }
  }, [isAuthenticated, isAdmin, navigate]);

  if (!isAuthenticated || !isAdmin) {
    return null;
  }

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-semibold">Admin Dashboard</h1>
        <p className="mt-1 text-sm text-muted-foreground">
          Administrative tools and settings
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <SeedDatabase />
        
        {/* Add more admin components here */}
        <div className="bg-card shadow-sm rounded-lg border p-6">
          <h2 className="text-xl font-semibold">Admin Information</h2>
          <div className="mt-4 space-y-2">
            <p className="text-sm">
              <span className="font-medium">Email:</span> {user.email}
            </p>
            <p className="text-sm">
              <span className="font-medium">Name:</span> {user.firstName} {user.lastName}
            </p>
            {user.companyName && (
              <p className="text-sm">
                <span className="font-medium">Company:</span> {user.companyName}
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Admin;
