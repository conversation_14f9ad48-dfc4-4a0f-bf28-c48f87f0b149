import api from "./api";

export interface CallTranscript {
  id: string;
  speaker: "user" | "agent" | "system";
  messageType: "text" | "audio" | "function_call" | "extracted_info";
  content: string;
  timestamp: string;
  metadata?: Record<string, unknown>;
  extractedData?: Record<string, unknown>;
}

export interface ExtractedInformation {
  id: string;
  extractionType:
    | "contact_info"
    | "appointment"
    | "problem_description"
    | "action_items"
    | "sentiment"
    | "intent"
    | "custom";
  key: string;
  value: unknown;
  confidence: number;
  context?: string;
  verified: boolean;
  createdAt: string;
}

export interface CallAnalysis {
  id: string;
  summary: string;
  keyTakeaways: string;
  actionItems: string[];
  outcome: string;
  sentiment: {
    overall: "positive" | "negative" | "neutral";
    scores: {
      positive: number;
      negative: number;
      neutral: number;
    };
    timeline: Array<{
      timestamp: string;
      sentiment: "positive" | "negative" | "neutral";
      score: number;
    }>;
  };
  topics: Array<{
    topic: string;
    confidence: number;
    mentions: number;
  }>;
  callQuality: {
    audioQuality: number;
    interruptionCount: number;
    silencePeriods: number;
    speakingTime: {
      user: number;
      agent: number;
    };
  };
  recommendations: string[];
}

export interface Call {
  id: string;
  type: "phone" | "test" | "simulation";
  source: "inbound" | "outbound" | "test";
  twilioCallSid?: string;
  callerNumber?: string;
  callerName?: string;
  testSessionId?: string;
  status: "ongoing" | "completed" | "failed" | "analyzing";
  duration?: number;
  recordingUrl?: string;
  transcriptionUrl?: string;
  provider?: string;
  agent: {
    id: string;
    name: string;
    prompt?: string;
  };
  callData?: {
    id: string;
    customerInfo?: Record<string, unknown>;
    appointmentDetails?: Record<string, unknown>;
    requestDetails?: Record<string, unknown>;
    summary?: string;
    googleCalendarEventId?: string;
  };
  transcripts?: CallTranscript[];
  extractedInformation?: ExtractedInformation[];
  analysis?: CallAnalysis;
  createdAt: string;
  updatedAt: string;
}

export const getCalls = async (agentId?: string): Promise<Call[]> => {
  const url = agentId ? `/calls?agentId=${agentId}` : "/calls";
  const response = await api.get<Call[]>(url);
  return response.data;
};

export const getCall = async (id: string): Promise<Call> => {
  const response = await api.get<Call>(`/calls/${id}`);
  return response.data;
};

export const seedCalls = async (
  count?: number,
  agentId?: string
): Promise<Call[]> => {
  const response = await api.post<Call[]>("/calls/seed", { count, agentId });
  return response.data;
};
