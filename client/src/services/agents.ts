import api from './api';

interface Agent {
  id: string;
  name: string;
  prompt: string;
  twilioPhoneNumber: string;
  isActive: boolean;
  googleCalendarId: string | null;
  createdAt: string;
  updatedAt: string;
}

interface CreateAgentData {
  name: string;
  prompt: string;
  twilioPhoneNumber?: string;
  isActive?: boolean;
  googleCalendarId?: string;
}

interface UpdateAgentData {
  name?: string;
  prompt?: string;
  twilioPhoneNumber?: string;
  isActive?: boolean;
  googleCalendarId?: string;
}

export const getAgents = async (): Promise<Agent[]> => {
  const response = await api.get<Agent[]>('/agents');
  return response.data;
};

export const getAgent = async (id: string): Promise<Agent> => {
  const response = await api.get<Agent>(`/agents/${id}`);
  return response.data;
};

export const createAgent = async (data: CreateAgentData): Promise<Agent> => {
  const response = await api.post<Agent>('/agents', data);
  return response.data;
};

export const updateAgent = async (id: string, data: UpdateAgentData): Promise<Agent> => {
  const response = await api.patch<Agent>(`/agents/${id}`, data);
  return response.data;
};

export const deleteAgent = async (id: string): Promise<void> => {
  await api.delete(`/agents/${id}`);
};
