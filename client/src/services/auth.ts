import api from "./api";

interface LoginData {
  email: string;
  password: string;
}

interface RegisterData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  companyName?: string;
  phoneNumber?: string;
}

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  companyName?: string;
}

interface ApiError {
  response?: {
    data?: {
      message?: string;
    };
  };
}

interface AuthResponse {
  user: User;
  access_token: string;
  refresh_token: string;
}

export const login = async (data: LoginData): Promise<{ user: User }> => {
  try {
    console.log("Attempting login with:", {
      email: data.email,
      passwordLength: data.password?.length,
    });
    const response = await api.post<AuthResponse>("/auth/login", data);

    // Store both tokens
    localStorage.setItem("token", response.data.access_token);
    localStorage.setItem("refresh_token", response.data.refresh_token);

    // Log success for debugging
    console.log("Login successful, tokens stored");

    return { user: response.data.user };
  } catch (error: unknown) {
    const apiError = error as ApiError;
    throw new Error(apiError.response?.data?.message || "Login failed");
  }
};

export const register = async (data: RegisterData): Promise<{ user: User }> => {
  try {
    const response = await api.post<AuthResponse>("/auth/register", data);
    localStorage.setItem("token", response.data.access_token);
    localStorage.setItem("refresh_token", response.data.refresh_token);
    return { user: response.data.user };
  } catch (error: unknown) {
    const apiError = error as ApiError;
    throw new Error(apiError.response?.data?.message || "Registration failed");
  }
};

export const logout = async (): Promise<void> => {
  try {
    const refreshToken = localStorage.getItem("refresh_token");
    if (refreshToken) {
      await api.post("/auth/logout", { refresh_token: refreshToken });
    }
  } catch (error) {
    console.warn("Error during logout:", error);
  } finally {
    localStorage.removeItem("token");
    localStorage.removeItem("refresh_token");
    window.location.href = "/login";
  }
};

export const getProfile = async (): Promise<User> => {
  try {
    const response = await api.get<User>("/auth/profile");
    return response.data;
  } catch (error: unknown) {
    const apiError = error as ApiError;
    throw new Error(
      apiError.response?.data?.message || "Failed to get profile"
    );
  }
};

export const isAuthenticated = (): boolean => {
  const token = localStorage.getItem("token");
  if (!token) return false;

  try {
    // Basic JWT structure validation
    const parts = token.split('.');
    if (parts.length !== 3) return false;

    // Decode payload to check expiration
    const payload = JSON.parse(atob(parts[1]));
    const currentTime = Math.floor(Date.now() / 1000);

    // Check if token is expired (with 5 minute buffer)
    if (payload.exp && payload.exp < currentTime + 300) {
      return false;
    }

    return true;
  } catch (error) {
    // If token is malformed, consider it invalid
    console.warn('Invalid token format:', error);
    return false;
  }
};

export const validateToken = async (): Promise<boolean> => {
  try {
    await getProfile();
    return true;
  } catch (error) {
    console.warn('Token validation failed:', error);
    return false;
  }
};

export const getTokenExpiration = (): number | null => {
  const token = localStorage.getItem("token");
  if (!token) return null;

  try {
    const parts = token.split('.');
    if (parts.length !== 3) return null;

    const payload = JSON.parse(atob(parts[1]));
    return payload.exp ? payload.exp * 1000 : null; // Convert to milliseconds
  } catch (error) {
    console.warn('Failed to get token expiration:', error);
    return null;
  }
};

export const refreshAccessToken = async (): Promise<boolean> => {
  try {
    const refreshToken = localStorage.getItem("refresh_token");
    if (!refreshToken) {
      console.warn('No refresh token available');
      return false;
    }

    const response = await api.post<{ access_token: string; refresh_token: string }>(
      "/auth/refresh",
      { refresh_token: refreshToken }
    );

    // Store new tokens
    localStorage.setItem("token", response.data.access_token);
    localStorage.setItem("refresh_token", response.data.refresh_token);

    console.log('Access token refreshed successfully');
    return true;
  } catch (error) {
    console.warn('Failed to refresh access token:', error);
    // Clear invalid tokens
    localStorage.removeItem("token");
    localStorage.removeItem("refresh_token");
    return false;
  }
};

export const updateProfile = async (
  data: Partial<RegisterData>
): Promise<User> => {
  try {
    const response = await api.put<{ user: User }>("/auth/profile", data);
    return response.data.user;
  } catch (error: unknown) {
    const apiError = error as ApiError;
    throw new Error(
      apiError.response?.data?.message || "Failed to update profile"
    );
  }
};

export const changePassword = async (data: {
  currentPassword: string;
  newPassword: string;
}): Promise<{ message: string }> => {
  const response = await api.post("/auth/change-password", data);
  return response.data;
};
