import api from './api';

export interface DashboardStats {
  totalAgents: number;
  totalCalls: number;
  callsToday: number;
  appointmentsBooked: number;
}

export interface RecentActivity {
  type: 'call' | 'appointment' | 'agent';
  id: string;
  title: string;
  description: string;
  timestamp: string;
}

export const getDashboardStats = async (): Promise<DashboardStats> => {
  const response = await api.get<DashboardStats>('/stats/dashboard');
  return response.data;
};

export const getRecentActivity = async (limit = 5): Promise<RecentActivity[]> => {
  const response = await api.get<RecentActivity[]>(`/stats/recent-activity?limit=${limit}`);
  return response.data;
};
