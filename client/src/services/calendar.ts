import api from "./api";

export interface CalendarConnection {
  id: string;
  calendarId: string;
  calendarName: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface CalendarEvent {
  id: string;
  summary: string;
  description?: string;
  start: {
    dateTime: string;
    timeZone: string;
  };
  end: {
    dateTime: string;
    timeZone: string;
  };
  attendees?: {
    email: string;
    displayName?: string;
  }[];
  htmlLink?: string;
}

export const getAuthUrl = async (): Promise<{ url: string }> => {
  const response = await api.get<{ url: string }>(
    "/integrations/google/auth-url"
  );
  return response.data;
};

export const handleCallback = async (
  code: string
): Promise<CalendarConnection> => {
  const response = await api.get<CalendarConnection>(
    `/integrations/google/callback?code=${code}`
  );
  return response.data;
};

export const getCalendarConnections = async (): Promise<
  CalendarConnection[]
> => {
  const response = await api.get<CalendarConnection[]>(
    "/integrations/google/calendars"
  );
  return response.data;
};

export const disconnectCalendar = async (id: string): Promise<void> => {
  await api.delete(`/integrations/google/calendars/${id}`);
};

export const getEvents = async (
  timeMin?: string,
  timeMax?: string
): Promise<CalendarEvent[]> => {
  let url = "/integrations/google/events";
  const params = [];

  if (timeMin) {
    params.push(`timeMin=${encodeURIComponent(timeMin)}`);
  }

  if (timeMax) {
    params.push(`timeMax=${encodeURIComponent(timeMax)}`);
  }

  if (params.length > 0) {
    url += `?${params.join("&")}`;
  }

  const response = await api.get<CalendarEvent[]>(url);
  return response.data;
};

export const createEvent = async (data: {
  summary: string;
  description: string;
  startDateTime: string;
  endDateTime: string;
  attendees?: { email: string; name?: string }[];
}): Promise<CalendarEvent> => {
  const response = await api.post<CalendarEvent>(
    "/integrations/google/events",
    data
  );
  return response.data;
};

export const checkAvailability = async (
  startDateTime: string,
  endDateTime: string
): Promise<{ available: boolean }> => {
  const response = await api.get<{ available: boolean }>(
    `/integrations/google/availability?startDateTime=${encodeURIComponent(
      startDateTime
    )}&endDateTime=${encodeURIComponent(endDateTime)}`
  );
  return response.data;
};
