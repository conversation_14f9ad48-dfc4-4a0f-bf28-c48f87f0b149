import { io, Socket } from "socket.io-client";

export interface TestCallSession {
  id: string;
  agentId: string;
  userId: string;
  startedAt: Date;
  status: "active" | "ended";
  transcript: Array<{
    timestamp: Date;
    speaker: "user" | "agent";
    content: string;
    type: "text" | "audio";
  }>;
}

export interface AgentMessage {
  type: "text" | "audio" | "completion" | "speech_started" | "speech_stopped";
  content?: string;
  audioData?: string;
  timestamp: Date;
}

export class TestingService {
  private socket: Socket | null = null;
  private currentSession: TestCallSession | null = null;

  connect(userId: string): Promise<Socket> {
    return new Promise((resolve, reject) => {
      if (this.socket?.connected) {
        resolve(this.socket);
        return;
      }

      this.socket = io("http://localhost:3000/testing", {
        query: { userId },
        transports: ["websocket"],
      });

      this.socket.on("connect", () => {
        console.log("Connected to testing WebSocket");
        resolve(this.socket!);
      });

      this.socket.on("connect_error", (error) => {
        console.error("Testing WebSocket connection error:", error);
        reject(error);
      });

      this.socket.on("error", (error) => {
        console.error("Testing WebSocket error:", error);
      });
    });
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.currentSession = null;
  }

  startTestCall(
    agentId: string,
    provider: "openai" | "gemini" = "gemini"
  ): Promise<{ sessionId: string; agentId: string; provider: string }> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error("Not connected to testing service"));
        return;
      }

      this.socket.emit("start-test-call", { agentId, provider });

      this.socket.once("test-call-started", (data) => {
        this.currentSession = {
          id: data.sessionId,
          agentId: data.agentId,
          userId: "", // Will be set by the server
          startedAt: new Date(),
          status: "active",
          transcript: [],
        };
        resolve(data);
      });

      this.socket.once("error", (error) => {
        reject(new Error(error.message || "Failed to start test call"));
      });
    });
  }

  switchProvider(provider: "openai" | "gemini"): Promise<{ sessionId: string; provider: string }> {
    return new Promise((resolve, reject) => {
      if (!this.socket || !this.currentSession) {
        reject(new Error("No active test session"));
        return;
      }

      this.socket.emit("switch-provider", { provider });

      this.socket.once("provider-switched", (data) => {
        resolve(data);
      });

      this.socket.once("error", (error) => {
        reject(new Error(error.message || "Failed to switch provider"));
      });
    });
  }

  endTestCall() {
    if (this.socket && this.currentSession) {
      this.socket.emit("end-test-call");
      this.currentSession = null;
    }
  }

  sendAudioChunk(audioData: ArrayBuffer) {
    if (this.socket && this.currentSession) {
      this.socket.emit("audio-chunk", audioData);
    }
  }

  sendTextMessage(content: string) {
    if (this.socket && this.currentSession) {
      this.socket.emit("text-message", { content });
    }
  }

  onAgentMessage(callback: (message: AgentMessage) => void) {
    if (this.socket) {
      this.socket.on("agent-message", (data) => {
        console.log("Received agent-message event:", data);
        callback(data);
      });
    }
  }

  onAgentAudio(
    callback: (audioData: {
      type: string;
      audioData: string;
      timestamp: Date;
    }) => void
  ) {
    if (this.socket) {
      this.socket.on("agent-audio", (data) => {
        console.log("Received agent-audio event:", data);
        callback(data);
      });
    }
  }

  onTestCallEnded(
    callback: (data: { sessionId: string; endedAt: Date }) => void
  ) {
    if (this.socket) {
      this.socket.on("test-call-ended", callback);
    }
  }

  onError(callback: (error: { message: string; error?: string }) => void) {
    if (this.socket) {
      this.socket.on("error", callback);
    }
  }

  getCurrentSession(): TestCallSession | null {
    return this.currentSession;
  }

  addToTranscript(
    speaker: "user" | "agent",
    content: string,
    type: "text" | "audio" = "text"
  ) {
    if (this.currentSession) {
      this.currentSession.transcript.push({
        timestamp: new Date(),
        speaker,
        content,
        type,
      });
    }
  }
}

export const testingService = new TestingService();
