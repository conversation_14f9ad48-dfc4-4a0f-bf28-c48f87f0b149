import { useState, useRef, useCallback } from "react";

export interface AudioRecorderState {
  isRecording: boolean;
  isSupported: boolean;
  error: string | null;
}

export interface AudioRecorderControls {
  startRecording: () => Promise<void>;
  stopRecording: () => void;
  clearError: () => void;
}

export const useAudioRecorder = (
  onAudioData?: (audioData: ArrayBuffer) => void,
  onSpeechStart?: () => void,
  onSpeechEnd?: () => void
): [AudioRecorderState, AudioRecorderControls] => {
  const [state, setState] = useState<AudioRecorderState>({
    isRecording: false,
    isSupported:
      typeof navigator !== "undefined" &&
      !!navigator.mediaDevices?.getUserMedia,
    error: null,
  });

  const audioContextRef = useRef<AudioContext | null>(null);
  const processorRef = useRef<ScriptProcessorNode | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const sourceRef = useRef<MediaStreamAudioSourceNode | null>(null);

  // Voice Activity Detection state
  const isSpeakingRef = useRef(false);
  const silenceCounterRef = useRef(0);
  const speechThreshold = 0.01; // Adjust based on microphone sensitivity
  const silenceFrames = 10; // Number of quiet frames before considering speech stopped

  const startRecording = useCallback(async () => {
    if (!state.isSupported) {
      setState((prev) => ({
        ...prev,
        error: "Audio recording is not supported in this browser",
      }));
      return;
    }

    try {
      setState((prev) => ({ ...prev, error: null }));

      // Request microphone access with specific constraints for Gemini Live API
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 16000, // Gemini Live API expects 16kHz input
          channelCount: 1, // Mono audio
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        },
      });

      streamRef.current = stream;

      // Create AudioContext for raw PCM processing
      const audioContext = new (window.AudioContext ||
        (window as typeof window & { webkitAudioContext?: typeof AudioContext })
          .webkitAudioContext)({
        sampleRate: 16000, // Gemini expects 16kHz input
      });

      if (audioContext.state === "suspended") {
        await audioContext.resume();
      }

      audioContextRef.current = audioContext;

      // Create audio source from microphone stream
      const source = audioContext.createMediaStreamSource(stream);
      sourceRef.current = source;

      // Create script processor for raw PCM data extraction
      // Note: ScriptProcessorNode is deprecated but still widely supported
      // AudioWorklet would be preferred but requires more complex setup
      // Using 1024 samples for faster interruption detection (64ms at 16kHz)
      const processor = audioContext.createScriptProcessor(1024, 1, 1);
      processorRef.current = processor;

      processor.onaudioprocess = (event) => {
        const inputBuffer = event.inputBuffer;
        const inputData = inputBuffer.getChannelData(0); // Get mono channel

        // Voice Activity Detection (VAD)
        let audioLevel = 0;
        for (let i = 0; i < inputData.length; i++) {
          audioLevel += Math.abs(inputData[i]);
        }
        audioLevel = audioLevel / inputData.length;

        const wasSpeaking = isSpeakingRef.current;
        const isSpeaking = audioLevel > speechThreshold;

        if (isSpeaking) {
          // User is speaking
          silenceCounterRef.current = 0;
          if (!wasSpeaking) {
            isSpeakingRef.current = true;
            console.log("Speech started - interrupting AI");
            onSpeechStart?.();
          }
        } else {
          // No speech detected
          silenceCounterRef.current++;
          if (wasSpeaking && silenceCounterRef.current >= silenceFrames) {
            isSpeakingRef.current = false;
            console.log("Speech ended - resuming AI");
            onSpeechEnd?.();
          }
        }

        // Send audio data
        if (onAudioData) {
          // Convert Float32 to Int16 (PCM16 format required by OpenAI)
          const pcm16Buffer = new Int16Array(inputData.length);
          for (let i = 0; i < inputData.length; i++) {
            // Clamp to [-1, 1] and convert to 16-bit signed integer
            const sample = Math.max(-1, Math.min(1, inputData[i]));
            pcm16Buffer[i] = sample * 0x7fff;
          }

          // Send as ArrayBuffer
          onAudioData(pcm16Buffer.buffer);
        }
      };

      // Connect the audio processing chain
      source.connect(processor);
      processor.connect(audioContext.destination);

      setState((prev) => ({ ...prev, isRecording: true }));
    } catch (error) {
      console.error("Error starting recording:", error);
      let errorMessage = "Failed to start recording";

      if (error instanceof Error) {
        if (error.name === "NotAllowedError") {
          errorMessage =
            "Microphone access denied. Please allow microphone access and try again.";
        } else if (error.name === "NotFoundError") {
          errorMessage =
            "No microphone found. Please connect a microphone and try again.";
        } else if (error.name === "NotSupportedError") {
          errorMessage = "Audio recording is not supported in this browser.";
        } else {
          errorMessage = error.message;
        }
      }

      setState((prev) => ({ ...prev, error: errorMessage }));
    }
  }, [state.isSupported, onAudioData, onSpeechStart, onSpeechEnd]);

  const stopRecording = useCallback(() => {
    if (!state.isRecording) return;

    try {
      // Disconnect and clean up audio processing chain
      if (sourceRef.current && processorRef.current) {
        sourceRef.current.disconnect();
        processorRef.current.disconnect();
      }

      // Stop all tracks in the stream
      if (streamRef.current) {
        streamRef.current.getTracks().forEach((track) => track.stop());
        streamRef.current = null;
      }

      // Close AudioContext
      if (audioContextRef.current) {
        audioContextRef.current.close();
        audioContextRef.current = null;
      }

      // Clear refs
      sourceRef.current = null;
      processorRef.current = null;

      setState((prev) => ({ ...prev, isRecording: false }));
    } catch (error) {
      console.error("Error stopping recording:", error);
      setState((prev) => ({
        ...prev,
        isRecording: false,
        error: "Error stopping recording",
      }));
    }
  }, [state.isRecording]);

  const clearError = useCallback(() => {
    setState((prev) => ({ ...prev, error: null }));
  }, []);

  return [
    state,
    {
      startRecording,
      stopRecording,
      clearError,
    },
  ];
};
