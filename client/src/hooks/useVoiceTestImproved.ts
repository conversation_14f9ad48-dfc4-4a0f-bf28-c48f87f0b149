import { useEffect, useCallback, useRef } from "react";
import { v4 as uuidv4 } from "uuid";
import { testingService } from "../services/testing";
import type { AgentMessage } from "../services/testing";
import { useAudioRecorder } from "./useAudioRecorder";
import { useTranscript } from "../contexts/TranscriptContext";
import { useEvent } from "../contexts/EventContext";
import { useTesting, type ProviderType } from "../contexts/TestingContext";
import { useAuth } from "../contexts/AuthContext";

/**
 * Real-time audio player for streaming WebSocket audio chunks
 */
class RealtimeAudioPlayer {
  private audioContext: AudioContext | null = null;
  private isInitialized = false;
  private readonly sampleRate = 24000;
  private nextStartTime = 0;
  private activeSources: AudioBufferSourceNode[] = [];
  private isInterrupted = false;

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      this.audioContext = new (window.AudioContext ||
        (window as typeof window & { webkitAudioContext?: typeof AudioContext })
          .webkitAudioContext)({
        sampleRate: this.sampleRate,
      });

      if (this.audioContext.state === "suspended") {
        await this.audioContext.resume();
      }

      this.isInitialized = true;
      console.log("RealtimeAudioPlayer initialized");
    } catch (error) {
      console.error("Failed to initialize RealtimeAudioPlayer:", error);
      throw error;
    }
  }

  private decodeBase64ToFloat32(base64Data: string): Float32Array {
    const binaryString = atob(base64Data);
    const bytes = new Uint8Array(binaryString.length);

    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }

    const pcm16Array = new Int16Array(bytes.buffer);
    const float32Array = new Float32Array(pcm16Array.length);

    for (let i = 0; i < pcm16Array.length; i++) {
      float32Array[i] = pcm16Array[i] / 32768.0;
    }

    return float32Array;
  }

  private async createAndPlayBuffer(
    float32Data: Float32Array,
    scheduled = false
  ): Promise<void> {
    if (!this.audioContext) {
      throw new Error("Audio context not initialized");
    }

    const audioBuffer = this.audioContext.createBuffer(
      1,
      float32Data.length,
      this.sampleRate
    );
    audioBuffer.getChannelData(0).set(float32Data);

    const source = this.audioContext.createBufferSource();
    source.buffer = audioBuffer;
    source.connect(this.audioContext.destination);

    if (scheduled) {
      const currentTime = this.audioContext.currentTime;
      const startTime = Math.max(currentTime, this.nextStartTime);

      source.start(startTime);
      this.nextStartTime = startTime + audioBuffer.duration;

      this.activeSources.push(source);
      source.onended = () => {
        const index = this.activeSources.indexOf(source);
        if (index > -1) {
          this.activeSources.splice(index, 1);
        }
      };
    } else {
      source.start();
    }
  }

  async playChunk(base64Data: string): Promise<void> {
    await this.initialize();

    // Skip playing if interrupted by user speech
    if (this.isInterrupted) {
      return;
    }

    try {
      const float32Data = this.decodeBase64ToFloat32(base64Data);
      await this.createAndPlayBuffer(float32Data, true);
    } catch (error) {
      console.error("Error playing audio chunk:", error);
    }
  }

  async playComplete(base64Data: string): Promise<void> {
    await this.initialize();

    try {
      const float32Data = this.decodeBase64ToFloat32(base64Data);
      await this.createAndPlayBuffer(float32Data, false);
    } catch (error) {
      console.error("Error playing complete audio:", error);
    }
  }

  interrupt(): void {
    console.log("Interrupting AI audio playback");
    this.isInterrupted = true;
    this.stop();
  }

  resume(): void {
    console.log("Resuming AI audio playback");
    this.isInterrupted = false;
  }

  stop(): void {
    this.activeSources.forEach((source) => {
      try {
        source.stop();
      } catch {
        // Source might already be stopped, ignore error
      }
    });
    this.activeSources = [];
    this.nextStartTime = 0;
  }
}

/**
 * Enhanced voice testing hook with improved architecture
 */
export function useVoiceTestImproved() {
  const { user } = useAuth();
  const {
    testingState,
    setSessionStatus,
    setIsRecording,
    setError,
    setIsLoading,
    setSessionId,
    setCurrentProvider,
  } = useTesting();

  const {
    addTranscriptMessage,
    addTranscriptBreadcrumb,
    updateTranscriptItem,
  } = useTranscript();

  const { logClientEvent, logServerEvent } = useEvent();

  // Audio handling refs
  const audioPlayerRef = useRef<RealtimeAudioPlayer | null>(null);
  const currentAudioMessageIdRef = useRef<string | null>(null);
  const accumulatedAudioRef = useRef<string[]>([]);

  // Audio recorder integration with VAD for interruption handling
  const [audioState, audioControls] = useAudioRecorder(
    (audioData) => {
      if (testingState.sessionStatus === "CONNECTED") {
        testingService.sendAudioChunk(audioData);
        logClientEvent(
          { type: "audio_chunk", size: audioData.byteLength },
          "sent"
        );
      }
    },
    // On speech start - interrupt AI
    () => {
      if (audioPlayerRef.current) {
        audioPlayerRef.current.interrupt();
      }
      logClientEvent({ action: "speech_started" }, "vad_interrupt");
    },
    // On speech end - resume AI
    () => {
      if (audioPlayerRef.current) {
        audioPlayerRef.current.resume();
      }
      logClientEvent({ action: "speech_ended" }, "vad_resume");
    }
  );

  // Initialize audio player
  useEffect(() => {
    if (!audioPlayerRef.current) {
      audioPlayerRef.current = new RealtimeAudioPlayer();
    }
  }, []);

  const connect = useCallback(async () => {
    if (!user?.id || testingState.sessionStatus !== "DISCONNECTED") return;

    try {
      setIsLoading(true);
      setError(null);
      setSessionStatus("CONNECTING");

      logClientEvent({ action: "connecting" }, "websocket_connect_attempt");

      await testingService.connect(user.id);
      setSessionStatus("CONNECTED");

      logClientEvent({ action: "connected" }, "websocket_connected");
      addTranscriptBreadcrumb("Connected to testing service");
    } catch (error) {
      console.error("Failed to connect to testing service:", error);
      setError("Failed to connect to testing service");
      setSessionStatus("DISCONNECTED");
      logClientEvent(
        {
          error: error instanceof Error ? error.message : "Unknown error",
        },
        "websocket_connect_error"
      );
    } finally {
      setIsLoading(false);
    }
  }, [
    user?.id,
    testingState.sessionStatus,
    setIsLoading,
    setError,
    setSessionStatus,
    logClientEvent,
    addTranscriptBreadcrumb,
  ]);

  const startTest = useCallback(async () => {
    if (
      !testingState.currentAgentId ||
      testingState.sessionStatus !== "CONNECTED"
    )
      return;

    try {
      setIsLoading(true);
      logClientEvent(
        {
          agentId: testingState.currentAgentId,
          provider: testingState.currentProvider,
        },
        "start_test_call"
      );

      const result = await testingService.startTestCall(
        testingState.currentAgentId,
        testingState.currentProvider
      );
      setSessionId(result.sessionId);

      logServerEvent(result, "test_call_started");
      addTranscriptBreadcrumb(
        `Test session started with ${testingState.currentProvider}`,
        result
      );
    } catch (error) {
      console.error("Failed to start test:", error);
      setError("Failed to start test session");
      logClientEvent(
        {
          error: error instanceof Error ? error.message : "Unknown error",
        },
        "start_test_error"
      );
    } finally {
      setIsLoading(false);
    }
  }, [
    testingState.currentAgentId,
    testingState.currentProvider,
    testingState.sessionStatus,
    setIsLoading,
    setSessionId,
    logClientEvent,
    logServerEvent,
    addTranscriptBreadcrumb,
    setError,
  ]);

  const disconnect = useCallback(() => {
    if (testingState.sessionStatus === "CONNECTED") {
      testingService.endTestCall();
      logClientEvent({ action: "disconnecting" }, "end_test_call");
    }

    testingService.disconnect();
    setSessionStatus("DISCONNECTED");
    setSessionId(null);
    setIsRecording(false);

    // Stop audio
    if (audioPlayerRef.current) {
      audioPlayerRef.current.stop();
    }
    audioControls.stopRecording();

    logClientEvent({ action: "disconnected" }, "websocket_disconnected");
    addTranscriptBreadcrumb("Disconnected from testing service");
  }, [
    testingState.sessionStatus,
    setSessionStatus,
    setSessionId,
    setIsRecording,
    audioControls,
    logClientEvent,
    addTranscriptBreadcrumb,
  ]);

  const toggleConnection = useCallback(() => {
    if (testingState.sessionStatus === "CONNECTED") {
      disconnect();
    } else if (testingState.sessionStatus === "DISCONNECTED") {
      connect();
    }
  }, [testingState.sessionStatus, connect, disconnect]);

  const toggleRecording = useCallback(async () => {
    if (testingState.sessionStatus !== "CONNECTED") return;

    try {
      if (audioState.isRecording) {
        audioControls.stopRecording();
        setIsRecording(false);
        logClientEvent({ action: "stop_recording" }, "audio_recording");
      } else {
        await audioControls.startRecording();
        setIsRecording(true);
        logClientEvent({ action: "start_recording" }, "audio_recording");
      }
    } catch (error) {
      console.error("Error toggling recording:", error);
      setError("Failed to toggle recording");
    }
  }, [
    testingState.sessionStatus,
    audioState.isRecording,
    audioControls,
    setIsRecording,
    logClientEvent,
    setError,
  ]);

  const sendTextMessage = useCallback(
    (content: string) => {
      if (testingState.sessionStatus !== "CONNECTED") return;

      const messageId = uuidv4().slice(0, 32);

      // Add to transcript immediately
      addTranscriptMessage(messageId, "user", content, false);

      // Send to server
      testingService.sendTextMessage(content);
      logClientEvent({ content, messageId }, "text_message_sent");

      // Mark as complete
      updateTranscriptItem(messageId, { status: "DONE" });
    },
    [
      testingState.sessionStatus,
      addTranscriptMessage,
      updateTranscriptItem,
      logClientEvent,
    ]
  );

  const switchProvider = useCallback(
    async (provider: ProviderType) => {
      if (testingState.sessionStatus !== "CONNECTED") {
        throw new Error("Must be connected to switch provider");
      }

      try {
        setIsLoading(true);
        setError(null);

        logClientEvent(
          {
            action: "switching_provider",
            from: testingState.currentProvider,
            to: provider,
          },
          "provider_switch_attempt"
        );

        const result = await testingService.switchProvider(provider);
        setCurrentProvider(provider);

        logClientEvent(result, "provider_switched");
        addTranscriptBreadcrumb(`Switched to ${provider} provider`, result);
      } catch (error) {
        console.error("Failed to switch provider:", error);
        setError(`Failed to switch to ${provider}`);
        logClientEvent(
          {
            error: error instanceof Error ? error.message : "Unknown error",
          },
          "provider_switch_error"
        );
      } finally {
        setIsLoading(false);
      }
    },
    [
      testingState.sessionStatus,
      testingState.currentProvider,
      setIsLoading,
      setError,
      setCurrentProvider,
      logClientEvent,
      addTranscriptBreadcrumb,
    ]
  );

  const playAudio = useCallback(
    async (audioData: string) => {
      if (!audioPlayerRef.current) return;

      try {
        await audioPlayerRef.current.playComplete(audioData);
        logClientEvent({ action: "play_audio" }, "audio_playback");
      } catch (error) {
        console.error("Error playing audio:", error);
        setError("Failed to play audio");
      }
    },
    [logClientEvent, setError]
  );

  // Handle agent messages
  useEffect(() => {
    if (testingState.sessionStatus !== "CONNECTED") return;

    const handleAgentMessage = async (message: AgentMessage) => {
      logServerEvent(
        message as unknown as Record<string, unknown>,
        "agent_message_received"
      );

      if (message.type === "text" && message.content) {
        const messageId = `agent-${Date.now()}-${Math.random()
          .toString(36)
          .slice(2, 11)}`;
        addTranscriptMessage(messageId, "agent", message.content, false);
        updateTranscriptItem(messageId, { status: "DONE" });
      } else if (message.type === "audio" && message.audioData) {
        // Handle streaming audio
        if (!currentAudioMessageIdRef.current) {
          currentAudioMessageIdRef.current = `agent-audio-${Date.now()}-${Math.random()
            .toString(36)
            .slice(2, 11)}`;
          accumulatedAudioRef.current = [];
          addTranscriptMessage(
            currentAudioMessageIdRef.current,
            "agent",
            "🎵 Audio message",
            false
          );
        }

        // Accumulate audio data
        accumulatedAudioRef.current.push(message.audioData);

        // Play chunk in real-time
        if (audioPlayerRef.current) {
          await audioPlayerRef.current.playChunk(message.audioData);
        }
      } else if (message.type === "completion") {
        // Audio message completed
        if (
          currentAudioMessageIdRef.current &&
          accumulatedAudioRef.current.length > 0
        ) {
          const fullAudioData = accumulatedAudioRef.current.join("");

          updateTranscriptItem(currentAudioMessageIdRef.current, {
            status: "DONE",
            audioData: fullAudioData,
          });

          currentAudioMessageIdRef.current = null;
          accumulatedAudioRef.current = [];
        }
      } else if (message.type === "speech_started") {
        // Server detected user speech - interrupt AI immediately
        if (audioPlayerRef.current) {
          audioPlayerRef.current.interrupt();
        }
        addTranscriptBreadcrumb("🔇 User started speaking (server VAD)");
        logServerEvent(
          message as unknown as Record<string, unknown>,
          "server_vad_speech_started"
        );
      } else if (message.type === "speech_stopped") {
        // Server detected user speech ended - resume AI
        if (audioPlayerRef.current) {
          audioPlayerRef.current.resume();
        }
        addTranscriptBreadcrumb("🔊 User stopped speaking (server VAD)");
        logServerEvent(
          message as unknown as Record<string, unknown>,
          "server_vad_speech_stopped"
        );
      }
    };

    testingService.onAgentMessage(handleAgentMessage);

    return () => {
      // Cleanup handled by testingService
    };
  }, [
    testingState.sessionStatus,
    addTranscriptMessage,
    updateTranscriptItem,
    logServerEvent,
    addTranscriptBreadcrumb,
  ]);

  // Sync recording state with audio recorder
  useEffect(() => {
    setIsRecording(audioState.isRecording);
  }, [audioState.isRecording, setIsRecording]);

  // Handle audio errors
  useEffect(() => {
    if (audioState.error) {
      setError(audioState.error);
    }
  }, [audioState.error, setError]);

  return {
    connect,
    disconnect,
    startTest,
    switchProvider,
    toggleConnection,
    toggleRecording,
    sendTextMessage,
    playAudio,
  };
}
