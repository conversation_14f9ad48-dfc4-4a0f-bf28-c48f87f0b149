import { useState, useEffect, useCallback, useRef } from "react";
import { testingService } from "../services/testing";
import type { TestCallSession, AgentMessage } from "../services/testing";
import { useAudioRecorder } from "./useAudioRecorder";

export interface VoiceTestState {
  isConnected: boolean;
  isTestActive: boolean;
  currentSession: TestCallSession | null;
  messages: Array<{
    id: string;
    timestamp: Date;
    speaker: "user" | "agent";
    content: string;
    type: "text" | "audio";
    audioData?: string; // Base64 audio data for audio messages
  }>;
  error: string | null;
  isLoading: boolean;
}

export interface VoiceTestControls {
  startTest: (agentId: string, provider?: "openai" | "gemini") => Promise<void>;
  endTest: () => void;
  sendTextMessage: (content: string) => void;
  clearError: () => void;
  toggleRecording: () => void;
  playAudio: (audioData: string) => Promise<void>;
}

// Real-time audio streaming for WebSocket chunks
class RealtimeAudioPlayer {
  private audioContext: AudioContext | null = null;
  private isInitialized = false;
  private sampleRate = 24000; // Both OpenAI and Gemini output at 24kHz
  private nextStartTime = 0;
  private isPlaying = false;
  private activeSources: AudioBufferSourceNode[] = []; // Track active sources

  async initialize() {
    if (this.isInitialized) return;

    try {
      this.audioContext = new (window.AudioContext ||
        (window as typeof window & { webkitAudioContext?: typeof AudioContext })
          .webkitAudioContext)();
      if (this.audioContext.state === "suspended") {
        await this.audioContext.resume();
      }
      this.isInitialized = true;
      this.nextStartTime = this.audioContext.currentTime;
      console.log("RealtimeAudioPlayer initialized");
    } catch (error) {
      console.error("Failed to initialize AudioContext:", error);
    }
  }

  async playChunk(base64Data: string) {
    if (!this.audioContext || !this.isInitialized) {
      console.error("AudioContext not initialized");
      return;
    }

    try {
      // Convert base64 to PCM16 data
      const binaryString = atob(base64Data);
      let bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      // Ensure we have an even number of bytes for 16-bit samples
      if (bytes.length % 2 !== 0) {
        console.warn("Odd number of bytes in audio chunk, padding with zero");
        const paddedBytes = new Uint8Array(bytes.length + 1);
        paddedBytes.set(bytes);
        paddedBytes[bytes.length] = 0;
        bytes = paddedBytes;
      }

      // Convert to Int16Array (PCM16) with proper little-endian handling
      const pcm16Data = new Int16Array(bytes.length / 2);
      const dataView = new DataView(bytes.buffer);

      for (let i = 0; i < pcm16Data.length; i++) {
        // Read as little-endian 16-bit signed integer
        pcm16Data[i] = dataView.getInt16(i * 2, true); // true = little-endian
      }

      if (pcm16Data.length === 0) {
        console.log("Empty audio chunk, skipping");
        return;
      }

      console.log(`Playing audio chunk: ${pcm16Data.length} samples`);

      // Create AudioBuffer for this chunk
      const audioBuffer = this.audioContext.createBuffer(
        1,
        pcm16Data.length,
        this.sampleRate
      );
      const channelData = audioBuffer.getChannelData(0);

      // Convert Int16 to Float32 and normalize
      for (let i = 0; i < pcm16Data.length; i++) {
        channelData[i] = pcm16Data[i] / 32768.0;
      }

      // Create and schedule audio source for seamless playback
      const source = this.audioContext.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(this.audioContext.destination);

      // Track this source so we can stop it later
      this.activeSources.push(source);

      // Calculate when to start this chunk for seamless playback
      const currentTime = this.audioContext.currentTime;
      const startTime = Math.max(currentTime, this.nextStartTime);

      source.start(startTime);
      this.isPlaying = true;

      // Update next start time for seamless continuation
      this.nextStartTime = startTime + audioBuffer.duration;

      source.onended = () => {
        // Remove from active sources when ended
        const index = this.activeSources.indexOf(source);
        if (index > -1) {
          this.activeSources.splice(index, 1);
        }
        console.log(
          `Audio chunk completed, duration: ${audioBuffer.duration}s`
        );
      };

      console.log(
        `Scheduled audio chunk at ${startTime.toFixed(
          3
        )}s, duration: ${audioBuffer.duration.toFixed(3)}s`
      );
    } catch (error) {
      console.error("Error playing audio chunk:", error);
    }
  }

  reset() {
    // Stop all active sources first
    this.stopAllSources();

    if (this.audioContext) {
      // Add a small delay to prevent overlap with previous audio
      this.nextStartTime = this.audioContext.currentTime + 0.1;
      console.log("Audio player reset, next start time:", this.nextStartTime);
    }
    this.isPlaying = false;
    console.log("RealtimeAudioPlayer reset for new response");
  }

  stop() {
    // Stop all active sources
    this.stopAllSources();

    this.isPlaying = false;
    if (this.audioContext) {
      // Stop any currently playing audio by setting next start time to now
      this.nextStartTime = this.audioContext.currentTime;
    }
    console.log("RealtimeAudioPlayer stopped");
  }

  private stopAllSources() {
    // Stop all currently playing audio sources
    this.activeSources.forEach((source) => {
      try {
        source.stop();
      } catch {
        // Source might already be stopped, ignore error
      }
    });
    this.activeSources = [];
    console.log("Stopped all active audio sources");
  }
}

// Helper function to play base64 audio data (fallback method)
const playAudioData = async (base64AudioData: string): Promise<void> => {
  try {
    console.log(
      "playAudioData called with data length:",
      base64AudioData?.length
    );

    if (!base64AudioData) {
      console.error("No audio data provided");
      return;
    }

    // Convert base64 to ArrayBuffer
    const binaryString = atob(base64AudioData);
    let bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }

    // Ensure we have an even number of bytes for 16-bit samples
    if (bytes.length % 2 !== 0) {
      console.warn("Odd number of bytes in fallback audio, padding with zero");
      const paddedBytes = new Uint8Array(bytes.length + 1);
      paddedBytes.set(bytes);
      paddedBytes[bytes.length] = 0;
      bytes = paddedBytes;
    }

    console.log("Converted to bytes, length:", bytes.length);

    // Create a simple WAV file for fallback playback
    const sampleRate = 24000;

    // Convert to Int16Array with proper little-endian handling
    const pcm16Array = new Int16Array(bytes.length / 2);
    const dataView = new DataView(bytes.buffer);

    for (let i = 0; i < pcm16Array.length; i++) {
      // Read as little-endian 16-bit signed integer
      pcm16Array[i] = dataView.getInt16(i * 2, true); // true = little-endian
    }
    const length = pcm16Array.length;
    const buffer = new ArrayBuffer(44 + length * 2);
    const view = new DataView(buffer);

    // WAV header
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    writeString(0, "RIFF");
    view.setUint32(4, 36 + length * 2, true);
    writeString(8, "WAVE");
    writeString(12, "fmt ");
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, 1, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * 2, true);
    view.setUint16(32, 2, true);
    view.setUint16(34, 16, true);
    writeString(36, "data");
    view.setUint32(40, length * 2, true);

    // Copy PCM data
    const pcm16View = new Int16Array(buffer, 44);
    pcm16View.set(pcm16Array);

    const audioBlob = new Blob([buffer], { type: "audio/wav" });
    const audioUrl = URL.createObjectURL(audioBlob);

    console.log("Created audio URL:", audioUrl);

    // Create and play audio element
    const audio = new Audio(audioUrl);

    // Set up event handlers before playing
    audio.onended = () => {
      console.log("Audio playback ended");
      URL.revokeObjectURL(audioUrl);
    };

    audio.onerror = (e) => {
      console.error("Audio playback error:", e);
      URL.revokeObjectURL(audioUrl);
    };

    audio.onloadeddata = () => {
      console.log("Audio data loaded, duration:", audio.duration);
    };

    // Prevent looping
    audio.loop = false;

    console.log("Attempting to play audio...");
    try {
      await audio.play();
      console.log("Audio play() called successfully");
    } catch (error) {
      console.error("Failed to play audio:", error);
      URL.revokeObjectURL(audioUrl);
      throw error;
    }
  } catch (error) {
    console.error("Error playing audio:", error);
  }
};

export const useVoiceTest = (
  userId: string
): [VoiceTestState, VoiceTestControls] => {
  const [state, setState] = useState<VoiceTestState>({
    isConnected: false,
    isTestActive: false,
    currentSession: null,
    messages: [],
    error: null,
    isLoading: false,
  });

  // Real-time audio player for streaming chunks
  const audioPlayerRef = useRef<RealtimeAudioPlayer | null>(null);
  const currentAudioMessageIdRef = useRef<string | null>(null);
  const accumulatedAudioRef = useRef<string[]>([]); // Store chunks for play button
  const audioMessageMapRef = useRef<Map<string, string>>(new Map()); // Map messageId to accumulated audio

  const [audioState, audioControls] = useAudioRecorder((audioData) => {
    if (state.isTestActive) {
      testingService.sendAudioChunk(audioData);
    }
  });

  // Connect to testing service on mount
  useEffect(() => {
    const connect = async () => {
      try {
        setState((prev) => ({ ...prev, isLoading: true, error: null }));
        await testingService.connect(userId);
        setState((prev) => ({ ...prev, isConnected: true, isLoading: false }));
      } catch (error) {
        console.error("Failed to connect to testing service:", error);
        setState((prev) => ({
          ...prev,
          isConnected: false,
          isLoading: false,
          error: "Failed to connect to testing service",
        }));
      }
    };

    connect();

    return () => {
      testingService.disconnect();
    };
  }, [userId]);

  // Set up event listeners
  useEffect(() => {
    if (!state.isConnected) return;

    const handleAgentMessage = async (message: AgentMessage) => {
      console.log("Received agent message:", message);

      if (message.type === "audio" && message.audioData) {
        // Initialize audio player if needed
        if (!audioPlayerRef.current) {
          audioPlayerRef.current = new RealtimeAudioPlayer();
          await audioPlayerRef.current.initialize();
        }

        // Check if this is a new audio response (create message only once)
        if (!currentAudioMessageIdRef.current) {
          const messageId = `audio-${Date.now()}`;
          currentAudioMessageIdRef.current = messageId;

          // Stop any previous audio and reset for new response
          audioPlayerRef.current.stop();
          audioPlayerRef.current.reset();

          // Clear accumulated audio for new response
          accumulatedAudioRef.current = [];

          // Create a single audio message entry for the entire response
          const newMessage = {
            id: messageId,
            timestamp: message.timestamp,
            speaker: "agent" as const,
            content: "🎵 Audio message",
            type: "audio" as const,
            audioData: messageId, // Use messageId as identifier for accumulated audio
          };

          setState((prev) => ({
            ...prev,
            messages: [...prev.messages, newMessage],
          }));

          testingService.addToTranscript("agent", "Audio message", "audio");
        }

        // Accumulate audio chunk for play button
        accumulatedAudioRef.current.push(message.audioData);

        // Store accumulated audio in the map for this message
        const combinedAudio = accumulatedAudioRef.current.join("");
        audioMessageMapRef.current.set(
          currentAudioMessageIdRef.current,
          combinedAudio
        );

        // TEMPORARILY DISABLED: Play this audio chunk immediately for real-time streaming
        // This might be causing the distortion/repetition issue
        console.log(
          "Audio chunk received, length:",
          message.audioData?.length || 0
        );
        console.log("Total accumulated audio length:", combinedAudio.length);
        // await audioPlayerRef.current.playChunk(message.audioData);
      } else if (message.type === "text" && message.content) {
        // Stop any playing audio when text message arrives
        if (audioPlayerRef.current) {
          audioPlayerRef.current.stop();
        }

        // Handle text messages normally
        const newMessage = {
          id: `agent-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          timestamp: message.timestamp,
          speaker: "agent" as const,
          content: message.content,
          type: "text" as const,
        };

        setState((prev) => ({
          ...prev,
          messages: [...prev.messages, newMessage],
        }));

        testingService.addToTranscript("agent", message.content, "text");

        // Reset audio message tracking for next response
        currentAudioMessageIdRef.current = null;
      } else if (
        message.type === "completion" &&
        message.content === "turn_complete"
      ) {
        // Handle Gemini turn completion signal
        console.log("Turn completed - auto-playing accumulated audio");

        // Auto-play the accumulated audio when response is complete
        if (currentAudioMessageIdRef.current) {
          const combinedAudio = audioMessageMapRef.current.get(
            currentAudioMessageIdRef.current
          );
          if (combinedAudio) {
            console.log("Auto-playing completed audio response");
            await playAudioData(combinedAudio);
          }
        }

        currentAudioMessageIdRef.current = null;
      } else if (message.type === "audio" && !message.audioData) {
        // Handle end of audio response (empty audio data) - fallback for OpenAI
        console.log("Audio response completed (empty data)");

        // Auto-play the accumulated audio when response is complete
        if (currentAudioMessageIdRef.current) {
          const combinedAudio = audioMessageMapRef.current.get(
            currentAudioMessageIdRef.current
          );
          if (combinedAudio) {
            console.log("Auto-playing completed audio response (fallback)");
            await playAudioData(combinedAudio);
          }
        }

        currentAudioMessageIdRef.current = null;
      }
    };

    const handleTestCallEnded = () => {
      setState((prev) => ({
        ...prev,
        isTestActive: false,
        currentSession: null,
      }));

      if (audioState.isRecording) {
        audioControls.stopRecording();
      }
    };

    const handleError = (error: { message: string; error?: string }) => {
      setState((prev) => ({
        ...prev,
        error: error.message,
        isTestActive: false,
      }));

      if (audioState.isRecording) {
        audioControls.stopRecording();
      }
    };

    testingService.onAgentMessage(handleAgentMessage);
    testingService.onTestCallEnded(handleTestCallEnded);
    testingService.onError(handleError);

    return () => {
      // Clean up listeners would go here if the service supported it
      // Reset audio player for cleanup
      if (audioPlayerRef.current) {
        audioPlayerRef.current.stop();
      }
      currentAudioMessageIdRef.current = null;
    };
  }, [state.isConnected, audioState.isRecording, audioControls]);

  const startTest = useCallback(
    async (agentId: string, provider: "openai" | "gemini" = "gemini") => {
      try {
        setState((prev) => ({
          ...prev,
          isLoading: true,
          error: null,
          messages: [],
        }));

        await testingService.startTestCall(agentId, provider);
        const session = testingService.getCurrentSession();

        setState((prev) => ({
          ...prev,
          isTestActive: true,
          currentSession: session,
          isLoading: false,
        }));
      } catch (error) {
        console.error("Failed to start test:", error);
        setState((prev) => ({
          ...prev,
          isLoading: false,
          error:
            error instanceof Error ? error.message : "Failed to start test",
        }));
      }
    },
    []
  );

  const endTest = useCallback(() => {
    testingService.endTestCall();

    if (audioState.isRecording) {
      audioControls.stopRecording();
    }

    setState((prev) => ({
      ...prev,
      isTestActive: false,
      currentSession: null,
    }));
  }, [audioState.isRecording, audioControls]);

  const sendTextMessage = useCallback(
    (content: string) => {
      if (!state.isTestActive) return;

      testingService.sendTextMessage(content);

      // Add user message to local state
      const newMessage = {
        id: `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date(),
        speaker: "user" as const,
        content,
        type: "text" as const,
      };

      setState((prev) => ({
        ...prev,
        messages: [...prev.messages, newMessage],
      }));

      // Add to testing service transcript
      testingService.addToTranscript("user", content, "text");
    },
    [state.isTestActive]
  );

  const clearError = useCallback(() => {
    setState((prev) => ({ ...prev, error: null }));
    audioControls.clearError();
  }, [audioControls]);

  const toggleRecording = useCallback(async () => {
    if (!state.isTestActive) return;

    if (audioState.isRecording) {
      audioControls.stopRecording();
    } else {
      await audioControls.startRecording();
    }
  }, [state.isTestActive, audioState.isRecording, audioControls]);

  const playAudio = useCallback(async (audioData: string) => {
    // Check if this is a messageId (for accumulated audio) or actual base64 data
    if (audioData.startsWith("audio-")) {
      // This is a messageId - get the accumulated audio from the map
      const combinedAudio = audioMessageMapRef.current.get(audioData);
      if (combinedAudio) {
        console.log(`Playing accumulated audio for message: ${audioData}`);
        await playAudioData(combinedAudio);
      } else {
        console.log(`No accumulated audio found for message: ${audioData}`);
      }
    } else {
      // This is regular base64 audio data
      await playAudioData(audioData);
    }
  }, []);

  return [
    {
      ...state,
      // Include audio state
      isRecording: audioState.isRecording,
      audioError: audioState.error,
    } as VoiceTestState & { isRecording: boolean; audioError: string | null },
    {
      startTest,
      endTest,
      sendTextMessage,
      clearError,
      toggleRecording,
      playAudio,
    },
  ];
};
