import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./contexts/AuthContext";
import { EventProvider } from "./contexts/EventContext";
import { TranscriptProvider } from "./contexts/TranscriptContext";
import { TestingProvider } from "./contexts/TestingContext";
import Login from "./pages/Login";
import Register from "./pages/Register";
import Dashboard from "./pages/Dashboard";
import CallHistory from "./pages/CallHistory";
import Layout from "./components/layout/Layout";
import Agents from "./pages/Agents";
import AgentDetail from "./pages/AgentDetail";
import NewAgent from "./pages/NewAgent";
import EditAgent from "./pages/EditAgent";
import CallDetails from "./pages/CallDetails";
import Settings from "./pages/Settings";
import Admin from "./pages/Admin";
import NotFound from "./pages/NotFound";
import ProtectedRoute from "./components/auth/ProtectedRoute";
import TestingPage from "./pages/TestingPage";
import LoadingTest from "./pages/LoadingTest";

function App() {
  return (
    <Router>
      <AuthProvider>
        <EventProvider>
          <TranscriptProvider>
            <TestingProvider>
              <Routes>
                {/* Public routes */}
                <Route path="/login" element={<Login />} />
                <Route path="/register" element={<Register />} />

                {/* Protected routes */}
                <Route element={<ProtectedRoute />}>
                  <Route path="/" element={<Layout />}>
                    <Route index element={<Dashboard />} />
                    <Route path="agents" element={<Agents />} />
                    <Route path="agents/new" element={<NewAgent />} />
                    <Route path="agents/:id" element={<AgentDetail />} />
                    <Route path="agents/:id/edit" element={<EditAgent />} />
                    <Route path="calls" element={<CallHistory />} />
                    <Route path="calls/:callId" element={<CallDetails />} />
                    <Route path="settings" element={<Settings />} />
                    <Route path="admin" element={<Admin />} />
                    <Route path="loading-test" element={<LoadingTest />} />
                    <Route path="*" element={<NotFound />} />
                  </Route>
                  {/* Full-page testing route (outside Layout) */}
                  <Route
                    path="agents/:agentId/test"
                    element={<TestingPage />}
                  />
                </Route>
              </Routes>
            </TestingProvider>
          </TranscriptProvider>
        </EventProvider>
      </AuthProvider>
    </Router>
  );
}

export default App;
