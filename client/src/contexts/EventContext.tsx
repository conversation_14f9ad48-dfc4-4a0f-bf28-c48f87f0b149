"use client";

import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  useRef,
  FC,
  PropsWithChildren,
} from "react";

export interface LoggedEvent {
  id: number;
  direction: "client" | "server";
  expanded: boolean;
  timestamp: string;
  eventName: string;
  eventData: Record<string, unknown>;
}

type EventContextValue = {
  events: LoggedEvent[];
  logClientEvent: (
    eventData: Record<string, unknown>,
    eventNameSuffix?: string
  ) => void;
  logServerEvent: (
    eventData: Record<string, unknown>,
    eventNameSuffix?: string
  ) => void;
  toggleEventExpand: (eventId: number) => void;
  clearEvents: () => void;
};

const EventContext = createContext<EventContextValue | undefined>(undefined);

export const EventProvider: FC<PropsWithChildren> = ({ children }) => {
  const [events, setEvents] = useState<LoggedEvent[]>([]);
  const eventIdCounterRef = useRef(0);

  function newTimestampPretty(): string {
    const now = new Date();
    const time = now.toLocaleTimeString([], {
      hour12: false,
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    });
    const ms = now.getMilliseconds().toString().padStart(3, "0");
    return `${time}.${ms}`;
  }

  const logClientEvent: EventContextValue["logClientEvent"] = useCallback(
    (eventData, eventNameSuffix = "") => {
      const eventName = eventData.type || "unknown";
      const fullEventName = eventNameSuffix
        ? `${eventName}.${eventNameSuffix}`
        : eventName;

      setEvents((prev) => {
        const newEvent: LoggedEvent = {
          id: eventIdCounterRef.current++,
          direction: "client",
          expanded: false,
          timestamp: newTimestampPretty(),
          eventName: fullEventName,
          eventData,
        };

        return [...prev, newEvent];
      });
    },
    []
  );

  const logServerEvent: EventContextValue["logServerEvent"] = useCallback(
    (eventData, eventNameSuffix = "") => {
      const eventName = eventData.type || "unknown";
      const fullEventName = eventNameSuffix
        ? `${eventName}.${eventNameSuffix}`
        : eventName;

      setEvents((prev) => {
        const newEvent: LoggedEvent = {
          id: eventIdCounterRef.current++,
          direction: "server",
          expanded: false,
          timestamp: newTimestampPretty(),
          eventName: fullEventName,
          eventData,
        };

        return [...prev, newEvent];
      });
    },
    []
  );

  const toggleEventExpand: EventContextValue["toggleEventExpand"] = useCallback(
    (eventId) => {
      setEvents((prev) =>
        prev.map((event) =>
          event.id === eventId ? { ...event, expanded: !event.expanded } : event
        )
      );
    },
    []
  );

  const clearEvents = useCallback(() => {
    setEvents([]);
    eventIdCounterRef.current = 0;
  }, []);

  return (
    <EventContext.Provider
      value={{
        events,
        logClientEvent,
        logServerEvent,
        toggleEventExpand,
        clearEvents,
      }}
    >
      {children}
    </EventContext.Provider>
  );
};

export function useEvent() {
  const context = useContext(EventContext);
  if (!context) {
    throw new Error("useEvent must be used within an EventProvider");
  }
  return context;
}
