"use client";

import React, { createContext, useContext, useState, useCallback, FC, PropsWithChildren } from "react";

export type SessionStatus = "DISCONNECTED" | "CONNECTING" | "CONNECTED";
export type ProviderType = "openai" | "gemini";

export interface TestingState {
  sessionStatus: SessionStatus;
  currentAgentId: string | null;
  currentProvider: ProviderType;
  isRecording: boolean;
  error: string | null;
  isLoading: boolean;
  sessionId: string | null;
}

type TestingContextValue = {
  testingState: TestingState;
  setSessionStatus: (status: SessionStatus) => void;
  setCurrentAgent: (agentId: string | null) => void;
  setCurrentProvider: (provider: ProviderType) => void;
  setIsRecording: (recording: boolean) => void;
  setError: (error: string | null) => void;
  setIsLoading: (loading: boolean) => void;
  setSessionId: (sessionId: string | null) => void;
  clearError: () => void;
  resetState: () => void;
};

const TestingContext = createContext<TestingContextValue | undefined>(undefined);

const initialState: TestingState = {
  sessionStatus: "DISCONNECTED",
  currentAgentId: null,
  currentProvider: "gemini",
  isRecording: false,
  error: null,
  isLoading: false,
  sessionId: null,
};

export const TestingProvider: FC<PropsWithChildren> = ({ children }) => {
  const [testingState, setTestingState] = useState<TestingState>(initialState);

  const setSessionStatus = useCallback((status: SessionStatus) => {
    setTestingState(prev => ({ ...prev, sessionStatus: status }));
  }, []);

  const setCurrentAgent = useCallback((agentId: string | null) => {
    setTestingState(prev => ({ ...prev, currentAgentId: agentId }));
  }, []);

  const setCurrentProvider = useCallback((provider: ProviderType) => {
    setTestingState(prev => ({ ...prev, currentProvider: provider }));
  }, []);

  const setIsRecording = useCallback((recording: boolean) => {
    setTestingState(prev => ({ ...prev, isRecording: recording }));
  }, []);

  const setError = useCallback((error: string | null) => {
    setTestingState(prev => ({ ...prev, error }));
  }, []);

  const setIsLoading = useCallback((loading: boolean) => {
    setTestingState(prev => ({ ...prev, isLoading: loading }));
  }, []);

  const setSessionId = useCallback((sessionId: string | null) => {
    setTestingState(prev => ({ ...prev, sessionId }));
  }, []);

  const clearError = useCallback(() => {
    setTestingState(prev => ({ ...prev, error: null }));
  }, []);

  const resetState = useCallback(() => {
    setTestingState(initialState);
  }, []);

  return (
    <TestingContext.Provider
      value={{
        testingState,
        setSessionStatus,
        setCurrentAgent,
        setCurrentProvider,
        setIsRecording,
        setError,
        setIsLoading,
        setSessionId,
        clearError,
        resetState,
      }}
    >
      {children}
    </TestingContext.Provider>
  );
};

export function useTesting() {
  const context = useContext(TestingContext);
  if (!context) {
    throw new Error("useTesting must be used within a TestingProvider");
  }
  return context;
}
