import React, { createContext, useContext, useState, useEffect } from "react";
import type { ReactNode } from "react";
import { useNavigate } from "react-router-dom";
import * as authService from "../services/auth";

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  companyName?: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
}

interface RegisterData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  companyName?: string;
  phoneNumber?: string;
}

interface LoginData {
  email: string;
  password: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  // Token monitoring
  useEffect(() => {
    if (!user) return;

    const monitorToken = () => {
      const expiration = authService.getTokenExpiration();
      if (!expiration) return;

      const now = Date.now();
      const timeUntilExpiry = expiration - now;
      const fiveMinutes = 5 * 60 * 1000; // 5 minutes in milliseconds

      // If token expires in less than 5 minutes, try to refresh
      if (timeUntilExpiry <= fiveMinutes) {
        console.log('Token expiring soon, attempting refresh...');
        authService.refreshAccessToken().then((success) => {
          if (success) {
            console.log('Token refreshed successfully');
            // Schedule next check
            setTimeout(monitorToken, 60000);
          } else {
            console.log('Token refresh failed, logging out');
            logout();
          }
        }).catch(() => {
          console.log('Token refresh failed, logging out');
          logout();
        });
      } else {
        // Schedule next check for when token will be close to expiry
        const checkTime = Math.max(timeUntilExpiry - fiveMinutes, 60000); // At least 1 minute
        setTimeout(monitorToken, checkTime);
      }
    };

    // Start monitoring
    const timer = setTimeout(monitorToken, 60000); // Check every minute initially

    return () => clearTimeout(timer);
  }, [user]);

  // Check if user is authenticated on initial load
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // First check if we have a token and it's not expired
        if (!authService.isAuthenticated()) {
          setLoading(false);
          return;
        }

        // Validate token with server
        const isValid = await authService.validateToken();
        if (isValid) {
          const profile = await authService.getProfile();
          setUser(profile);
        } else {
          // Token is invalid, clear it
          localStorage.removeItem('token');
        }
      } catch (err) {
        console.error("Error checking authentication:", err);
        // If there's an error fetching the profile, the token might be invalid
        localStorage.removeItem('token');
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  const login = async (data: LoginData) => {
    setLoading(true);
    setError(null);
    try {
      const response = await authService.login(data);
      setUser(response.user);
      navigate("/");
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error &&
        "response" in err &&
        typeof err.response === "object" &&
        err.response !== null &&
        "data" in err.response &&
        typeof err.response.data === "object" &&
        err.response.data !== null &&
        "message" in err.response.data &&
        typeof err.response.data.message === "string"
          ? err.response.data.message
          : "Login failed";
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const register = async (data: RegisterData) => {
    setLoading(true);
    setError(null);
    try {
      const response = await authService.register(data);
      setUser(response.user);
      navigate("/");
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error &&
        "response" in err &&
        typeof err.response === "object" &&
        err.response !== null &&
        "data" in err.response &&
        typeof err.response.data === "object" &&
        err.response.data !== null &&
        "message" in err.response.data &&
        typeof err.response.data.message === "string"
          ? err.response.data.message
          : "Registration failed";
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    authService.logout();
    setUser(null);
  };

  const value = {
    user,
    loading,
    error,
    login,
    register,
    logout,
    isAuthenticated: !!user,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
