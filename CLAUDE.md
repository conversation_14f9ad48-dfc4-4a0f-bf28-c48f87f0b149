# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview
Talkloop1 is an AI-powered phone answering service with a NestJS backend and React frontend. It integrates with <PERSON>wi<PERSON> for calls, OpenAI/Gemini for voice AI, and Google Calendar for appointments.

## Common Commands

### Development
```bash
# Start full stack (client + server)
npm run start

# Start components individually
npm run server      # NestJS dev server (port 3000)
npm run client      # Vite dev server (port 5173)

# Install all dependencies
npm run install:all
```

### Server (NestJS)
```bash
cd server
npm run start:dev           # Development with hot reload
npm run build               # Production build
npm run test                # Jest unit tests
npm run test:e2e            # End-to-end tests
npm run migration:generate  # Generate TypeORM migration
npm run migration:run       # Run pending migrations
npm run seed                # Seed database with sample data
```

### Client (React + Vite)
```bash
cd client
npm run dev         # Development server
npm run build       # Production build
npm run lint        # ESLint checking
npm run preview     # Preview production build
```

### Database
```bash
# Start PostgreSQL via Docker
docker-compose up postgres

# Reset and seed database
cd server && npm run migration:run && npm run seed
```

## Architecture

### Backend Structure
- **Modular NestJS**: Feature-based modules (auth, users, agents, calls, integrations)
- **Database**: PostgreSQL with TypeORM, entities for User, Agent, Call, CallData
- **Authentication**: JWT with Passport strategies (local + JWT guards)
- **Real-time**: Socket.io gateways for voice testing and live updates
- **Integrations**: Twilio (calls), OpenAI/Gemini (voice AI), Google Calendar, Resend (email)

### Frontend Structure
- **React 19** with TypeScript, Vite build system
- **UI**: Tailwind CSS + ShadCN components (Radix UI based)
- **State**: Context providers (Auth, Testing, Transcript, Events) + custom hooks
- **Services**: Axios-based API layer in `/services` directory
- **Routing**: React Router with protected route components

### Key Directories
```
server/src/
├── auth/           # JWT authentication
├── agents/         # AI agent management
├── calls/          # Call handling & logging  
├── integrations/   # Third-party services (Twilio, OpenAI, Google, etc.)
├── testing/        # Voice testing with WebSocket gateway
└── database/       # TypeORM config, migrations, seeders

client/src/
├── components/     # Feature-organized React components
├── contexts/       # React Context providers
├── hooks/          # Custom hooks (audio, auth, voice testing)
├── pages/          # Route components
└── services/       # API service abstractions
```

## Development Notes

### Testing Voice Features
- Use `/testing` page for real-time voice interaction testing
- WebSocket connection to server testing gateway
- Audio recording hooks: `useAudioRecorder`, `useVoiceTest`, `useVoiceTestImproved`

### Agent Configuration
- Agents define AI behavior with custom prompts and settings
- Provider support: OpenAI Realtime API and Gemini Live
- Real-time provider factory pattern in `integrations/realtime/`

### Database Workflow
- Always run migrations before seeding: `npm run migration:run && npm run seed`
- Generate migrations after entity changes: `npm run migration:generate`
- Use seeder service for consistent test data

### API Structure
- Global `/api` prefix for all backend routes
- RESTful endpoints with NestJS decorators
- WebSocket gateways for real-time features
- JWT required for most endpoints (check auth guards)