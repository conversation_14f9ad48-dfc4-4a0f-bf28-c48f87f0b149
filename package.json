{"name": "talkloop1", "version": "1.0.0", "description": "AI phone answering service for businesses", "main": "index.js", "scripts": {"start": "npx concurrently \"npm run server\" \"npm run client\"", "server": "cd server && npm run start:dev", "client": "cd client && npm run dev", "install:all": "npm install && cd server && npm install && cd ../client && npm install", "build": "cd server && npm run build && cd ../client && npm run build"}, "keywords": ["ai", "phone", "answering", "service"], "author": "", "license": "ISC", "devDependencies": {"concurrently": "^9.1.2"}}