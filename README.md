# Talkloop - AI Phone Answering Service

Talkloop is an AI-powered phone answering service that allows businesses to handle calls 24/7, book appointments, and integrate with their existing tools.

## Features

- AI voice agents that answer calls using OpenAI's realtime API
- Google Calendar integration for appointment booking
- Call history and data collection
- User-friendly dashboard
- Customizable AI prompts

## Tech Stack

### Backend

- NestJS (Node.js framework)
- PostgreSQL
- TypeORM
- JWT Authentication
- Twilio for phone integration
- OpenAI Realtime API
- Google Calendar API
- Resend for email notifications

### Frontend

- React
- TypeScript
- Tailwind CSS
- React Router
- Axios
- Headless UI components

## Getting Started

### Prerequisites

- Node.js (v16+)
- PostgreSQL (either installed locally or via Docker)
- Docker and Docker Compose (optional, for containerized setup)
- Twilio account
- OpenAI API key
- Google Cloud Platform account (for Calendar API)
- Resend API key

#### Setting Up PostgreSQL

**Option 1: Using Docker (Recommended for development)**

- Install [Docker](https://docs.docker.com/get-docker/) and [Docker Compose](https://docs.docker.com/compose/install/)
- The project includes a `docker-compose.yml` file that will set up PostgreSQL for you

**Option 2: Local Installation**

- Install [PostgreSQL](https://www.postgresql.org/download/) on your machine
- Create a user with the username and password that match your `.env` file (default: postgres/postgres)
- Make sure PostgreSQL is running on the default port (5432)

### Installation

1. Clone the repository:

   ```
   git clone https://github.com/yourusername/talkloop.git
   cd talkloop
   ```

2. Install dependencies:

   ```
   # Install server dependencies
   cd server
   npm install

   # Install client dependencies
   cd ../client
   npm install
   ```

3. Set up environment variables:

   - Copy `.env.example` to `.env` in both the server and client directories
   - Fill in the required environment variables

4. Set up the database:

   ### Option A: Using Docker

   a. Start PostgreSQL using Docker:

   ```bash
   docker-compose up -d postgres
   ```

   b. Create the database:

   ```bash
   docker exec -it talkloop-postgres psql -U postgres -c "CREATE DATABASE talkloop;"
   ```

   c. Verify the database was created:

   ```bash
   docker exec -it talkloop-postgres psql -U postgres -c "\l"
   ```

   ### Option B: Using Local PostgreSQL Installation

   a. Make sure PostgreSQL is installed and running on your machine.

   b. Create the database:

   ```bash
   # For macOS/Linux
   psql -U postgres -c "CREATE DATABASE talkloop;"

   # If you're prompted for a password, use the one from your .env file
   ```

   c. Verify the database was created:

   ```bash
   psql -U postgres -c "\l"
   ```

   > **Note for Windows users**: If you're using Windows, you might need to use pgAdmin or run:
   >
   > ```
   > psql -U postgres
   > CREATE DATABASE talkloop;
   > \l
   > \q
   > ```

   ### For Both Options

   d. Generate the initial migration:

   ```bash
   # In the server directory
   npm run migration:generate -- src/database/migrations/InitialMigration
   ```

   e. Run the migration:

   ```bash
   npm run migration:run
   ```

5. Start the development servers:

   ```
   # Start the server (in the server directory)
   npm run start:dev

   # Start the client (in the client directory)
   npm run dev
   ```

## Project Structure

```
talkloop/
├── server/                 # NestJS backend
│   ├── src/
│   │   ├── main.ts         # Entry point
│   │   ├── app.module.ts   # Root module
│   │   ├── auth/           # Authentication
│   │   ├── users/          # User management
│   │   ├── agents/         # AI agent management
│   │   ├── calls/          # Call handling and logging
│   │   ├── integrations/   # Third-party integrations
│   │   │   ├── twilio/     # Twilio integration
│   │   │   ├── openai/     # OpenAI integration
│   │   │   ├── google/     # Google Calendar integration
│   │   │   └── resend/     # Resend email integration
│   │   └── common/         # Shared utilities
│   ├── test/               # Tests
│   └── package.json        # Dependencies
├── client/                 # React frontend
│   ├── src/
│   │   ├── main.tsx        # Entry point
│   │   ├── App.tsx         # Root component
│   │   ├── components/     # Reusable components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom hooks
│   │   ├── services/       # API services
│   │   ├── utils/          # Utility functions
│   │   └── styles/         # Global styles
│   ├── public/             # Static assets
│   └── package.json        # Dependencies
├── docker-compose.yml      # Docker configuration
├── .env                    # Environment variables
└── README.md               # Project documentation
```

## Database Management

### Working with Migrations

TypeORM migrations allow you to manage database schema changes in a version-controlled way. Here's how to work with migrations:

#### Creating a New Migration

After making changes to your entity files, generate a new migration:

```bash
# In the server directory
npm run migration:generate -- src/database/migrations/YourMigrationName
```

Use descriptive names for migrations that indicate what changes they make, e.g., `AddUserTable`, `UpdateAgentRelationships`, etc.

#### Running Migrations

To apply pending migrations to the database:

```bash
npm run migration:run
```

#### Reverting Migrations

To revert the most recently applied migration:

```bash
npm run migration:revert
```

### Migration Best Practices

1. **Always generate migrations after making changes to your entities**:
   Whenever you modify your entity files (add/remove fields, change relationships, etc.), you should generate a new migration to apply these changes to the database.

2. **Check the generated migration files**:
   Before running migrations, review the generated SQL to ensure it will make the changes you expect.

3. **Use descriptive names for migrations**:
   Instead of generic names like "InitialMigration", use descriptive names that indicate what changes the migration makes.

4. **Test migrations in development before applying to production**:
   Always test your migrations in a development environment before applying them to production to avoid unexpected issues.

5. **Back up your database before running migrations in production**:
   Always create a backup of your production database before running migrations to ensure you can recover if something goes wrong.

### Database Troubleshooting

#### If the Database Doesn't Exist

If you get an error like `database "talkloop" does not exist`, create it manually:

**For Docker users:**

```bash
docker exec -it talkloop-postgres psql -U postgres -c "CREATE DATABASE talkloop;"
```

**For local PostgreSQL users:**

```bash
psql -U postgres -c "CREATE DATABASE talkloop;"
```

#### Using a Different Database Name

If you want to use a different database name, update the `.env` file:

```
DATABASE_NAME=your_database_name
```

Then create the database with that name:

**For Docker users:**

```bash
docker exec -it talkloop-postgres psql -U postgres -c "CREATE DATABASE your_database_name;"
```

**For local PostgreSQL users:**

```bash
psql -U postgres -c "CREATE DATABASE your_database_name;"
```

#### Connecting to the Database Directly

To connect to the PostgreSQL database for manual queries:

**For Docker users:**

```bash
docker exec -it talkloop-postgres psql -U postgres -d talkloop
```

**For local PostgreSQL users:**

```bash
psql -U postgres -d talkloop
```

#### PostgreSQL Connection Issues

If you're having trouble connecting to PostgreSQL:

1. **Check if PostgreSQL is running:**

   ```bash
   # For macOS/Linux
   ps aux | grep postgres

   # For Windows
   tasklist | findstr postgres
   ```

2. **Start PostgreSQL if it's not running:**

   ```bash
   # For macOS (using Homebrew)
   brew services start postgresql

   # For Ubuntu/Debian
   sudo service postgresql start

   # For Windows
   net start postgresql
   ```

3. **Verify your PostgreSQL credentials:**
   Make sure the username and password in your `.env` file match your PostgreSQL installation.

4. **Check PostgreSQL port:**
   The default port is 5432. If your installation uses a different port, update the `DATABASE_PORT` in your `.env` file.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgements

- OpenAI for the realtime API
- Twilio for the phone integration
- Google for the Calendar API
- Resend for the email service
