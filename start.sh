#!/bin/bash

# Start the server in the background
cd server && npm run start:dev &
SERVER_PID=$!

# Start the client in the background
cd ../client && npm run dev &
CLIENT_PID=$!

# Function to handle script termination
cleanup() {
  echo "Stopping server and client..."
  kill $SERVER_PID
  kill $CLIENT_PID
  exit
}

# Trap SIGINT (Ctrl+C) and call cleanup
trap cleanup SIGINT

# Keep the script running
echo "Server and client are running. Press Ctrl+C to stop."
wait
