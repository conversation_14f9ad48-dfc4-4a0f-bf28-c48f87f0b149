services:
  postgres:
    image: postgres:14-alpine
    container_name: talkloop-postgres
    ports:
      - '5432:5432'
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: talkloop
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - talkloop-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  server:
    build:
      context: ./server
      dockerfile: Dockerfile
    container_name: talkloop-server
    ports:
      - '3000:3000'
    depends_on:
      postgres:
        condition: service_healthy
    env_file:
      - ./server/.env.docker
    volumes:
      - ./server:/app
      - /app/node_modules
    networks:
      - talkloop-network
    restart: unless-stopped

  client:
    build:
      context: ./client
      dockerfile: Dockerfile
    container_name: talkloop-client
    ports:
      - '5173:5173'
    depends_on:
      - server
    environment:
      - VITE_API_URL=http://localhost:3000
    volumes:
      - ./client:/app
      - /app/node_modules
    networks:
      - talkloop-network
    restart: unless-stopped

networks:
  talkloop-network:
    driver: bridge

volumes:
  postgres-data:
