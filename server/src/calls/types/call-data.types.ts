export interface CustomerInfo {
  name?: string;
  email?: string;
  phone?: string;
  company?: string;
  notes?: string;
}

export interface AppointmentDetails {
  date: string;
  duration: number;
  type?: string;
  service?: string;
  notes?: string;
  preferred_time?: string;
  time_preference?: 'morning' | 'afternoon' | 'evening' | 'any';
}

export interface RequestDetails {
  urgency?: 'low' | 'medium' | 'high' | 'critical';
  primary_intent?: string;
  specific_request?: string;
  category?:
    | 'technical'
    | 'billing'
    | 'account'
    | 'product'
    | 'service'
    | 'other';
  problem_description?: string;
}

export interface TranscriptMetadata {
  audioDuration?: number;
  confidenceScores?: number;
  functionName?: string;
  arguments?: Record<string, unknown>;
  [key: string]: unknown;
}

export interface ExtractedData {
  type?: string;
  key?: string;
  value?: unknown;
  confidence?: number;
  context?: string;
}

export interface ExtractContactInfoArgs {
  name?: string;
  email?: string;
  phone?: string;
  company?: string;
  address?: string;
  confidence?: number;
}

export interface ExtractAppointmentArgs {
  datetime?: string;
  preferred_date?: string;
  preferred_time?: string;
  service_type?: string;
  duration?: string | number;
  flexibility?: 'flexible' | 'specific' | 'urgent';
  confidence?: number;
}

export interface ExtractIntentArgs {
  primary_intent?:
    | 'schedule_appointment'
    | 'billing_inquiry'
    | 'technical_support'
    | 'general_question'
    | 'cancel_service'
    | 'product_inquiry';
  urgency?: 'low' | 'medium' | 'high' | 'critical';
  specific_request?: string;
  confidence?: number;
}

export interface ExtractSentimentArgs {
  sentiment?: 'positive' | 'negative' | 'neutral';
  overall_sentiment?:
    | 'very_positive'
    | 'positive'
    | 'neutral'
    | 'negative'
    | 'very_negative';
  satisfaction_level?:
    | 'very_satisfied'
    | 'satisfied'
    | 'neutral'
    | 'dissatisfied'
    | 'very_dissatisfied';
  key_emotions?: string[];
  score?: number;
  reason?: string;
  confidence?: number;
}

export interface ExtractResolutionArgs {
  issue_resolved?: boolean;
  resolution_type?:
    | 'fully_resolved'
    | 'partially_resolved'
    | 'requires_followup'
    | 'escalated'
    | 'unresolved';
  next_steps?: string[];
  followup_required?: boolean;
  confidence?: number;
}

export interface ExtractActionItemsArgs {
  action_items: string[];
  confidence?: number;
}

export interface ExtractProblemArgs {
  problem?: string;
  technical_details?: string;
  severity?: 'low' | 'medium' | 'high' | 'critical';
  category?:
    | 'technical'
    | 'billing'
    | 'account'
    | 'product'
    | 'service'
    | 'other';
  urgency?: 'low' | 'medium' | 'high' | 'critical';
  confidence?: number;
}

export type ExtractedValue =
  | string
  | number
  | boolean
  | Date
  | string[]
  | {
      sentiment: string;
      score: number;
      reason: string;
    };

export type FunctionArguments =
  | ExtractContactInfoArgs
  | ExtractAppointmentArgs
  | ExtractIntentArgs
  | ExtractSentimentArgs
  | ExtractResolutionArgs
  | ExtractActionItemsArgs
  | ExtractProblemArgs;
