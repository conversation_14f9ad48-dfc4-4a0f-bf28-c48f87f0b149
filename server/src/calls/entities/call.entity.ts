import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { Agent } from '../../agents/entities/agent.entity';
import { CallData } from './call-data.entity';
import { CallTranscript } from './call-transcript.entity';
import { ExtractedInformation } from './extracted-information.entity';
import { CallAnalysis } from './call-analysis.entity';

export enum CallStatus {
  ONGOING = 'ongoing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  ANALYZING = 'analyzing', // Post-call analysis in progress
}

export enum CallType {
  PHONE = 'phone', // Real phone call via Twilio
  TEST = 'test', // Test call via web interface
  SIMULATION = 'simulation', // AI simulation
}

export enum CallSource {
  INBOUND = 'inbound', // Customer called us
  OUTBOUND = 'outbound', // We called customer
  TEST = 'test', // Test/simulation call
}

@Entity()
export class Call {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'enum', enum: CallType, default: CallType.PHONE })
  type: CallType;

  @Column({ type: 'enum', enum: CallSource, default: CallSource.INBOUND })
  source: CallSource;

  @Column({ nullable: true })
  twilioCallSid: string; // Only for phone calls

  @Column({ nullable: true })
  callerNumber: string; // Only for phone calls

  @Column({ nullable: true })
  callerName: string;

  @Column({ nullable: true })
  testSessionId: string; // Only for test calls

  @Column({ type: 'enum', enum: CallStatus, default: CallStatus.ONGOING })
  status: CallStatus;

  @Column({ type: 'int', nullable: true })
  duration: number; // Duration in seconds

  @Column({ nullable: true })
  recordingUrl: string;

  @Column({ nullable: true })
  transcriptionUrl: string;

  @Column({ nullable: true })
  provider: string; // 'openai' | 'gemini'

  @ManyToOne(() => Agent, (agent) => agent.calls)
  agent: Agent;

  @OneToOne(() => CallData, { nullable: true })
  @JoinColumn()
  callData: CallData;

  @OneToMany(() => CallTranscript, (transcript) => transcript.call, {
    cascade: true,
  })
  transcripts: CallTranscript[];

  @OneToMany(() => ExtractedInformation, (info) => info.call, { cascade: true })
  extractedInformation: ExtractedInformation[];

  @OneToOne(() => CallAnalysis, (analysis) => analysis.call, { cascade: true })
  analysis: CallAnalysis;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
