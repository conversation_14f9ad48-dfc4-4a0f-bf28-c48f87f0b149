import {
  <PERSON><PERSON>ty,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import {
  CustomerInfo,
  AppointmentDetails,
  RequestDetails,
} from '../types/call-data.types';

@Entity()
export class CallData {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'jsonb', nullable: true })
  customerInfo: CustomerInfo;

  @Column({ type: 'jsonb', nullable: true })
  appointmentDetails: AppointmentDetails;

  @Column({ type: 'jsonb', nullable: true })
  requestDetails: RequestDetails;

  @Column({ type: 'text', nullable: true })
  summary: string;

  @Column({ nullable: true })
  googleCalendarEventId: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
