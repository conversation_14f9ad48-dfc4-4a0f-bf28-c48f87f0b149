import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn,
} from 'typeorm';
import { Call } from './call.entity';

export enum AnalysisStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

export enum CallOutcome {
  SUCCESSFUL = 'successful',
  NEEDS_FOLLOWUP = 'needs_followup',
  ISSUE_RESOLVED = 'issue_resolved',
  APPOINTMENT_SCHEDULED = 'appointment_scheduled',
  INFORMATION_GATHERED = 'information_gathered',
  ESCALATED = 'escalated',
  DISCONNECTED = 'disconnected',
}

@Entity()
export class CallAnalysis {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @OneToOne(() => Call, (call) => call.analysis)
  @JoinColumn()
  call: Call;

  @Column({
    type: 'enum',
    enum: AnalysisStatus,
    default: AnalysisStatus.PENDING,
  })
  status: AnalysisStatus;

  @Column({ type: 'text', nullable: true })
  summary: string; // AI-generated call summary

  @Column({ type: 'text', nullable: true })
  keyTakeaways: string; // Main points from the call

  @Column({ type: 'jsonb', nullable: true })
  actionItems: string[]; // Tasks to be completed

  @Column({ type: 'enum', enum: CallOutcome, nullable: true })
  outcome: CallOutcome;

  @Column({ type: 'jsonb', nullable: true })
  sentiment: {
    overall: 'positive' | 'negative' | 'neutral';
    scores: {
      positive: number;
      negative: number;
      neutral: number;
    };
    timeline: Array<{
      timestamp: Date;
      sentiment: 'positive' | 'negative' | 'neutral';
      score: number;
    }>;
  };

  @Column({ type: 'jsonb', nullable: true })
  topics: Array<{
    topic: string;
    confidence: number;
    mentions: number;
  }>;

  @Column({ type: 'jsonb', nullable: true })
  callQuality: {
    audioQuality: number; // 0-1 score
    interruptionCount: number;
    silencePeriods: number;
    speakingTime: {
      user: number; // seconds
      agent: number; // seconds
    };
  };

  @Column({ type: 'jsonb', nullable: true })
  recommendations: string[]; // AI suggestions for improvement

  @Column({ type: 'text', nullable: true })
  errorMessage: string; // If analysis failed

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
