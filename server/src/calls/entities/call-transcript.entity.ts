import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  ManyToOne,
  Index,
} from 'typeorm';
import { Call } from './call.entity';
import { TranscriptMetadata, ExtractedData } from '../types/call-data.types';

export enum SpeakerType {
  USER = 'user',
  AGENT = 'agent',
  SYSTEM = 'system',
}

export enum MessageType {
  TEXT = 'text',
  AUDIO = 'audio',
  FUNCTION_CALL = 'function_call',
  EXTRACTED_INFO = 'extracted_info',
}

@Entity()
@Index(['call', 'timestamp']) // For efficient chronological queries
export class CallTranscript {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Call, (call) => call.transcripts)
  call: Call;

  @Column({ type: 'enum', enum: SpeakerType })
  speaker: SpeakerType;

  @Column({ type: 'enum', enum: MessageType, default: MessageType.TEXT })
  messageType: MessageType;

  @Column({ type: 'text' })
  content: string;

  @Column({ type: 'jsonb', nullable: true })
  metadata: TranscriptMetadata; // Audio duration, confidence scores, etc.

  @Column({ type: 'jsonb', nullable: true })
  extractedData: ExtractedData; // Information extracted from this message

  @Column({ type: 'timestamptz' })
  timestamp: Date;

  @CreateDateColumn()
  createdAt: Date;
}
