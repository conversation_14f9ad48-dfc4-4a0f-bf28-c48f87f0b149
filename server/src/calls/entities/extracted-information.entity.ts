import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  Index,
} from 'typeorm';
import { Call } from './call.entity';
import { ExtractedValue } from '../types/call-data.types';

export enum ExtractionType {
  CONTACT_INFO = 'contact_info',
  APPOINTMENT = 'appointment',
  PROBLEM_DESCRIPTION = 'problem_description',
  ACTION_ITEMS = 'action_items',
  SENTIMENT = 'sentiment',
  INTENT = 'intent',
  CUSTOM = 'custom',
}

export enum ExtractionSource {
  REAL_TIME = 'real_time', // Extracted during conversation
  POST_CALL = 'post_call', // Extracted after call ended
  MANUAL = 'manual', // Manually added/edited
}

@Entity()
@Index(['call', 'extractionType']) // For efficient filtering
export class ExtractedInformation {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Call, (call) => call.extractedInformation)
  call: Call;

  @Column({ type: 'enum', enum: ExtractionType })
  extractionType: ExtractionType;

  @Column({
    type: 'enum',
    enum: ExtractionSource,
    default: ExtractionSource.REAL_TIME,
  })
  extractionSource: ExtractionSource;

  @Column()
  key: string; // e.g., "customer_name", "phone_number", "appointment_date"

  @Column({ type: 'jsonb' })
  value: ExtractedValue; // Flexible storage for any data type

  @Column({ type: 'float', nullable: true })
  confidence: number; // AI confidence score (0-1)

  @Column({ type: 'text', nullable: true })
  context: string; // Where in conversation this was extracted from

  @Column({ type: 'boolean', default: false })
  verified: boolean; // Whether human has verified this information

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
