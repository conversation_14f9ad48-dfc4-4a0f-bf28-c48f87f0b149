import {
  Controller,
  Get,
  Post,
  Param,
  UseGuards,
  Request,
  Query,
  Body,
} from '@nestjs/common';
import { CallsService } from './calls.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AuthenticatedRequest } from '../common/types/request.types';

interface FindAllQueryDto {
  agentId?: string;
}

interface SeedCallsDto {
  count?: number;
  agentId?: string;
}

@Controller('calls')
@UseGuards(JwtAuthGuard)
export class CallsController {
  constructor(private readonly callsService: CallsService) {}

  @Get()
  async findAll(
    @Request() req: AuthenticatedRequest,
    @Query() query: FindAllQueryDto,
  ) {
    // If agentId is provided, check if it belongs to the user
    if (query.agentId) {
      return this.callsService.findAll(query.agentId);
    }

    // Otherwise, get all calls for the user
    return this.callsService.findByUserId(req.user.id);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.callsService.findOne(id);
  }

  @Post('/seed')
  async seedCalls(
    @Request() req: AuthenticatedRequest,
    @Body() body: SeedCallsDto,
  ) {
    const count = body.count || 5; // Default to 5 calls if not specified
    const userId = req.user.id;
    return this.callsService.seedCalls(userId, count, body.agentId);
  }
}
