import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  ExtractedInformation,
  ExtractionType,
  ExtractionSource,
} from '../entities/extracted-information.entity';
import {
  CallTranscript,
  SpeakerType,
  MessageType,
} from '../entities/call-transcript.entity';
import { Call } from '../entities/call.entity';
import { OpenaiService } from '../../integrations/openai/openai.service';
import {
  ExtractContactInfoArgs,
  ExtractAppointmentArgs,
  ExtractIntentArgs,
  ExtractSentimentArgs,
  ExtractResolutionArgs,
  ExtractActionItemsArgs,
  ExtractProblemArgs,
  FunctionArguments,
  ExtractedValue,
} from '../types/call-data.types';

export interface ExtractionFunction {
  name: string;
  description: string;
  parameters: {
    type: 'object';
    properties: Record<
      string,
      {
        type: string;
        description: string;
        enum?: string[];
      }
    >;
    required: string[];
  };
}

export interface ExtractionSchema {
  functions: ExtractionFunction[];
  instructions: string;
}

interface OpenAICompletion {
  choices: Array<{
    message?: {
      tool_calls?: Array<{
        type: string;
        function: {
          name: string;
          arguments: string;
        };
      }>;
    };
  }>;
}

// Type guard for OpenAI completion response
function isValidOpenAICompletion(obj: unknown): obj is OpenAICompletion {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'choices' in obj &&
    Array.isArray((obj as Record<string, unknown>).choices)
  );
}

@Injectable()
export class InformationExtractionService {
  private readonly logger = new Logger(InformationExtractionService.name);

  constructor(
    @InjectRepository(ExtractedInformation)
    private extractedInfoRepository: Repository<ExtractedInformation>,
    @InjectRepository(CallTranscript)
    private transcriptRepository: Repository<CallTranscript>,
    private openaiService: OpenaiService,
  ) {}

  /**
   * Get extraction schema for a specific agent type
   */
  getExtractionSchema(agentType: string): ExtractionSchema {
    // This could be configured per agent or agent type
    switch (agentType.toLowerCase()) {
      case 'customer_service':
        return this.getCustomerServiceSchema();
      case 'appointment_booking':
        return this.getAppointmentBookingSchema();
      case 'sales':
        return this.getSalesSchema();
      default:
        return this.getGenericSchema();
    }
  }

  /**
   * AI-powered real-time extraction from user message
   */
  async extractFromMessage(
    call: Call,
    userMessage: string,
    conversationHistory: string = '',
  ): Promise<ExtractedInformation[]> {
    try {
      const agentType = call.agent?.name || 'generic';
      const extractionPrompt = `
Analyze this customer message and extract relevant information:
"${userMessage}"

Context: This is a ${agentType} call.
Previous conversation context: ${conversationHistory}

Extract any contact information, intents, problems, or appointment requests mentioned.
`;

      let completion: unknown;
      try {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-call
        completion = await this.openaiService.createChatCompletion({
          model: 'gpt-4',
          messages: [
            {
              role: 'system',
              content:
                'You are an expert information extraction system. Extract structured data from customer service conversations.',
            },
            {
              role: 'user',
              content: extractionPrompt,
            },
          ],
          tools: this.getAIExtractionTools(),
          tool_choice: 'auto',
        });
      } catch (error) {
        this.logger.error('Error calling OpenAI API:', error);
        // Fallback to pattern matching
        return this.fallbackPatternExtraction(call, userMessage);
      }

      const extractedItems: ExtractedInformation[] = [];

      // Type guard to ensure completion has the expected structure
      if (
        isValidOpenAICompletion(completion) &&
        completion.choices[0]?.message?.tool_calls
      ) {
        for (const toolCall of completion.choices[0].message.tool_calls) {
          if (toolCall.type === 'function') {
            const functionName = toolCall.function.name;
            const args = JSON.parse(
              toolCall.function.arguments,
            ) as FunctionArguments;

            const items = await this.processFunctionCall(
              call,
              functionName,
              args,
              `AI extracted from: "${userMessage}"`,
            );
            extractedItems.push(...items);
          }
        }
      }

      return extractedItems;
    } catch (error) {
      this.logger.error('Error in AI-powered extraction:', error);
      // Fallback to pattern matching
      return this.fallbackPatternExtraction(call, userMessage);
    }
  }

  /**
   * Comprehensive post-call AI analysis
   */
  async performPostCallAnalysis(call: Call): Promise<ExtractedInformation[]> {
    try {
      // Get all transcripts for the call
      const transcripts = await this.transcriptRepository.find({
        where: { call: { id: call.id } },
        order: { timestamp: 'ASC' },
      });

      const fullTranscript = transcripts
        .map((t) => `${t.speaker}: ${t.content}`)
        .join('\n');

      const analysisPrompt = `
COMPREHENSIVE CALL ANALYSIS

TRANSCRIPT:
${fullTranscript}

TASK: Analyze this complete customer service call transcript and extract ALL relevant information with high accuracy.

Extract:
1. Complete customer contact information (name, email, phone, company, address)
2. All issues, problems, or requests discussed
3. Appointment details if any were scheduled
4. Customer sentiment and satisfaction indicators
5. Resolution status and outcomes
6. Follow-up actions required
7. Any product or service interests mentioned

Be thorough and accurate. Consider the full context of the conversation.
`;

      let completion: unknown;
      try {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-call
        completion = await this.openaiService.createChatCompletion({
          model: 'gpt-4',
          messages: [
            {
              role: 'system',
              content:
                'You are an expert call analyst. Perform comprehensive analysis of customer service calls.',
            },
            {
              role: 'user',
              content: analysisPrompt,
            },
          ],
          tools: this.getComprehensiveAnalysisTools(),
          tool_choice: 'auto',
        });
      } catch (error) {
        this.logger.error(
          'Error calling OpenAI API for post-call analysis:',
          error,
        );
        return [];
      }

      const extractedItems: ExtractedInformation[] = [];

      // Type guard to ensure completion has the expected structure
      if (
        isValidOpenAICompletion(completion) &&
        completion.choices[0]?.message?.tool_calls
      ) {
        for (const toolCall of completion.choices[0].message.tool_calls) {
          if (toolCall.type === 'function') {
            const functionName = toolCall.function.name;
            const args = JSON.parse(
              toolCall.function.arguments,
            ) as FunctionArguments;

            const items = await this.processFunctionCall(
              call,
              functionName,
              args,
              'Comprehensive post-call AI analysis',
            );
            extractedItems.push(...items);
          }
        }
      }

      this.logger.log(
        `Post-call analysis extracted ${extractedItems.length} items for call ${call.id}`,
      );
      return extractedItems;
    } catch (error) {
      this.logger.error('Error in post-call AI analysis:', error);
      return [];
    }
  }

  /**
   * Fallback pattern-based extraction
   */
  private fallbackPatternExtraction(
    call: Call,
    userMessage: string,
  ): ExtractedInformation[] {
    const items: ExtractedInformation[] = [];

    // Email extraction
    const emailMatch = userMessage.match(/[\w.-]+@[\w.-]+\.\w+/);
    if (emailMatch) {
      items.push(
        this.createExtractionItem(
          call,
          ExtractionType.CONTACT_INFO,
          'email',
          emailMatch[0],
          0.8,
          `Pattern extracted from: "${userMessage}"`,
        ),
      );
    }

    // Name extraction
    const nameMatch = userMessage.match(
      /(?:I'm|I am|my name is|this is)\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)/i,
    );
    if (nameMatch) {
      items.push(
        this.createExtractionItem(
          call,
          ExtractionType.CONTACT_INFO,
          'customer_name',
          nameMatch[1],
          0.7,
          `Pattern extracted from: "${userMessage}"`,
        ),
      );
    }

    return items;
  }

  /**
   * AI extraction tools for real-time processing
   */
  private getAIExtractionTools(): Array<{
    type: 'function';
    function: {
      name: string;
      description: string;
      parameters: {
        type: 'object';
        properties: Record<
          string,
          {
            type: string;
            description: string;
            enum?: string[];
            items?: { type: string };
          }
        >;
        required: string[];
      };
    };
  }> {
    return [
      {
        type: 'function',
        function: {
          name: 'extract_contact_info',
          description: 'Extract customer contact information',
          parameters: {
            type: 'object',
            properties: {
              name: { type: 'string', description: 'Customer full name' },
              email: { type: 'string', description: 'Email address' },
              phone: { type: 'string', description: 'Phone number' },
              company: {
                type: 'string',
                description: 'Company or organization',
              },
              confidence: {
                type: 'number',
                description: 'Confidence score 0-1',
              },
            },
            required: [],
          },
        },
      },
      {
        type: 'function',
        function: {
          name: 'extract_intent',
          description: 'Understand customer intent and needs',
          parameters: {
            type: 'object',
            properties: {
              primary_intent: {
                type: 'string',
                enum: [
                  'schedule_appointment',
                  'billing_inquiry',
                  'technical_support',
                  'general_question',
                  'cancel_service',
                  'product_inquiry',
                ],
                description: 'Main reason for the call',
              },
              urgency: {
                type: 'string',
                enum: ['low', 'medium', 'high', 'critical'],
                description: 'Urgency level of the request',
              },
              specific_request: {
                type: 'string',
                description: 'Detailed description of what they need',
              },
              confidence: {
                type: 'number',
                description: 'Confidence score 0-1',
              },
            },
            required: ['primary_intent'],
          },
        },
      },
      {
        type: 'function',
        function: {
          name: 'extract_appointment_request',
          description: 'Extract appointment scheduling requests',
          parameters: {
            type: 'object',
            properties: {
              preferred_date: {
                type: 'string',
                description: 'Preferred date mentioned',
              },
              preferred_time: {
                type: 'string',
                description: 'Preferred time mentioned',
              },
              service_type: {
                type: 'string',
                description: 'Type of service or appointment',
              },
              duration: { type: 'string', description: 'Expected duration' },
              flexibility: {
                type: 'string',
                enum: ['flexible', 'specific', 'urgent'],
                description: 'How flexible they are with timing',
              },
              confidence: {
                type: 'number',
                description: 'Confidence score 0-1',
              },
            },
            required: [],
          },
        },
      },
    ];
  }

  /**
   * Comprehensive analysis tools for post-call processing
   */
  private getComprehensiveAnalysisTools(): Array<{
    type: 'function';
    function: {
      name: string;
      description: string;
      parameters: {
        type: 'object';
        properties: Record<
          string,
          {
            type: string;
            description: string;
            enum?: string[];
            items?: { type: string };
          }
        >;
        required: string[];
      };
    };
  }> {
    return [
      ...this.getAIExtractionTools(),
      {
        type: 'function',
        function: {
          name: 'extract_sentiment_analysis',
          description: 'Comprehensive sentiment and satisfaction analysis',
          parameters: {
            type: 'object',
            properties: {
              overall_sentiment: {
                type: 'string',
                description: 'Overall sentiment of the conversation',
                enum: [
                  'very_positive',
                  'positive',
                  'neutral',
                  'negative',
                  'very_negative',
                ],
              },
              satisfaction_level: {
                type: 'string',
                description: 'Customer satisfaction level',
                enum: [
                  'very_satisfied',
                  'satisfied',
                  'neutral',
                  'dissatisfied',
                  'very_dissatisfied',
                ],
              },
              key_emotions: {
                type: 'array',
                items: { type: 'string' },
                description:
                  'Key emotions expressed (frustrated, happy, confused, etc.)',
              },
              confidence: {
                type: 'number',
                description: 'Confidence score 0-1',
              },
            },
            required: ['overall_sentiment', 'satisfaction_level'],
          },
        },
      },
      {
        type: 'function',
        function: {
          name: 'extract_resolution_status',
          description: 'Extract call resolution and outcome information',
          parameters: {
            type: 'object',
            properties: {
              issue_resolved: {
                type: 'boolean',
                description: 'Was the main issue resolved',
              },
              resolution_type: {
                type: 'string',
                description: 'Type of resolution achieved',
                enum: [
                  'fully_resolved',
                  'partially_resolved',
                  'requires_followup',
                  'escalated',
                  'unresolved',
                ],
              },
              next_steps: {
                type: 'array',
                items: { type: 'string' },
                description: 'Action items or next steps identified',
              },
              followup_required: {
                type: 'boolean',
                description: 'Does this require follow-up',
              },
              confidence: {
                type: 'number',
                description: 'Confidence score 0-1',
              },
            },
            required: ['issue_resolved', 'resolution_type'],
          },
        },
      },
    ];
  }

  /**
   * Process a function call and extract information
   */
  async processFunctionCall(
    call: Call,
    functionName: string,
    args: FunctionArguments,
    context?: string,
  ): Promise<ExtractedInformation[]> {
    const extractedItems: ExtractedInformation[] = [];

    try {
      switch (functionName) {
        case 'extract_contact_info':
          extractedItems.push(
            ...this.extractContactInfo(
              call,
              args as ExtractContactInfoArgs,
              context,
            ),
          );
          break;
        case 'extract_appointment_details':
        case 'extract_appointment_request':
          extractedItems.push(
            ...this.extractAppointmentDetails(
              call,
              args as ExtractAppointmentArgs,
              context,
            ),
          );
          break;
        case 'extract_problem_description':
          extractedItems.push(
            ...this.extractProblemDescription(
              call,
              args as ExtractProblemArgs,
              context,
            ),
          );
          break;
        case 'extract_sentiment':
        case 'extract_sentiment_analysis':
          extractedItems.push(
            ...this.extractSentiment(
              call,
              args as ExtractSentimentArgs,
              context,
            ),
          );
          break;
        case 'extract_action_items':
          extractedItems.push(
            ...this.extractActionItems(
              call,
              args as ExtractActionItemsArgs,
              context,
            ),
          );
          break;
        case 'extract_intent':
          extractedItems.push(
            ...this.extractIntent(call, args as ExtractIntentArgs, context),
          );
          break;
        case 'extract_resolution_status':
          extractedItems.push(
            ...this.extractResolutionStatus(
              call,
              args as ExtractResolutionArgs,
              context,
            ),
          );
          break;
        default:
          this.logger.warn(`Unknown function: ${functionName}`);
      }

      // Save extracted information to database
      await this.extractedInfoRepository.save(extractedItems);

      // Log extraction event in transcript
      await this.logExtractionEvent(
        call,
        functionName,
        args,
        extractedItems.length,
      );

      this.logger.log(
        `Extracted ${extractedItems.length} items from function ${functionName}`,
      );
      return extractedItems;
    } catch (error) {
      this.logger.error(
        `Error processing function call ${functionName}:`,
        error,
      );
      throw error;
    }
  }

  private extractContactInfo(
    call: Call,
    args: ExtractContactInfoArgs,
    context?: string,
  ): ExtractedInformation[] {
    const items: ExtractedInformation[] = [];

    if (args.name) {
      items.push(
        this.createExtractionItem(
          call,
          ExtractionType.CONTACT_INFO,
          'customer_name',
          args.name,
          args.confidence || 0.9,
          context,
        ),
      );
    }

    if (args.email) {
      items.push(
        this.createExtractionItem(
          call,
          ExtractionType.CONTACT_INFO,
          'email',
          args.email,
          args.confidence || 0.9,
          context,
        ),
      );
    }

    if (args.phone) {
      items.push(
        this.createExtractionItem(
          call,
          ExtractionType.CONTACT_INFO,
          'phone_number',
          args.phone,
          args.confidence || 0.9,
          context,
        ),
      );
    }

    if (args.address) {
      items.push(
        this.createExtractionItem(
          call,
          ExtractionType.CONTACT_INFO,
          'address',
          args.address,
          args.confidence || 0.8,
          context,
        ),
      );
    }

    return items;
  }

  private extractAppointmentDetails(
    call: Call,
    args: ExtractAppointmentArgs,
    context?: string,
  ): ExtractedInformation[] {
    const items: ExtractedInformation[] = [];

    if (args.datetime) {
      items.push(
        this.createExtractionItem(
          call,
          ExtractionType.APPOINTMENT,
          'appointment_datetime',
          new Date(args.datetime),
          args.confidence || 0.9,
          context,
        ),
      );
    }

    if (args.service_type) {
      items.push(
        this.createExtractionItem(
          call,
          ExtractionType.APPOINTMENT,
          'service_type',
          args.service_type,
          args.confidence || 0.8,
          context,
        ),
      );
    }

    if (args.duration) {
      items.push(
        this.createExtractionItem(
          call,
          ExtractionType.APPOINTMENT,
          'duration_minutes',
          typeof args.duration === 'string'
            ? parseInt(args.duration, 10)
            : args.duration,
          args.confidence || 0.8,
          context,
        ),
      );
    }

    return items;
  }

  private extractProblemDescription(
    call: Call,
    args: ExtractProblemArgs,
    context?: string,
  ): ExtractedInformation[] {
    const items: ExtractedInformation[] = [];

    if (args.problem) {
      items.push(
        this.createExtractionItem(
          call,
          ExtractionType.PROBLEM_DESCRIPTION,
          'problem_description',
          args.problem,
          args.confidence || 0.8,
          context,
        ),
      );
    }

    if (args.urgency) {
      items.push(
        this.createExtractionItem(
          call,
          ExtractionType.PROBLEM_DESCRIPTION,
          'urgency_level',
          args.urgency,
          args.confidence || 0.7,
          context,
        ),
      );
    }

    if (args.category) {
      items.push(
        this.createExtractionItem(
          call,
          ExtractionType.PROBLEM_DESCRIPTION,
          'problem_category',
          args.category,
          args.confidence || 0.7,
          context,
        ),
      );
    }

    return items;
  }

  private extractSentiment(
    call: Call,
    args: ExtractSentimentArgs,
    context?: string,
  ): ExtractedInformation[] {
    const items: ExtractedInformation[] = [];

    if (args.sentiment) {
      items.push(
        this.createExtractionItem(
          call,
          ExtractionType.SENTIMENT,
          'current_sentiment',
          {
            sentiment: args.sentiment || args.overall_sentiment || '',
            score: args.score || 0.5,
            reason: args.reason || '',
          },
          args.confidence || 0.8,
          context,
        ),
      );
    }

    return items;
  }

  private extractActionItems(
    call: Call,
    args: ExtractActionItemsArgs,
    context?: string,
  ): ExtractedInformation[] {
    const items: ExtractedInformation[] = [];

    if (args.action_items && Array.isArray(args.action_items)) {
      args.action_items.forEach((action: string, index: number) => {
        items.push(
          this.createExtractionItem(
            call,
            ExtractionType.ACTION_ITEMS,
            `action_item_${index + 1}`,
            action,
            args.confidence || 0.8,
            context,
          ),
        );
      });
    }

    return items;
  }

  private extractIntent(
    call: Call,
    args: ExtractIntentArgs,
    context?: string,
  ): ExtractedInformation[] {
    const items: ExtractedInformation[] = [];

    if (args.primary_intent) {
      items.push(
        this.createExtractionItem(
          call,
          ExtractionType.INTENT,
          'primary_intent',
          args.primary_intent,
          args.confidence || 0.8,
          context,
        ),
      );
    }

    if (args.urgency) {
      items.push(
        this.createExtractionItem(
          call,
          ExtractionType.INTENT,
          'urgency_level',
          args.urgency,
          args.confidence || 0.7,
          context,
        ),
      );
    }

    if (args.specific_request) {
      items.push(
        this.createExtractionItem(
          call,
          ExtractionType.INTENT,
          'specific_request',
          args.specific_request,
          args.confidence || 0.8,
          context,
        ),
      );
    }

    return items;
  }

  private extractResolutionStatus(
    call: Call,
    args: ExtractResolutionArgs,
    context?: string,
  ): ExtractedInformation[] {
    const items: ExtractedInformation[] = [];

    if (args.issue_resolved !== undefined) {
      items.push(
        this.createExtractionItem(
          call,
          ExtractionType.CUSTOM,
          'issue_resolved',
          args.issue_resolved,
          args.confidence || 0.9,
          context,
        ),
      );
    }

    if (args.resolution_type) {
      items.push(
        this.createExtractionItem(
          call,
          ExtractionType.CUSTOM,
          'resolution_type',
          args.resolution_type,
          args.confidence || 0.8,
          context,
        ),
      );
    }

    if (args.next_steps && Array.isArray(args.next_steps)) {
      items.push(
        this.createExtractionItem(
          call,
          ExtractionType.ACTION_ITEMS,
          'next_steps',
          args.next_steps,
          args.confidence || 0.8,
          context,
        ),
      );
    }

    return items;
  }

  private createExtractionItem(
    call: Call,
    type: ExtractionType,
    key: string,
    value: ExtractedValue,
    confidence: number,
    context?: string,
  ): ExtractedInformation {
    const item = new ExtractedInformation();
    item.call = call;
    item.extractionType = type;
    item.extractionSource = ExtractionSource.REAL_TIME;
    item.key = key;
    item.value = value;
    item.confidence = confidence;
    item.context = context || '';
    item.verified = false;
    return item;
  }

  private async logExtractionEvent(
    call: Call,
    functionName: string,
    args: FunctionArguments,
    itemCount: number,
  ): Promise<void> {
    const transcript = new CallTranscript();
    transcript.call = call;
    transcript.speaker = SpeakerType.SYSTEM;
    transcript.messageType = MessageType.FUNCTION_CALL;
    transcript.content = `Extracted ${itemCount} items via ${functionName}`;
    transcript.metadata = {
      functionName,
      arguments: args as Record<string, unknown>,
    };
    transcript.timestamp = new Date();

    await this.transcriptRepository.save(transcript);
  }

  // Schema definitions for different agent types
  private getCustomerServiceSchema(): ExtractionSchema {
    return {
      functions: [
        {
          name: 'extract_contact_info',
          description:
            'Extract customer contact information from the conversation',
          parameters: {
            type: 'object',
            properties: {
              name: { type: 'string', description: 'Customer full name' },
              email: { type: 'string', description: 'Customer email address' },
              phone: { type: 'string', description: 'Customer phone number' },
              address: { type: 'string', description: 'Customer address' },
              confidence: {
                type: 'number',
                description: 'Confidence score 0-1',
              },
            },
            required: [],
          },
        },
        {
          name: 'extract_problem_description',
          description: 'Extract details about the customer problem or issue',
          parameters: {
            type: 'object',
            properties: {
              problem: {
                type: 'string',
                description: 'Description of the problem',
              },
              category: {
                type: 'string',
                description: 'Problem category',
                enum: [
                  'technical',
                  'billing',
                  'account',
                  'product',
                  'service',
                  'other',
                ],
              },
              urgency: {
                type: 'string',
                description: 'Urgency level',
                enum: ['low', 'medium', 'high', 'critical'],
              },
              confidence: {
                type: 'number',
                description: 'Confidence score 0-1',
              },
            },
            required: ['problem'],
          },
        },
      ],
      instructions:
        'Extract customer contact information and problem details as the conversation progresses. Call functions when you have identified relevant information.',
    };
  }

  private getAppointmentBookingSchema(): ExtractionSchema {
    return {
      functions: [
        {
          name: 'extract_contact_info',
          description: 'Extract customer contact information',
          parameters: {
            type: 'object',
            properties: {
              name: { type: 'string', description: 'Customer full name' },
              email: { type: 'string', description: 'Customer email address' },
              phone: { type: 'string', description: 'Customer phone number' },
              confidence: {
                type: 'number',
                description: 'Confidence score 0-1',
              },
            },
            required: [],
          },
        },
        {
          name: 'extract_appointment_details',
          description: 'Extract appointment scheduling information',
          parameters: {
            type: 'object',
            properties: {
              datetime: {
                type: 'string',
                description: 'Preferred appointment date and time (ISO format)',
              },
              service_type: {
                type: 'string',
                description: 'Type of service requested',
              },
              duration: {
                type: 'string',
                description: 'Estimated duration in minutes',
              },
              confidence: {
                type: 'number',
                description: 'Confidence score 0-1',
              },
            },
            required: [],
          },
        },
      ],
      instructions:
        'Extract contact information and appointment details. Focus on gathering complete scheduling information including preferred dates, times, and service types.',
    };
  }

  private getSalesSchema(): ExtractionSchema {
    return {
      functions: [
        {
          name: 'extract_contact_info',
          description: 'Extract prospect contact information',
          parameters: {
            type: 'object',
            properties: {
              name: { type: 'string', description: 'Prospect full name' },
              email: { type: 'string', description: 'Prospect email address' },
              phone: { type: 'string', description: 'Prospect phone number' },
              company: { type: 'string', description: 'Company name' },
              title: { type: 'string', description: 'Job title' },
              confidence: {
                type: 'number',
                description: 'Confidence score 0-1',
              },
            },
            required: [],
          },
        },
        {
          name: 'extract_sentiment',
          description: 'Extract customer sentiment and interest level',
          parameters: {
            type: 'object',
            properties: {
              sentiment: {
                type: 'string',
                description: 'Overall sentiment',
                enum: ['positive', 'negative', 'neutral'],
              },
              score: { type: 'number', description: 'Sentiment score 0-1' },
              reason: {
                type: 'string',
                description: 'Reason for sentiment assessment',
              },
              confidence: {
                type: 'number',
                description: 'Confidence score 0-1',
              },
            },
            required: ['sentiment'],
          },
        },
      ],
      instructions:
        'Extract prospect information and gauge their interest level and sentiment throughout the conversation.',
    };
  }

  private getGenericSchema(): ExtractionSchema {
    return {
      functions: [
        {
          name: 'extract_contact_info',
          description: 'Extract contact information when mentioned',
          parameters: {
            type: 'object',
            properties: {
              name: { type: 'string', description: 'Person name' },
              email: { type: 'string', description: 'Email address' },
              phone: { type: 'string', description: 'Phone number' },
              confidence: {
                type: 'number',
                description: 'Confidence score 0-1',
              },
            },
            required: [],
          },
        },
        {
          name: 'extract_action_items',
          description:
            'Extract action items or tasks mentioned in the conversation',
          parameters: {
            type: 'object',
            properties: {
              action_items: {
                type: 'array',
                description: 'List of action items or follow-up tasks',
              },
              confidence: {
                type: 'number',
                description: 'Confidence score 0-1',
              },
            },
            required: ['action_items'],
          },
        },
      ],
      instructions:
        'Extract any contact information and action items mentioned during the conversation.',
    };
  }
}
