import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Call, CallStatus } from './entities/call.entity';
import { CallData } from './entities/call-data.entity';
import { Agent } from '../agents/entities/agent.entity';
import { v4 as uuidv4 } from 'uuid';
import {
  CustomerInfo,
  AppointmentDetails,
  RequestDetails,
} from './types/call-data.types';

@Injectable()
export class CallsService {
  constructor(
    @InjectRepository(Call)
    private callsRepository: Repository<Call>,
    @InjectRepository(CallData)
    private callDataRepository: Repository<CallData>,
    @InjectRepository(Agent)
    private agentRepository: Repository<Agent>,
  ) {}

  async create(callData: {
    twilioCallSid: string;
    callerNumber: string;
    callerName?: string;
    agent: Agent;
  }): Promise<Call> {
    const call = this.callsRepository.create({
      ...callData,
      status: CallStatus.ONGOING,
    });
    return this.callsRepository.save(call);
  }

  async findAll(agentId?: string): Promise<Call[]> {
    const query = this.callsRepository
      .createQueryBuilder('call')
      .leftJoinAndSelect('call.agent', 'agent')
      .leftJoinAndSelect('call.callData', 'callData')
      .leftJoinAndSelect('agent.user', 'user')
      .orderBy('call.createdAt', 'DESC');

    if (agentId) {
      query.where('agent.id = :agentId', { agentId });
    }

    return query.getMany();
  }

  async findOne(id: string): Promise<Call> {
    const call = await this.callsRepository.findOne({
      where: { id },
      relations: [
        'agent',
        'callData',
        'agent.user',
        'transcripts',
        'extractedInformation',
        'analysis',
      ],
      order: {
        transcripts: {
          timestamp: 'ASC',
        },
      },
    });

    if (!call) {
      throw new NotFoundException(`Call with ID ${id} not found`);
    }

    return call;
  }

  async findByTwilioCallSid(twilioCallSid: string): Promise<Call> {
    const call = await this.callsRepository.findOne({
      where: { twilioCallSid },
      relations: ['agent', 'callData', 'agent.user'],
    });

    if (!call) {
      throw new NotFoundException(
        `Call with Twilio SID ${twilioCallSid} not found`,
      );
    }

    return call;
  }

  async updateStatus(id: string, status: CallStatus): Promise<Call> {
    const call = await this.findOne(id);
    call.status = status;
    return this.callsRepository.save(call);
  }

  async updateCallData(
    callId: string,
    callDataUpdate: {
      customerInfo?: CustomerInfo;
      appointmentDetails?: AppointmentDetails;
      requestDetails?: RequestDetails;
      summary?: string;
      googleCalendarEventId?: string;
    },
  ): Promise<Call> {
    const call = await this.findOne(callId);

    // Create call data if it doesn't exist
    if (!call.callData) {
      const callData = this.callDataRepository.create(callDataUpdate);
      const savedCallData = await this.callDataRepository.save(callData);
      call.callData = savedCallData;
    } else {
      // Update existing call data
      Object.assign(call.callData, callDataUpdate);
      await this.callDataRepository.save(call.callData);
    }

    return this.callsRepository.save(call);
  }

  async updateCallRecording(id: string, recordingUrl: string): Promise<Call> {
    const call = await this.findOne(id);
    call.recordingUrl = recordingUrl;
    return this.callsRepository.save(call);
  }

  async updateCallTranscription(
    id: string,
    transcriptionUrl: string,
  ): Promise<Call> {
    const call = await this.findOne(id);
    call.transcriptionUrl = transcriptionUrl;
    return this.callsRepository.save(call);
  }

  async findByUserId(userId: string): Promise<Call[]> {
    return this.callsRepository
      .createQueryBuilder('call')
      .leftJoinAndSelect('call.agent', 'agent')
      .leftJoinAndSelect('call.callData', 'callData')
      .leftJoinAndSelect('agent.user', 'user')
      .where('user.id = :userId', { userId })
      .orderBy('call.createdAt', 'DESC')
      .getMany();
  }

  async seedCalls(
    userId: string,
    count: number,
    agentId?: string,
  ): Promise<Call[]> {
    // Find agents for the user
    let agents: Agent[];

    if (agentId) {
      // If agentId is provided, find that specific agent
      const agent = await this.agentRepository.findOne({
        where: { id: agentId, user: { id: userId } },
        relations: ['user'],
      });

      if (!agent) {
        throw new NotFoundException(
          `Agent with ID ${agentId} not found or does not belong to user`,
        );
      }

      agents = [agent];
    } else {
      // Otherwise, find all agents for the user
      agents = await this.agentRepository.find({
        where: { user: { id: userId } },
        relations: ['user'],
      });

      if (agents.length === 0) {
        throw new NotFoundException(`No agents found for user`);
      }
    }

    const phoneNumbers = [
      '+****************',
      '+****************',
      '+****************',
      '+****************',
      '+****************',
    ];

    const callerNames = [
      'John Doe',
      'Jane Smith',
      'Michael Johnson',
      'Emily Williams',
      'David Brown',
      null, // Some calls won't have a name
    ];

    const durations = [60, 120, 180, 240, 300, 360, 420, 480];
    const statuses = [CallStatus.COMPLETED, CallStatus.FAILED];
    const hasAppointment = [true, false];

    const calls: Call[] = [];

    // Create the specified number of calls
    for (let i = 0; i < count; i++) {
      // Randomly select an agent
      const agent = agents[Math.floor(Math.random() * agents.length)];

      // Randomly select a phone number and caller name
      const phoneNumber =
        phoneNumbers[Math.floor(Math.random() * phoneNumbers.length)];
      const callerName =
        callerNames[Math.floor(Math.random() * callerNames.length)];

      // Randomly select a duration and status
      const duration = durations[Math.floor(Math.random() * durations.length)];
      const status = statuses[Math.floor(Math.random() * statuses.length)];

      // Create a random date within the last 30 days
      const date = new Date();
      date.setDate(date.getDate() - Math.floor(Math.random() * 30));

      // Create the call
      const call = this.callsRepository.create({
        twilioCallSid: `CA${uuidv4().replace(/-/g, '')}`,
        callerNumber: phoneNumber,
        callerName: callerName || undefined,
        status,
        duration,
        agent,
        createdAt: date,
        updatedAt: date,
      });

      // Save the call
      const savedCall = await this.callsRepository.save(call);

      // Randomly decide if the call has appointment data
      if (hasAppointment[Math.floor(Math.random() * hasAppointment.length)]) {
        // Create call data with appointment details
        const appointmentDate = new Date();
        appointmentDate.setDate(
          date.getDate() + Math.floor(Math.random() * 14) + 1,
        ); // 1-14 days in the future

        const callData = this.callDataRepository.create({
          customerInfo: {
            name: callerName || 'Unknown',
            phone: phoneNumber,
            email: callerName
              ? `${callerName.toLowerCase().replace(' ', '.')}@example.com`
              : undefined,
          },
          appointmentDetails: {
            date: appointmentDate.toISOString(),
            service: ['Cleaning', 'Consultation', 'Follow-up', 'Emergency'][
              Math.floor(Math.random() * 4)
            ],
            duration: [30, 60, 90][Math.floor(Math.random() * 3)],
          },
          summary: `Call with ${callerName || 'unknown caller'} to schedule an appointment.`,
          googleCalendarEventId:
            Math.random() > 0.5 ? `event-${uuidv4()}` : undefined,
        });

        const savedCallData = await this.callDataRepository.save(callData);

        // Update the call with the call data
        savedCall.callData = savedCallData;
        await this.callsRepository.save(savedCall);
      }

      calls.push(savedCall);
    }

    return calls;
  }
}
