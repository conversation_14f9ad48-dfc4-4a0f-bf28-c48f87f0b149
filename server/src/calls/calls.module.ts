import { Modu<PERSON>, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CallsService } from './calls.service';
import { CallsController } from './calls.controller';
import { InformationExtractionService } from './services/information-extraction.service';
import { Call } from './entities/call.entity';
import { CallData } from './entities/call-data.entity';
import { CallTranscript } from './entities/call-transcript.entity';
import { ExtractedInformation } from './entities/extracted-information.entity';
import { CallAnalysis } from './entities/call-analysis.entity';
import { Agent } from '../agents/entities/agent.entity';
import { OpenaiModule } from '../integrations/openai/openai.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Call,
      CallData,
      CallTranscript,
      ExtractedInformation,
      CallAnalysis,
      Agent,
    ]),
    forwardRef(() => OpenaiModule),
  ],
  providers: [CallsService, InformationExtractionService],
  controllers: [CallsController],
  exports: [CallsService, InformationExtractionService],
})
export class CallsModule {}
