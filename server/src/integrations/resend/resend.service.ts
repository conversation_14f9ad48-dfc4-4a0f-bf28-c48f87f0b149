import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Resend } from 'resend';
import {
  CustomerInfo,
  AppointmentDetails,
  RequestDetails,
} from '../../calls/types/call-data.types';

@Injectable()
export class ResendService {
  private readonly resend: Resend;
  private readonly logger = new Logger(ResendService.name);

  constructor(private configService: ConfigService) {
    this.resend = new Resend(this.configService.get('RESEND_API_KEY'));
  }

  async sendCallSummaryEmail(
    to: string,
    callSummary: {
      id: string;
      date: string;
      callerName?: string;
      callerNumber: string;
      summary?: string;
      appointmentDetails?: AppointmentDetails;
      customerInfo?: CustomerInfo;
      requestDetails?: RequestDetails;
    },
  ) {
    try {
      const { data, error } = await this.resend.emails.send({
        from: '<EMAIL>',
        to,
        subject: `Call Summary - ${callSummary.date}`,
        html: this.generateCallSummaryHtml(callSummary),
      });

      if (error) {
        this.logger.error(`Error sending email: ${error.message}`);
        return { success: false, error };
      }

      return { success: true, data };
    } catch (error) {
      this.logger.error(
        `Error sending email: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  async sendAppointmentConfirmationEmail(
    to: string,
    appointment: {
      summary: string;
      date: string;
      startTime: string;
      endTime: string;
      description?: string;
      location?: string;
      calendarLink?: string;
    },
  ) {
    try {
      const { data, error } = await this.resend.emails.send({
        from: '<EMAIL>',
        to,
        subject: `Appointment Confirmation - ${appointment.summary}`,
        html: this.generateAppointmentConfirmationHtml(appointment),
      });

      if (error) {
        this.logger.error(`Error sending email: ${error.message}`);
        return { success: false, error };
      }

      return { success: true, data };
    } catch (error) {
      this.logger.error(
        `Error sending email: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  private generateCallSummaryHtml(callSummary: {
    id: string;
    date: string;
    callerName?: string;
    callerNumber: string;
    summary?: string;
    appointmentDetails?: AppointmentDetails;
    customerInfo?: CustomerInfo;
    requestDetails?: RequestDetails;
  }) {
    return `
      <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            h1 { color: #2c3e50; }
            h2 { color: #3498db; margin-top: 20px; }
            .section { margin-bottom: 20px; padding: 15px; background-color: #f9f9f9; border-radius: 5px; }
            .footer { margin-top: 30px; font-size: 12px; color: #7f8c8d; text-align: center; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>Call Summary</h1>
            <div class="section">
              <p><strong>Date:</strong> ${callSummary.date}</p>
              <p><strong>Caller:</strong> ${callSummary.callerName || 'Unknown'} (${callSummary.callerNumber})</p>
              ${callSummary.summary ? `<p><strong>Summary:</strong> ${callSummary.summary}</p>` : ''}
            </div>

            ${
              callSummary.customerInfo
                ? `
            <h2>Customer Information</h2>
            <div class="section">
              ${Object.entries(callSummary.customerInfo)
                .map(
                  ([key, value]) =>
                    `<p><strong>${key.charAt(0).toUpperCase() + key.slice(1)}:</strong> ${value}</p>`,
                )
                .join('')}
            </div>
            `
                : ''
            }

            ${
              callSummary.appointmentDetails
                ? `
            <h2>Appointment Details</h2>
            <div class="section">
              ${Object.entries(callSummary.appointmentDetails)
                .map(
                  ([key, value]) =>
                    `<p><strong>${key.charAt(0).toUpperCase() + key.slice(1)}:</strong> ${value}</p>`,
                )
                .join('')}
            </div>
            `
                : ''
            }

            ${
              callSummary.requestDetails
                ? `
            <h2>Request Details</h2>
            <div class="section">
              ${Object.entries(callSummary.requestDetails)
                .map(
                  ([key, value]) =>
                    `<p><strong>${key.charAt(0).toUpperCase() + key.slice(1)}:</strong> ${value}</p>`,
                )
                .join('')}
            </div>
            `
                : ''
            }

            <div class="footer">
              <p>This is an automated message from Talkloop. Please do not reply to this email.</p>
              <p>Call ID: ${callSummary.id}</p>
            </div>
          </div>
        </body>
      </html>
    `;
  }

  private generateAppointmentConfirmationHtml(appointment: {
    summary: string;
    date: string;
    startTime: string;
    endTime: string;
    description?: string;
    location?: string;
    calendarLink?: string;
  }) {
    return `
      <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            h1 { color: #2c3e50; }
            .section { margin-bottom: 20px; padding: 15px; background-color: #f9f9f9; border-radius: 5px; }
            .button { display: inline-block; padding: 10px 20px; background-color: #3498db; color: white; text-decoration: none; border-radius: 5px; }
            .footer { margin-top: 30px; font-size: 12px; color: #7f8c8d; text-align: center; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>Appointment Confirmation</h1>
            <div class="section">
              <p><strong>Appointment:</strong> ${appointment.summary}</p>
              <p><strong>Date:</strong> ${appointment.date}</p>
              <p><strong>Time:</strong> ${appointment.startTime} - ${appointment.endTime}</p>
              ${appointment.description ? `<p><strong>Description:</strong> ${appointment.description}</p>` : ''}
              ${appointment.location ? `<p><strong>Location:</strong> ${appointment.location}</p>` : ''}
            </div>

            ${
              appointment.calendarLink
                ? `
            <p style="text-align: center;">
              <a href="${appointment.calendarLink}" class="button">Add to Calendar</a>
            </p>
            `
                : ''
            }

            <div class="footer">
              <p>This is an automated message from Talkloop. Please do not reply to this email.</p>
            </div>
          </div>
        </body>
      </html>
    `;
  }
}
