# Realtime Voice Chat Integration

This module provides a flexible architecture for integrating real-time voice chat with AI providers. It currently supports OpenAI Realtime API and Google Gemini Live API with easy switching between providers.

## Architecture

### Provider Pattern
- **Abstract Interface**: `RealtimeProvider` defines common methods for all providers
- **OpenAI Provider**: `OpenAIRealtimeProvider` implements OpenAI Realtime API
- **Gemini Provider**: `GeminiLiveProvider` implements Google Gemini Live API
- **Factory**: `RealtimeProviderFactory` creates and manages provider instances

### Key Components
- **RealtimeService**: Main service for managing realtime sessions
- **Provider Interfaces**: Common interface for all AI providers
- **Audio Handling**: Standardized audio chunk processing
- **Session Management**: Automatic cleanup and state management

## Configuration

### Environment Variables
```bash
# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key

# Google AI Configuration  
GOOGLE_API_KEY=your-google-ai-api-key

# Default Provider (openai or gemini)
DEFAULT_REALTIME_PROVIDER=openai
```

### Provider Capabilities

#### OpenAI Realtime API
- **Model**: gpt-4o-realtime-preview-2024-10-01
- **Audio Formats**: PCM16, Opus
- **Voices**: alloy, echo, fable, onyx, nova, shimmer
- **Features**: Voice Activity Detection, Real-time transcription
- **Session Limit**: 15 minutes

#### Google Gemini Live API
- **Model**: gemini-2.0-flash-live-001
- **Audio Formats**: PCM, Opus
- **Voices**: Puck, Charon, Kore, Fenrir, Aoede, Leda, Orus, Zephyr
- **Features**: Real-time audio, Multi-language support
- **Session Limit**: 15 minutes

## Usage

### Basic Usage
```typescript
// Create a realtime session
const provider = await realtimeService.createSession(
  sessionId,
  socket,
  agentPrompt,
  'openai' // or 'gemini'
);

// Send audio
await realtimeService.sendAudio(sessionId, audioBuffer);

// Send text
await realtimeService.sendText(sessionId, "Hello!");

// End session
await realtimeService.endSession(sessionId);
```

### Frontend Integration
```typescript
// Start test with specific provider
await testingService.startTestCall(agentId, 'openai');

// The provider can be switched in the UI
<select value={provider} onChange={setProvider}>
  <option value="openai">OpenAI Realtime</option>
  <option value="gemini">Google Gemini Live</option>
</select>
```

## Adding New Providers

To add a new AI provider:

1. **Create Provider Class**
```typescript
export class NewProvider extends RealtimeProvider {
  async connect(): Promise<void> { /* implementation */ }
  async sendAudio(audioChunk: AudioChunk): Promise<void> { /* implementation */ }
  async sendText(text: string): Promise<void> { /* implementation */ }
  // ... other required methods
}
```

2. **Update Factory**
```typescript
// Add to RealtimeProviderFactory
case 'newprovider':
  return new NewProvider(sessionId, socket, config, this.configService);
```

3. **Update Types**
```typescript
export type ProviderType = 'openai' | 'gemini' | 'newprovider';
```

## Audio Processing

### Supported Formats
- **Input**: WebM/Opus from browser, PCM16, MP3
- **Output**: PCM16, Opus, MP3
- **Sample Rates**: 16kHz, 24kHz, 48kHz
- **Channels**: Mono (1 channel) recommended

### Audio Flow
```
Browser Microphone → WebM/Opus → WebSocket → Server → Provider API
Provider API → Audio Response → WebSocket → Browser → Audio Player
```

## Error Handling

The system includes comprehensive error handling:
- **Connection Errors**: Automatic retry and fallback
- **Audio Errors**: Graceful degradation to text-only
- **Provider Errors**: Error reporting and session cleanup
- **Network Errors**: Reconnection attempts

## Testing

### Manual Testing
1. Open the agent testing modal
2. Select your preferred provider (OpenAI or Gemini)
3. Click "Start Recording" to test voice input
4. Use text input as fallback
5. Monitor real-time responses

### Provider Switching
- Switch providers before starting a test session
- Each provider has different voices and capabilities
- Test with different agent prompts to see behavior differences

## Performance Considerations

- **Audio Chunking**: 100ms chunks for low latency
- **Session Limits**: 15-minute maximum per session
- **Concurrent Sessions**: Limited by API quotas
- **Memory Usage**: Automatic cleanup of ended sessions
- **Network**: WebSocket for real-time communication

## Security

- **API Keys**: Stored securely in environment variables
- **Authentication**: JWT-based user authentication
- **Rate Limiting**: Implemented at provider level
- **Data Privacy**: Audio data not stored permanently
