import { Injectable, Logger } from '@nestjs/common';
import { Socket } from 'socket.io';
import {
  RealtimeProvider,
  RealtimeConfig,
  AudioChunk,
  RealtimeMessage,
} from './interfaces/realtime-provider.interface';
import {
  RealtimeProviderFactory,
  ProviderType,
} from './realtime-provider.factory';

@Injectable()
export class RealtimeService {
  private readonly logger = new Logger(RealtimeService.name);
  private activeSessions = new Map<string, RealtimeProvider>();

  constructor(private realtimeProviderFactory: RealtimeProviderFactory) {}

  async createSession(
    sessionId: string,
    socket: Socket,
    agentPrompt: string,
    providerType?: ProviderType,
    userId?: string,
  ): Promise<RealtimeProvider> {
    try {
      // Use provided provider or default
      const provider =
        providerType || this.realtimeProviderFactory.getDefaultProvider();

      // Validate provider configuration
      if (!this.realtimeProviderFactory.validateProviderConfig(provider)) {
        throw new Error(`Provider ${provider} is not properly configured`);
      }

      const config: RealtimeConfig = {
        systemPrompt: agentPrompt,
        temperature: 0.8,
        maxTokens: 4096,
        audioFormat: 'pcm16',
        sampleRate: provider === 'gemini' ? 16000 : 24000, // Gemini uses 16kHz, OpenAI uses 24kHz
        userId: userId,
      };

      // Create provider instance
      const realtimeProvider = this.realtimeProviderFactory.createProvider(
        provider,
        sessionId,
        socket,
        config,
      );

      // Set up event handlers
      realtimeProvider.onMessage((message: RealtimeMessage) => {
        this.handleProviderMessage(sessionId, message, socket);
      });

      realtimeProvider.onError((error: Error) => {
        this.handleProviderError(sessionId, error, socket);
      });

      realtimeProvider.onSessionUpdate((session) => {
        this.logger.log(`Session ${sessionId} updated: ${session.status}`);
        socket.emit('session-update', session);
      });

      // Connect to the provider
      await realtimeProvider.connect();

      // Store active session
      this.activeSessions.set(sessionId, realtimeProvider);

      this.logger.log(`Created ${provider} realtime session: ${sessionId}`);
      return realtimeProvider;
    } catch (error) {
      this.logger.error(
        `Failed to create realtime session: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      throw error;
    }
  }

  async endSession(sessionId: string): Promise<void> {
    const provider = this.activeSessions.get(sessionId);
    if (provider) {
      await provider.disconnect();
      this.activeSessions.delete(sessionId);
      this.logger.log(`Ended realtime session: ${sessionId}`);
    }
  }

  async sendAudio(
    sessionId: string,
    audioData: Buffer,
    format = 'pcm16',
    sampleRate = 16000, // Default to 16kHz for Gemini
  ): Promise<void> {
    const provider = this.activeSessions.get(sessionId);
    if (!provider) {
      throw new Error(`No active session found: ${sessionId}`);
    }

    const audioChunk: AudioChunk = {
      data: audioData,
      format,
      sampleRate,
      channels: 1,
    };

    await provider.sendAudio(audioChunk);
  }

  async sendText(sessionId: string, text: string): Promise<void> {
    const provider = this.activeSessions.get(sessionId);
    if (!provider) {
      throw new Error(`No active session found: ${sessionId}`);
    }

    await provider.sendText(text);
  }

  getSession(sessionId: string): RealtimeProvider | undefined {
    return this.activeSessions.get(sessionId);
  }

  getActiveSessions(): string[] {
    return Array.from(this.activeSessions.keys());
  }

  getSupportedProviders(): ProviderType[] {
    return this.realtimeProviderFactory.getSupportedProviders();
  }

  getProviderCapabilities(providerType: ProviderType) {
    return this.realtimeProviderFactory.getProviderCapabilities(providerType);
  }

  private handleProviderMessage(
    sessionId: string,
    message: RealtimeMessage,
    socket: Socket,
  ): void {
    this.logger.debug(`Message from ${sessionId}: ${message.type}`);

    // Forward message to client
    socket.emit('agent-message', {
      type: message.type,
      content: message.content,
      audioData: message.audioData,
      timestamp: message.timestamp,
      metadata: message.metadata,
    });
  }

  private handleProviderError(
    sessionId: string,
    error: Error,
    socket: Socket,
  ): void {
    this.logger.error(
      `Error in session ${sessionId}: ${error.message}`,
      error.stack,
    );

    socket.emit('error', {
      message: 'Realtime provider error',
      error: error.message,
      sessionId,
    });
  }

  // Cleanup method for graceful shutdown
  async cleanup(): Promise<void> {
    const sessionIds = Array.from(this.activeSessions.keys());
    await Promise.all(
      sessionIds.map((sessionId) => this.endSession(sessionId)),
    );
    this.logger.log('All realtime sessions cleaned up');
  }
}
