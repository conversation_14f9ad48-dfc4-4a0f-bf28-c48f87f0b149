import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { RealtimeProviderFactory } from './realtime-provider.factory';
import { RealtimeService } from './realtime.service';
import { GoogleModule } from '../google/google.module';
import { CallsModule } from '../../calls/calls.module';

@Module({
  imports: [ConfigModule, GoogleModule, CallsModule],
  providers: [RealtimeProviderFactory, RealtimeService],
  exports: [RealtimeProviderFactory, RealtimeService],
})
export class RealtimeModule {}
