import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as WebSocket from 'ws';
import {
  RealtimeProvider,
  RealtimeMessage,
  RealtimeSession,
  RealtimeConfig,
  AudioChunk,
} from '../interfaces/realtime-provider.interface';
import { Socket } from 'socket.io';

// Gemini Live API message types
interface GeminiMessage {
  setupComplete?: boolean;
  serverContent?: GeminiServerContent;
  error?: GeminiError;
}

interface GeminiServerContent {
  modelTurn?: GeminiModelTurn;
  inputTranscription?: GeminiTranscription;
  outputTranscription?: GeminiTranscription;
  interrupted?: boolean;
  turnComplete?: boolean;
}

interface GeminiModelTurn {
  parts?: GeminiPart[];
}

interface GeminiPart {
  text?: string;
  inlineData?: GeminiInlineData;
}

interface GeminiInlineData {
  data?: string;
  mimeType?: string;
}

interface GeminiTranscription {
  text?: string;
}

interface GeminiError {
  message?: string;
}

@Injectable()
export class GeminiLiveProvider extends RealtimeProvider {
  private ws: WebSocket | null = null;
  private messageCallback: ((message: RealtimeMessage) => void) | null = null;
  private errorCallback: ((error: Error) => void) | null = null;
  private sessionCallback: ((session: RealtimeSession) => void) | null = null;
  private apiKey: string;

  constructor(
    sessionId: string,
    socket: Socket,
    config: RealtimeConfig,
    private configService: ConfigService,
  ) {
    super(sessionId, socket, config);

    const apiKey = this.configService.get<string>('GOOGLE_API_KEY');
    if (!apiKey) {
      throw new Error('GOOGLE_API_KEY is not configured');
    }
    this.apiKey = apiKey;

    this.session = {
      id: sessionId,
      agentId: config.model || 'gemini-2.0-flash-live-001',
      userId: '',
      status: 'connecting',
      provider: 'gemini',
      startedAt: new Date(),
    };
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.logInfo(
          `Connecting to Gemini Live API for session ${this.sessionId}`,
        );

        // Gemini Live API WebSocket URL
        const url = `wss://generativelanguage.googleapis.com/ws/google.ai.generativelanguage.v1alpha.GenerativeService.BidiGenerateContent?key=${this.apiKey}`;

        this.ws = new WebSocket(url);

        this.ws.on('open', () => {
          this.logInfo('Connected to Gemini Live API');
          this.session.status = 'connected';
          this.sessionCallback?.(this.session);
          this.initializeSession();
          resolve();
        });

        this.ws.on('message', (data: WebSocket.Data) => {
          const message =
            data instanceof Buffer
              ? data.toString('utf8')
              : typeof data === 'string'
                ? data
                : JSON.stringify(data);
          this.handleMessage(message);
        });

        this.ws.on('error', (error) => {
          this.logError('Gemini WebSocket error:', error);
          this.session.status = 'error';
          this.errorCallback?.(error);
          reject(error);
        });

        this.ws.on('close', () => {
          this.logInfo('Gemini WebSocket connection closed');
          this.session.status = 'disconnected';
          this.session.endedAt = new Date();
          this.sessionCallback?.(this.session);
        });
      } catch (error) {
        this.logError('Failed to connect to Gemini Live API:', error);
        this.session.status = 'error';
        reject(error instanceof Error ? error : new Error(String(error)));
      }
    });
  }

  disconnect(): Promise<void> {
    return Promise.resolve().then(() => {
      if (this.ws) {
        this.ws.close();
        this.ws = null;
      }
      this.session.status = 'disconnected';
      this.session.endedAt = new Date();
    });
  }

  sendAudio(audioChunk: AudioChunk): Promise<void> {
    return Promise.resolve().then(() => {
      if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
        throw new Error('WebSocket not connected');
      }

      // Convert audio to base64 for Gemini
      const base64Audio = audioChunk.data.toString('base64');

      // Use the CORRECT format with snake_case (like the working example)
      const message = {
        realtime_input: {
          // snake_case!
          media_chunks: [
            // Use media_chunks array format
            {
              data: base64Audio,
              mime_type: 'audio/pcm', // snake_case, no rate in mime_type
            },
          ],
        },
      };

      this.logInfo(
        `Sending audio chunk: ${audioChunk.data.length} bytes, ${audioChunk.sampleRate}Hz`,
      );
      this.ws.send(JSON.stringify(message));
    });
  }

  sendText(text: string): Promise<void> {
    return Promise.resolve().then(() => {
      if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
        throw new Error('WebSocket not connected');
      }

      // Use the CORRECT format with snake_case (like the working example)
      const message = {
        client_content: {
          // snake_case!
          turns: [
            {
              role: 'user',
              parts: [
                {
                  text: text,
                },
              ],
            },
          ],
          turn_complete: true, // snake_case!
        },
      };

      this.logInfo(`Sending text message: "${text}"`);
      this.ws.send(JSON.stringify(message));
    });
  }

  onMessage(callback: (message: RealtimeMessage) => void): void {
    this.messageCallback = callback;
  }

  onError(callback: (error: Error) => void): void {
    this.errorCallback = callback;
  }

  onSessionUpdate(callback: (session: RealtimeSession) => void): void {
    this.sessionCallback = callback;
  }

  private initializeSession(): void {
    if (!this.ws) return;

    // Configure Gemini Live session using the CORRECT format (snake_case)
    const setupMessage = {
      setup: {
        model: this.config.model || 'models/gemini-2.0-flash-live-001',
        generationConfig: {
          responseModalities: ['AUDIO'], // Focus on audio for now
          speechConfig: {
            voice_config: {
              // snake_case!
              prebuilt_voice_config: {
                // snake_case!
                voice_name: this.config.voice || 'Kore', // snake_case!
              },
            },
          },
        },
        systemInstruction: {
          parts: [
            {
              text: this.config.systemPrompt || 'You are a helpful assistant.',
            },
          ],
        },
        tools: [],
      },
    };

    this.logInfo(
      'Sending setup message:',
      JSON.stringify(setupMessage, null, 2),
    );
    this.ws.send(JSON.stringify(setupMessage));
  }

  private handleMessage(data: string): void {
    try {
      const message = JSON.parse(data) as GeminiMessage;

      // Log all messages for debugging
      this.logInfo(
        'Received Gemini message:',
        JSON.stringify(message, null, 2),
      );

      if (message.setupComplete) {
        this.logInfo(
          'Gemini session setup complete - ready for audio/text input',
        );
        return;
      }

      if (message.serverContent) {
        const serverContent = message.serverContent;

        // Handle model turn (AI response)
        if (serverContent.modelTurn) {
          const modelTurn = serverContent.modelTurn;

          for (const part of modelTurn.parts || []) {
            if (part.text) {
              this.messageCallback?.({
                type: 'text',
                content: part.text,
                timestamp: new Date(),
              });
            }

            if (
              part.inlineData &&
              part.inlineData.mimeType?.startsWith('audio/')
            ) {
              this.logInfo(
                `Received audio data: ${part.inlineData.data?.length || 0} chars, mime: ${part.inlineData.mimeType}`,
              );
              this.messageCallback?.({
                type: 'audio',
                audioData: part.inlineData.data,
                timestamp: new Date(),
              });
            }
          }
        }

        // Handle input transcription
        if (serverContent.inputTranscription) {
          this.logInfo(
            'User speech transcribed:',
            serverContent.inputTranscription.text,
          );
        }

        // Handle output transcription
        if (serverContent.outputTranscription) {
          this.logInfo(
            'AI speech transcribed:',
            serverContent.outputTranscription.text,
          );
        }

        // Handle interruption
        if (serverContent.interrupted) {
          this.logInfo('Generation was interrupted');
          // Send interruption signal to frontend
          this.messageCallback?.({
            type: 'speech_started',
            content: 'Generation was interrupted by user speech',
            timestamp: new Date(),
          });
        }

        // Handle turn completion
        if (serverContent.turnComplete) {
          this.logInfo('Turn completed');
          // Send completion signal to frontend
          this.messageCallback?.({
            type: 'completion',
            content: 'turn_complete',
            timestamp: new Date(),
          });
        }
      }

      if (message.error) {
        this.logError('Gemini API error:', message.error);
        this.errorCallback?.(
          new Error(message.error.message || 'Unknown error'),
        );
      }
    } catch (error) {
      this.logError('Error parsing message:', error);
    }
  }
}
