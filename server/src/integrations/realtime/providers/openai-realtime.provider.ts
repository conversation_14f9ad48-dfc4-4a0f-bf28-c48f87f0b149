import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as WebSocket from 'ws';
import {
  RealtimeProvider,
  RealtimeMessage,
  RealtimeSession,
  RealtimeConfig,
  AudioChunk,
} from '../interfaces/realtime-provider.interface';
import { Socket } from 'socket.io';
import { GoogleService, AppointmentRequest } from '../../google/google.service';
import { InformationExtractionService } from '../../../calls/services/information-extraction.service';
import {
  OpenAIMessage,
  OpenAIFunctionCallArgumentsDone,
  OpenAIError,
  CheckAvailabilityArgs,
  BookAppointmentArgs,
  SuggestAppointmentTimesArgs,
  SaveCustomerInfoArgs,
  FunctionCallResult,
  ToolDefinition,
  SessionConfig,
  OpenAITextDelta,
  OpenAIAudioDelta,
  OpenAITranscriptionCompleted,
} from '../types/openai-types';

@Injectable()
export class OpenAIRealtimeProvider extends RealtimeProvider {
  private ws: WebSocket | null = null;
  private messageCallback: ((message: RealtimeMessage) => void) | null = null;
  private errorCallback: ((error: Error) => void) | null = null;
  private sessionCallback: ((session: RealtimeSession) => void) | null = null;
  private apiKey: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 3;

  constructor(
    sessionId: string,
    socket: Socket,
    config: RealtimeConfig,
    private configService: ConfigService,
    private googleService?: GoogleService,
    private informationExtractionService?: InformationExtractionService,
  ) {
    super(sessionId, socket, config);

    const apiKey = this.configService.get<string>('OPENAI_API_KEY');
    if (!apiKey) {
      throw new Error('OPENAI_API_KEY is not configured');
    }
    if (!apiKey.startsWith('sk-')) {
      throw new Error(
        'OPENAI_API_KEY appears to be invalid (should start with sk-)',
      );
    }

    // Additional validation for API key format
    if (apiKey.length < 50) {
      throw new Error(
        'OPENAI_API_KEY appears to be too short - check if it was truncated',
      );
    }

    this.apiKey = apiKey;

    this.session = {
      id: sessionId,
      agentId: config.model || 'gpt-4o-realtime-preview',
      userId: '',
      status: 'connecting',
      provider: 'openai',
      startedAt: new Date(),
    };
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.logInfo(
          `Connecting to OpenAI Realtime API for session ${this.sessionId}`,
        );
        this.logInfo(
          `Using API key: ${this.apiKey.substring(0, 7)}...${this.apiKey.substring(this.apiKey.length - 4)}`,
        );

        // OpenAI Realtime API WebSocket URL - using latest model
        const url =
          'wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview';

        this.logInfo(`Connecting to: ${url}`);

        this.ws = new WebSocket(url, {
          headers: {
            Authorization: `Bearer ${this.apiKey}`,
            'OpenAI-Beta': 'realtime=v1',
          },
          handshakeTimeout: 30000, // 30 second timeout
        });

        // Set up a connection timeout
        const connectionTimeout = setTimeout(() => {
          if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
            this.logError('Connection timeout - closing WebSocket');
            this.ws.close();
            reject(new Error('Connection timeout'));
          }
        }, 30000);

        this.ws.on('open', () => {
          clearTimeout(connectionTimeout);
          this.logInfo('Connected to OpenAI Realtime API successfully');
          this.session.status = 'connected';
          this.sessionCallback?.(this.session);

          // Initialize session after a small delay to ensure connection is stable
          setTimeout(() => {
            this.initializeSession();
          }, 100);

          resolve();
        });

        this.ws.on('message', (data: WebSocket.Data) => {
          const message =
            data instanceof Buffer
              ? data.toString('utf8')
              : typeof data === 'string'
                ? data
                : JSON.stringify(data);
          this.handleMessage(message);
        });

        this.ws.on('error', (error) => {
          clearTimeout(connectionTimeout);
          this.logError('OpenAI WebSocket error:', error);
          this.session.status = 'error';
          this.errorCallback?.(error);
          reject(error);
        });

        this.ws.on('close', (code: number, reason: Buffer) => {
          const reasonString = reason.toString();
          this.logInfo(
            `OpenAI WebSocket connection closed - Code: ${code}, Reason: ${reasonString}`,
          );
          this.session.status = 'disconnected';
          this.session.endedAt = new Date();
          this.sessionCallback?.(this.session);

          // Log additional details for debugging
          if (code !== 1000) {
            // 1000 is normal closure
            this.logError(
              `Unexpected WebSocket closure - Code: ${code}, Reason: ${reasonString}`,
            );

            // Attempt reconnection for certain error codes
            if (
              code === 1006 &&
              this.reconnectAttempts < this.maxReconnectAttempts
            ) {
              this.reconnectAttempts++;
              this.logInfo(
                `Attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts}`,
              );
              setTimeout(() => {
                this.connect().catch((error) => {
                  this.logError('Reconnection failed:', error);
                });
              }, 1000 * this.reconnectAttempts); // Exponential backoff
            }
          }
        });
      } catch (error) {
        this.logError('Failed to connect to OpenAI Realtime API:', error);
        this.session.status = 'error';
        reject(error instanceof Error ? error : new Error(String(error)));
      }
    });
  }

  disconnect(): Promise<void> {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.session.status = 'disconnected';
    this.session.endedAt = new Date();
    return Promise.resolve();
  }

  sendAudio(audioChunk: AudioChunk): Promise<void> {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      this.logError(
        `Cannot send audio - WebSocket state: ${this.ws?.readyState || 'null'}`,
      );
      return Promise.reject(new Error('WebSocket not connected'));
    }

    // Convert audio to base64 for OpenAI
    const base64Audio = audioChunk.data.toString('base64');

    const message = {
      type: 'input_audio_buffer.append',
      audio: base64Audio,
    };

    this.ws.send(JSON.stringify(message));
    return Promise.resolve();
  }

  sendText(text: string): Promise<void> {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      this.logError(
        `Cannot send text - WebSocket state: ${this.ws?.readyState || 'null'}`,
      );
      return Promise.reject(new Error('WebSocket not connected'));
    }

    const message = {
      type: 'conversation.item.create',
      item: {
        type: 'message',
        role: 'user',
        content: [
          {
            type: 'text',
            text: text,
          },
        ],
      },
    };

    this.ws.send(JSON.stringify(message));

    // Trigger response
    const responseMessage = {
      type: 'response.create',
      response: {
        modalities: ['text', 'audio'],
        instructions:
          this.config.systemPrompt || 'You are a helpful assistant.',
      },
    };

    this.ws.send(JSON.stringify(responseMessage));
    return Promise.resolve();
  }

  onMessage(callback: (message: RealtimeMessage) => void): void {
    this.messageCallback = callback;
  }

  onError(callback: (error: Error) => void): void {
    this.errorCallback = callback;
  }

  onSessionUpdate(callback: (session: RealtimeSession) => void): void {
    this.sessionCallback = callback;
  }

  isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }

  getConnectionState(): string {
    if (!this.ws) return 'null';
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'connecting';
      case WebSocket.OPEN:
        return 'open';
      case WebSocket.CLOSING:
        return 'closing';
      case WebSocket.CLOSED:
        return 'closed';
      default:
        return 'unknown';
    }
  }

  private initializeSession(): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      this.logError('Cannot initialize session - WebSocket not open');
      return;
    }

    try {
      this.logInfo('Initializing OpenAI session...');

      // Configure session with function calling
      const sessionConfig: SessionConfig = {
        type: 'session.update',
        session: {
          modalities: ['text', 'audio'],
          instructions: this.buildSystemPrompt(),
          voice: this.config.voice || 'alloy',
          input_audio_format: 'pcm16',
          output_audio_format: 'pcm16',
          input_audio_transcription: {
            model: 'whisper-1',
          },
          turn_detection: {
            type: 'server_vad',
            threshold: 0.5, // More conservative threshold
            prefix_padding_ms: 300, // More padding for stability
            silence_duration_ms: 500, // Longer silence duration for stability
          },
          temperature: this.config.temperature || 0.8,
          max_response_output_tokens: this.config.maxTokens || 4096,
          tools: this.getToolDefinitions(),
        },
      };

      this.logInfo('Sending session configuration...');
      this.ws.send(JSON.stringify(sessionConfig));
      this.logInfo('Session configuration sent successfully');
    } catch (error) {
      this.logError('Error initializing session:', error);
    }
  }

  private buildSystemPrompt(): string {
    let prompt = this.config.systemPrompt || 'You are a helpful assistant.';

    if (this.googleService) {
      prompt += `\n\nYou have access to calendar functions to check availability and book appointments. When a customer wants to schedule an appointment:
1. First ask what type of service they need
2. Ask for their preferred date and time
3. Use check_availability to see what's available
4. Offer them specific time slots
5. Once they confirm, use book_appointment to schedule it
6. Always confirm the appointment details after booking`;
    }

    if (this.informationExtractionService) {
      prompt += `\n\nYou automatically extract and save important information during conversations. This includes contact details, appointment requests, and customer problems. Continue the conversation naturally while the system captures this information in the background.`;
    }

    return prompt;
  }

  private getToolDefinitions(): ToolDefinition[] {
    const tools: ToolDefinition[] = [];

    // Calendar functions
    if (this.googleService) {
      this.logInfo('Adding calendar functions to OpenAI session');
      tools.push(
        {
          type: 'function',
          name: 'check_availability',
          description: 'Check available appointment slots for a specific date',
          parameters: {
            type: 'object',
            properties: {
              date: {
                type: 'string',
                description: 'Date to check in YYYY-MM-DD format',
              },
              duration: {
                type: 'number',
                description: 'Appointment duration in minutes (default: 60)',
              },
              time_preference: {
                type: 'string',
                enum: ['morning', 'afternoon', 'evening', 'any'],
                description: 'Preferred time of day',
              },
            },
            required: ['date'],
          },
        },
        {
          type: 'function',
          name: 'book_appointment',
          description: 'Book a confirmed appointment slot',
          parameters: {
            type: 'object',
            properties: {
              customer_name: {
                type: 'string',
                description: 'Customer full name',
              },
              customer_email: {
                type: 'string',
                description: 'Customer email address',
              },
              service_type: {
                type: 'string',
                description: 'Type of service or appointment',
              },
              preferred_date: {
                type: 'string',
                description: 'Preferred date in YYYY-MM-DD format',
              },
              preferred_time: {
                type: 'string',
                description: 'Preferred time in HH:MM format (24-hour)',
              },
              duration: {
                type: 'number',
                description: 'Appointment duration in minutes',
              },
              notes: {
                type: 'string',
                description: 'Any additional notes or requirements',
              },
            },
            required: [
              'customer_name',
              'customer_email',
              'service_type',
              'duration',
            ],
          },
        },
        {
          type: 'function',
          name: 'suggest_appointment_times',
          description: 'Get suggested appointment times for the next few days',
          parameters: {
            type: 'object',
            properties: {
              duration: {
                type: 'number',
                description: 'Appointment duration in minutes',
              },
              time_preference: {
                type: 'string',
                enum: ['morning', 'afternoon', 'evening', 'any'],
                description: 'Preferred time of day',
              },
              days_ahead: {
                type: 'number',
                description: 'Number of days to look ahead (default: 7)',
              },
            },
            required: ['duration'],
          },
        },
      );
      this.logInfo(
        `Added ${tools.length} calendar functions to OpenAI session`,
      );
    } else {
      this.logInfo('GoogleService not available - no calendar functions added');
    }

    // Information extraction functions (if needed for real-time extraction)
    if (this.informationExtractionService) {
      tools.push({
        type: 'function',
        name: 'save_customer_info',
        description:
          'Save important customer information mentioned during the conversation',
        parameters: {
          type: 'object',
          properties: {
            name: { type: 'string', description: 'Customer name' },
            email: { type: 'string', description: 'Customer email' },
            phone: { type: 'string', description: 'Customer phone number' },
            company: { type: 'string', description: 'Customer company' },
          },
          required: [],
        },
      });
    }

    this.logInfo(`Total tools configured: ${tools.length}`);
    return tools;
  }

  private handleMessage(data: string): void {
    try {
      const message: OpenAIMessage = JSON.parse(data) as OpenAIMessage;

      // Log all messages for debugging
      this.logInfo('Received message:', message.type);

      switch (message.type) {
        case 'session.created':
          this.logInfo('OpenAI session created');
          break;

        case 'session.updated':
          this.logInfo('OpenAI session updated');
          break;

        case 'response.created':
          this.logInfo('Response created:', message.response?.id);
          break;

        case 'response.done':
          this.logInfo('Response completed:', message.response?.id);
          break;

        case 'response.text.delta': {
          const textDelta = message as OpenAITextDelta;
          this.logInfo('Text delta received:', textDelta.delta);
          this.messageCallback?.({
            type: 'text',
            content: textDelta.delta,
            timestamp: new Date(),
          });
          break;
        }

        case 'response.text.done':
          this.logInfo('Text response completed');
          break;

        case 'response.audio.delta': {
          const audioDelta = message as OpenAIAudioDelta;
          this.logInfo(
            'Audio delta received, length:',
            audioDelta.delta?.length || 0,
          );
          this.messageCallback?.({
            type: 'audio',
            audioData: audioDelta.delta,
            timestamp: new Date(),
          });
          break;
        }

        case 'response.audio.done':
          this.logInfo('Audio response completed');
          break;

        case 'conversation.item.created':
          this.logInfo('Conversation item created:', message.item?.type);
          break;

        case 'conversation.item.input_audio_transcription.completed': {
          const transcription = message as OpenAITranscriptionCompleted;
          this.logInfo('User speech transcribed:', transcription.transcript);
          break;
        }

        case 'input_audio_buffer.committed':
          this.logInfo('Audio buffer committed');
          break;

        case 'input_audio_buffer.cleared':
          this.logInfo('Audio buffer cleared');
          break;

        case 'input_audio_buffer.speech_started':
          this.logInfo('User started speaking');
          this.messageCallback?.({
            type: 'speech_started',
            content: 'User started speaking',
            timestamp: new Date(),
          });
          break;

        case 'input_audio_buffer.speech_stopped':
          this.logInfo('User stopped speaking');
          this.messageCallback?.({
            type: 'speech_stopped',
            content: 'User stopped speaking',
            timestamp: new Date(),
          });
          break;

        case 'response.function_call_arguments.delta':
          this.logInfo('Function call arguments delta:', message.delta);
          break;

        case 'response.function_call_arguments.done':
          this.logInfo('Function call arguments completed');
          // Handle function call
          void this.handleFunctionCall(
            message as OpenAIFunctionCallArgumentsDone,
          );
          break;

        case 'error': {
          const errorMessage = message as OpenAIError;
          this.logError('OpenAI API error:', errorMessage.error);

          // Check if this is an authentication error
          if (
            errorMessage.error.code === 'invalid_api_key' ||
            errorMessage.error.code === 'authentication_error'
          ) {
            this.logError('Authentication failed - check your OpenAI API key');
          }

          this.errorCallback?.(
            new Error(errorMessage.error.message || 'Unknown OpenAI error'),
          );
          break;
        }

        default:
          this.logInfo('Unhandled message type:', message.type, message);
      }
    } catch (error) {
      this.logError('Error parsing message:', error);
    }
  }

  private async handleFunctionCall(
    message: OpenAIFunctionCallArgumentsDone,
  ): Promise<void> {
    try {
      const functionName = message.name;
      const args = JSON.parse(message.arguments || '{}') as Record<
        string,
        unknown
      >;

      this.logInfo(`Executing function: ${functionName}`, args);

      let result: FunctionCallResult;

      switch (functionName) {
        case 'check_availability':
          result = await this.handleCheckAvailability(
            args as unknown as CheckAvailabilityArgs,
          );
          break;
        case 'book_appointment':
          result = await this.handleBookAppointment(
            args as unknown as BookAppointmentArgs,
          );
          break;
        case 'suggest_appointment_times':
          result = await this.handleSuggestAppointmentTimes(
            args as unknown as SuggestAppointmentTimesArgs,
          );
          break;
        case 'save_customer_info':
          result = await this.handleSaveCustomerInfo(
            args as unknown as SaveCustomerInfoArgs,
          );
          break;
        default:
          result = { error: `Unknown function: ${functionName}` };
      }

      // Send function result back to OpenAI
      const functionResponse = {
        type: 'conversation.item.create',
        item: {
          type: 'function_call_output',
          call_id: message.call_id,
          output: JSON.stringify(result),
        },
      };

      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify(functionResponse));
      }
    } catch (error) {
      this.logError('Error handling function call:', error);

      // Send error response
      const errorResponse = {
        type: 'conversation.item.create',
        item: {
          type: 'function_call_output',
          call_id: message.call_id,
          output: JSON.stringify({ error: (error as Error).message }),
        },
      };

      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify(errorResponse));
      }
    }
  }

  private async handleCheckAvailability(
    args: CheckAvailabilityArgs,
  ): Promise<FunctionCallResult> {
    if (!this.googleService) {
      return { error: 'Calendar service not available' };
    }

    try {
      const { date, duration = 60, time_preference } = args;

      // We need a way to get the user ID - this should come from the session context
      // For now, we'll need to pass this from the testing service

      const userId = this.config.userId || '';

      const slots = await this.googleService.getAvailableSlots(
        userId,
        date,
        duration,
        time_preference,
      );

      // Type guard to ensure slots is an array
      if (!Array.isArray(slots)) {
        return { error: 'Invalid response from calendar service' };
      }

      return {
        date,

        available_slots: slots.map((slot) => ({
          start_time: slot.startTime.toISOString(),

          end_time: slot.endTime.toISOString(),

          duration: slot.duration,

          formatted_time: slot.startTime.toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true,
          }),
        })),

        total_slots: slots.length,
      };
    } catch (error) {
      return {
        error: `Failed to check availability: ${(error as Error).message}`,
      };
    }
  }

  private async handleBookAppointment(
    args: BookAppointmentArgs,
  ): Promise<FunctionCallResult> {
    if (!this.googleService) {
      return { error: 'Calendar service not available' };
    }

    try {
      const userId = this.config.userId || '';

      const appointmentRequest: AppointmentRequest = {
        customerName: args.customer_name,
        customerEmail: args.customer_email,
        serviceType: args.service_type,
        preferredDate: args.preferred_date,
        preferredTime: args.preferred_time,
        duration: args.duration,
        notes: args.notes,
      };

      const event = await this.googleService.bookAppointment(
        userId,
        appointmentRequest,
      );

      return {
        success: true,
        appointment: {
          id: event.id || undefined,

          summary: event.summary || undefined,

          start_time: event.start?.dateTime || undefined,

          end_time: event.end?.dateTime || undefined,
          customer_name: args.customer_name,
          customer_email: args.customer_email,
          service_type: args.service_type,
        },
        message: `Appointment successfully booked for ${args.customer_name} on ${args.preferred_date} at ${args.preferred_time}`,
      };
    } catch (error) {
      return {
        error: `Failed to book appointment: ${(error as Error).message}`,
      };
    }
  }

  private async handleSuggestAppointmentTimes(
    args: SuggestAppointmentTimesArgs,
  ): Promise<FunctionCallResult> {
    if (!this.googleService) {
      return { error: 'Calendar service not available' };
    }

    try {
      const { duration = 60, time_preference, days_ahead = 7 } = args;

      const userId = this.config.userId || '';

      const slots = await this.googleService.getSuggestedAppointmentTimes(
        userId,
        duration,
        time_preference,
        days_ahead,
      );

      // Type guard to ensure slots is an array
      if (!Array.isArray(slots)) {
        return { error: 'Invalid response from calendar service' };
      }

      return {
        suggested_times: slots.map((slot) => ({
          date: slot.startTime.toDateString(),

          time: slot.startTime.toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true,
          }),

          start_time: slot.startTime.toISOString(),

          end_time: slot.endTime.toISOString(),

          duration: slot.duration,
        })),

        total_suggestions: slots.length,
      };
    } catch (error) {
      return {
        error: `Failed to get suggestions: ${(error as Error).message}`,
      };
    }
  }

  private handleSaveCustomerInfo(
    args: SaveCustomerInfoArgs,
  ): Promise<FunctionCallResult> {
    if (!this.informationExtractionService) {
      return Promise.resolve({
        error: 'Information extraction service not available',
      });
    }

    try {
      // This would need to be integrated with the current call context
      // For now, we'll return a success message
      return Promise.resolve({
        success: true,
        saved_info: args,
        message: 'Customer information saved successfully',
      });
    } catch (error) {
      return Promise.resolve({
        error: `Failed to save customer info: ${(error as Error).message}`,
      });
    }
  }
}
