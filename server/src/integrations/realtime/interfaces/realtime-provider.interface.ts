import { Socket } from 'socket.io';

export interface RealtimeMessage {
  type:
    | 'text'
    | 'audio'
    | 'error'
    | 'session_started'
    | 'session_ended'
    | 'completion'
    | 'speech_started'
    | 'speech_stopped';
  content?: string;
  audioData?: string | Buffer;
  timestamp: Date;
  metadata?: Record<string, unknown>;
}

export interface RealtimeSession {
  id: string;
  agentId: string;
  userId: string;
  status: 'connecting' | 'connected' | 'disconnected' | 'error';
  provider: 'openai' | 'gemini';
  startedAt: Date;
  endedAt?: Date;
}

export interface RealtimeConfig {
  model?: string;
  voice?: string;
  language?: string;
  systemPrompt?: string;
  temperature?: number;
  maxTokens?: number;
  audioFormat?: 'pcm16' | 'opus' | 'mp3';
  sampleRate?: number;
  userId?: string;
}

export interface AudioChunk {
  data: Buffer;
  format: string;
  sampleRate: number;
  channels: number;
}

export abstract class RealtimeProvider {
  protected sessionId: string;
  protected socket: Socket;
  protected config: RealtimeConfig;
  protected session: RealtimeSession;

  constructor(sessionId: string, socket: Socket, config: RealtimeConfig) {
    this.sessionId = sessionId;
    this.socket = socket;
    this.config = config;
  }

  // Abstract methods that each provider must implement
  abstract connect(): Promise<void>;
  abstract disconnect(): Promise<void>;
  abstract sendAudio(audioChunk: AudioChunk): Promise<void>;
  abstract sendText(text: string): Promise<void>;
  abstract onMessage(callback: (message: RealtimeMessage) => void): void;
  abstract onError(callback: (error: Error) => void): void;
  abstract onSessionUpdate(callback: (session: RealtimeSession) => void): void;

  // Common methods
  getSession(): RealtimeSession {
    return this.session;
  }

  getSessionId(): string {
    return this.sessionId;
  }

  protected emitToClient(event: string, data: unknown): void {
    this.socket.emit(event, data);
  }

  protected logInfo(message: string, ...args: unknown[]): void {
    console.log(`[${this.constructor.name}] ${message}`, ...args);
  }

  protected logError(message: string, error?: unknown): void {
    console.error(`[${this.constructor.name}] ${message}`, error);
  }
}
