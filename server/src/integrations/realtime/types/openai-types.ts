// OpenAI Realtime API types

export interface OpenAIMessage {
  type: string;
  response?: {
    id: string;
    object?: string;
    status?: string;
    created?: number;
    output?: unknown[];
  };
  item?: {
    type: string;
    id?: string;
    object?: string;
    status?: string;
  };
  delta?: string;
  [key: string]: unknown;
}

export interface OpenAISessionCreated extends OpenAIMessage {
  type: 'session.created';
  session: {
    id: string;
    object: string;
    created: number;
  };
}

export interface OpenAIResponseCreated extends OpenAIMessage {
  type: 'response.created';
  response: {
    id: string;
    object: string;
    status: string;
    created: number;
  };
}

export interface OpenAIResponseDone extends OpenAIMessage {
  type: 'response.done';
  response: {
    id: string;
    object: string;
    status: string;
    created: number;
    output?: unknown[];
  };
}

export interface OpenAITextDelta extends OpenAIMessage {
  type: 'response.text.delta';
  delta: string;
  item_id?: string;
  output_index?: number;
  content_index?: number;
}

export interface OpenAIAudioDelta extends OpenAIMessage {
  type: 'response.audio.delta';
  delta: string;
  item_id?: string;
  output_index?: number;
  content_index?: number;
}

export interface OpenAITranscriptionCompleted extends OpenAIMessage {
  type: 'conversation.item.input_audio_transcription.completed';
  transcript: string;
  item_id: string;
}

export interface OpenAIFunctionCallArgumentsDone extends OpenAIMessage {
  type: 'response.function_call_arguments.done';
  name: string;
  call_id: string;
  arguments: string;
}

export interface OpenAIError extends OpenAIMessage {
  type: 'error';
  error: {
    message: string;
    type: string;
    code?: string;
  };
}

// Function call argument types
export interface CheckAvailabilityArgs {
  date: string;
  duration?: number;
  time_preference?: 'morning' | 'afternoon' | 'evening' | 'any';
}

export interface BookAppointmentArgs {
  customer_name: string;
  customer_email: string;
  service_type: string;
  preferred_date?: string;
  preferred_time?: string;
  duration: number;
  notes?: string;
}

export interface SuggestAppointmentTimesArgs {
  duration: number;
  time_preference?: 'morning' | 'afternoon' | 'evening' | 'any';
  days_ahead?: number;
}

export interface SaveCustomerInfoArgs {
  name?: string;
  email?: string;
  phone?: string;
  company?: string;
}

// Function call result types
export interface AvailabilityResult {
  date: string;
  available_slots: Array<{
    start_time: string;
    end_time: string;
    duration: number;
    formatted_time: string;
  }>;
  total_slots: number;
}

export interface BookingResult {
  success: boolean;
  appointment?: {
    id?: string;
    summary?: string;
    start_time?: string;
    end_time?: string;
    customer_name: string;
    customer_email: string;
    service_type: string;
  };
  message: string;
}

export interface SuggestionResult {
  suggested_times: Array<{
    date: string;
    time: string;
    start_time: string;
    end_time: string;
    duration: number;
  }>;
  total_suggestions: number;
}

export interface SaveInfoResult {
  success: boolean;
  saved_info: SaveCustomerInfoArgs;
  message: string;
}

export interface ErrorResult {
  error: string;
}

export type FunctionCallResult =
  | AvailabilityResult
  | BookingResult
  | SuggestionResult
  | SaveInfoResult
  | ErrorResult;

// Tool definition types
export interface ToolDefinition {
  type: 'function';
  name: string;
  description: string;
  parameters: {
    type: 'object';
    properties: Record<
      string,
      {
        type: string;
        description: string;
        enum?: string[];
      }
    >;
    required: string[];
  };
}

// Session configuration types
export interface SessionConfig {
  type: 'session.update';
  session: {
    modalities: string[];
    instructions: string;
    voice: string;
    input_audio_format: string;
    output_audio_format: string;
    input_audio_transcription: {
      model: string;
    };
    turn_detection: {
      type: string;
      threshold: number;
      prefix_padding_ms: number;
      silence_duration_ms: number;
    };
    temperature: number;
    max_response_output_tokens: number;
    tools: ToolDefinition[];
  };
}
