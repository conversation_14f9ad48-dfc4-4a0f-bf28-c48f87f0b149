import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Socket } from 'socket.io';
import {
  RealtimeProvider,
  RealtimeConfig,
} from './interfaces/realtime-provider.interface';
import { OpenAIRealtimeProvider } from './providers/openai-realtime.provider';
import { GeminiLiveProvider } from './providers/gemini-live.provider';
import { GoogleService } from '../google/google.service';
import { InformationExtractionService } from '../../calls/services/information-extraction.service';

export type ProviderType = 'openai' | 'gemini';

@Injectable()
export class RealtimeProviderFactory {
  constructor(
    private configService: ConfigService,
    private googleService: GoogleService,
    private informationExtractionService: InformationExtractionService,
  ) {}

  createProvider(
    providerType: ProviderType,
    sessionId: string,
    socket: Socket,
    config: RealtimeConfig,
  ): RealtimeProvider {
    switch (providerType) {
      case 'openai':
        return new OpenAIRealtimeProvider(
          sessionId,
          socket,
          config,
          this.configService,
          this.googleService,
          this.informationExtractionService,
        );

      case 'gemini':
        return new GeminiLiveProvider(
          sessionId,
          socket,
          config,
          this.configService,
        );

      default:
        throw new Error(`Unsupported provider type: ${String(providerType)}`);
    }
  }

  getDefaultProvider(): ProviderType {
    // You can configure this via environment variable
    const defaultProvider = this.configService.get<string>(
      'DEFAULT_REALTIME_PROVIDER',
    );

    if (defaultProvider === 'gemini' || defaultProvider === 'openai') {
      return defaultProvider;
    }

    // Default to Gemini if not specified
    return 'gemini';
  }

  getSupportedProviders(): ProviderType[] {
    return ['openai', 'gemini'];
  }

  validateProviderConfig(providerType: ProviderType): boolean {
    switch (providerType) {
      case 'openai':
        return !!this.configService.get<string>('OPENAI_API_KEY');

      case 'gemini':
        return !!this.configService.get<string>('GOOGLE_API_KEY');

      default:
        return false;
    }
  }

  getProviderCapabilities(providerType: ProviderType): {
    supportsAudio: boolean;
    supportsText: boolean;
    supportsVoiceActivityDetection: boolean;
    supportedVoices: string[];
    supportedLanguages: string[];
    maxSessionDuration?: number; // in minutes
  } {
    switch (providerType) {
      case 'openai':
        return {
          supportsAudio: true,
          supportsText: true,
          supportsVoiceActivityDetection: true,
          supportedVoices: [
            'alloy',
            'echo',
            'fable',
            'onyx',
            'nova',
            'shimmer',
          ],
          supportedLanguages: [
            'en',
            'es',
            'fr',
            'de',
            'it',
            'pt',
            'ru',
            'ja',
            'ko',
            'zh',
          ],
          maxSessionDuration: 15, // 15 minutes for OpenAI
        };

      case 'gemini':
        return {
          supportsAudio: true,
          supportsText: true,
          supportsVoiceActivityDetection: true,
          supportedVoices: [
            'Puck',
            'Charon',
            'Kore',
            'Fenrir',
            'Aoede',
            'Leda',
            'Orus',
            'Zephyr',
          ],
          supportedLanguages: [
            'en-US',
            'en-GB',
            'en-AU',
            'en-IN',
            'es-US',
            'es-ES',
            'fr-FR',
            'fr-CA',
            'de-DE',
            'it-IT',
            'pt-BR',
            'ja-JP',
            'ko-KR',
            'zh-CN',
            'hi-IN',
            'ar-XA',
            'th-TH',
            'vi-VN',
            'id-ID',
            'tr-TR',
            'ru-RU',
            'pl-PL',
            'nl-NL',
          ],
          maxSessionDuration: 15, // 15 minutes for Gemini Live
        };

      default:
        return {
          supportsAudio: false,
          supportsText: false,
          supportsVoiceActivityDetection: false,
          supportedVoices: [],
          supportedLanguages: [],
        };
    }
  }
}
