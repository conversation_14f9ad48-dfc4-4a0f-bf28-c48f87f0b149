import { Module } from '@nestjs/common';
import { TwilioService } from './twilio.service';
import { TwilioController } from './twilio.controller';
import { AgentsModule } from '../../agents/agents.module';
import { CallsModule } from '../../calls/calls.module';
import { OpenaiModule } from '../openai/openai.module';

@Module({
  imports: [AgentsModule, CallsModule, OpenaiModule],
  providers: [TwilioService],
  controllers: [TwilioController],
  exports: [TwilioService],
})
export class TwilioModule {}
