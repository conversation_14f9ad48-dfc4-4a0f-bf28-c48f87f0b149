import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as twilio from 'twilio';
import { AgentsService } from '../../agents/agents.service';
import { CallsService } from '../../calls/calls.service';
import { OpenaiService } from '../openai/openai.service';

interface MockRecording {
  sid: string;
}

interface MockTranscription {
  sid: string;
  recordingSid: string;
}

interface MockTwilioClient {
  recordings: {
    list: (params?: { callSid?: string }) => Promise<MockRecording[]>;
  };
  transcriptions: {
    list: () => Promise<MockTranscription[]>;
  };
}

@Injectable()
export class TwilioService {
  private readonly client: twilio.Twilio | MockTwilioClient;
  private readonly logger = new Logger(TwilioService.name);

  constructor(
    private configService: ConfigService,
    private agentsService: AgentsService,
    private callsService: CallsService,
    private openaiService: OpenaiService,
  ) {
    // Initialize Twilio client with proper error handling
    const accountSid = String(
      this.configService.get('TWILIO_ACCOUNT_SID') || '',
    );
    const authToken = String(this.configService.get('TWILIO_AUTH_TOKEN') || '');

    if (!accountSid || !String(accountSid).startsWith('AC')) {
      this.logger.warn(
        'Invalid or missing TWILIO_ACCOUNT_SID. Using mock Twilio client.',
      );
      // Create a mock Twilio client for development
      this.client = {
        recordings: { list: () => Promise.resolve([]) },
        transcriptions: { list: () => Promise.resolve([]) },
      };
    } else if (!authToken) {
      this.logger.warn('Missing TWILIO_AUTH_TOKEN. Using mock Twilio client.');
      // Create a mock Twilio client for development
      this.client = {
        recordings: { list: () => Promise.resolve([]) },
        transcriptions: { list: () => Promise.resolve([]) },
      };
    } else {
      // Initialize the real Twilio client
      this.client = twilio(accountSid, authToken);
    }
  }

  async handleIncomingCall(
    callSid: string,
    from: string,
    to: string,
  ): Promise<string> {
    try {
      // Find the agent associated with this phone number
      const agents = await this.agentsService.findAll('');
      const agent = agents.find((a) => a.twilioPhoneNumber === to);

      if (!agent) {
        this.logger.error(`No agent found for phone number ${to}`);
        return this.generateTwiMLResponse('No agent found for this number');
      }

      // Create a call record
      const call = await this.callsService.create({
        twilioCallSid: callSid,
        callerNumber: from,
        agent: agent,
      });

      // Generate TwiML to connect to OpenAI realtime API
      return this.generateTwiMLForOpenAI(call.id, agent.id);
    } catch (error) {
      this.logger.error(
        `Error handling incoming call: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      return this.generateTwiMLResponse(
        'An error occurred. Please try again later.',
      );
    }
  }

  private generateTwiMLResponse(message: string): string {
    const twiml = new twilio.twiml.VoiceResponse();
    twiml.say({ voice: 'alice' }, message);
    twiml.hangup();
    return String(twiml.toString());
  }

  private generateTwiMLForOpenAI(callId: string, agentId: string): string {
    const twiml = new twilio.twiml.VoiceResponse();

    // Connect to our OpenAI realtime endpoint
    const connectWebsocket = twiml.connect();
    connectWebsocket.stream({
      url: `wss://${this.configService.get('HOST')}/api/integrations/openai/stream?callId=${callId}&agentId=${agentId}`,
    });

    return String(twiml.toString());
  }

  async getCallRecording(callSid: string): Promise<string | null> {
    try {
      const recordings = await this.client.recordings.list({ callSid });
      if (recordings.length === 0) {
        return null;
      }

      // Get the most recent recording
      const recording = recordings[0];
      const recordingUrl = `https://api.twilio.com/2010-04-01/Accounts/${this.configService.get('TWILIO_ACCOUNT_SID')}/Recordings/${recording.sid}.mp3`;

      return recordingUrl;
    } catch (error) {
      this.logger.error(
        `Error getting call recording: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      return null;
    }
  }

  async getCallTranscription(callSid: string): Promise<string | null> {
    try {
      // Use the correct parameter format for Twilio API
      const recordings = await this.client.recordings.list({ callSid });

      if (recordings.length === 0) {
        return null;
      }

      // Get the most recent recording
      const recording = recordings[0];

      // Get transcriptions for this recording
      const transcriptions = await this.client.transcriptions.list();

      // Filter transcriptions manually by recording SID
      const recordingTranscriptions = transcriptions.filter(
        (t) => t.recordingSid === recording.sid,
      );

      if (recordingTranscriptions.length === 0) {
        return null;
      }

      // Get the most recent transcription
      const transcription = recordingTranscriptions[0];
      const transcriptionUrl = `https://api.twilio.com/2010-04-01/Accounts/${this.configService.get('TWILIO_ACCOUNT_SID')}/Transcriptions/${transcription.sid}.txt`;

      return transcriptionUrl;
    } catch (error) {
      this.logger.error(
        `Error getting call transcription: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      return null;
    }
  }
}
