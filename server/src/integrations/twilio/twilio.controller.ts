import { <PERSON>, Post, Body, Re<PERSON>, Logger } from '@nestjs/common';
import { Response } from 'express';
import { TwilioService } from './twilio.service';
import { TwilioWebhookBody } from './types/twilio.types';

@Controller('integrations/twilio')
export class TwilioController {
  private readonly logger = new Logger(TwilioController.name);

  constructor(private readonly twilioService: TwilioService) {}

  @Post('webhook')
  async handleWebhook(@Body() body: TwilioWebhookBody, @Res() res: Response) {
    this.logger.log(`Received Twilio webhook: ${JSON.stringify(body)}`);

    try {
      const callSid = String(body.CallSid || '');
      const from = String(body.From || '');
      const to = String(body.To || '');

      const twimlResponse = await this.twilioService.handleIncomingCall(
        callSid,
        from,
        to,
      );

      res.header('Content-Type', 'text/xml');
      res.send(twimlResponse);
    } catch (error) {
      this.logger.error(
        `Error handling webhook: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );

      res.header('Content-Type', 'text/xml');
      res.send(`
        <Response>
          <Say>An error occurred. Please try again later.</Say>
          <Hangup/>
        </Response>
      `);
    }
  }
}
