import { <PERSON>, Logger } from '@nestjs/common';
import {
  WebSocketGateway,
  WebSocketServer,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { OpenaiService } from './openai.service';

@WebSocketGateway({
  path: '/api/integrations/openai/stream',
  cors: {
    origin: '*',
  },
})
@Controller('integrations/openai')
export class OpenaiController
  implements OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(OpenaiController.name);
  private connections = new Map<string, Socket>();

  constructor(private readonly openaiService: OpenaiService) {}

  async handleConnection(client: Socket) {
    try {
      const { callId, agentId } = client.handshake.query;

      if (!callId || !agentId) {
        this.logger.error('Missing callId or agentId in WebSocket connection');
        client.disconnect();
        return;
      }

      this.logger.log(
        `WebSocket client connected: ${client.id} for call ${String(callId)}`,
      );
      this.connections.set(client.id, client);

      // Handle the connection with OpenAI
      await this.openaiService.handleWebSocketConnection(
        callId as string,
        agentId as string,
        client,
      );
    } catch (error) {
      this.logger.error(
        `Error handling WebSocket connection: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      client.disconnect();
    }
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`WebSocket client disconnected: ${client.id}`);
    this.connections.delete(client.id);
  }
}
