import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import OpenAI from 'openai';
import { AgentsService } from '../../agents/agents.service';
import { CallsService } from '../../calls/calls.service';
import { GoogleService } from '../google/google.service';
import { CallStatus } from '../../calls/entities/call.entity';
import {
  CustomerInfo,
  AppointmentDetails,
  RequestDetails,
} from '../../calls/types/call-data.types';

// Type definitions for OpenAI Realtime API and testing
interface RealtimeMessage {
  type: string;
  function_call?: { name: string; arguments: string };
  audio?: Buffer;
  error?: string;
}

interface MockRealtimeStream {
  sendAudio: (data: Buffer) => void;
  sendText: (text: string) => void;
  on: (
    event: string,
    callback: (message: RealtimeMessage | Error) => void,
  ) => void;
  close: () => void;
}

interface TestSession {
  userId: string;
  transcript: TranscriptEntry[];
}

interface TranscriptEntry {
  type: string;
  speaker: string;
  content: string;
}

interface TestingService {
  getSession: (sessionId: string) => TestSession | null;
  addToTranscript: (
    sessionId: string,
    speaker: string,
    content: string,
    type: string,
  ) => void;
}

interface TestSocket {
  emit: (event: string, data: EventData) => void;
  on: (
    event: string,
    callback: (data: Buffer | { content: string }) => void,
  ) => void;
}

interface EventData {
  type: string;
  content?: string;
  timestamp?: Date;
  audioData?: string;
  message?: string;
  error?: string;
}

interface WebSocketLike {
  on: (event: string, callback: (data: Buffer) => void) => void;
  send: (data: Buffer) => void;
  close?: () => void;
  disconnect?: () => void;
}

@Injectable()
export class OpenaiService {
  private readonly openai: OpenAI;
  private readonly logger = new Logger(OpenaiService.name);

  constructor(
    private configService: ConfigService,
    private agentsService: AgentsService,
    private callsService: CallsService,
    private googleService: GoogleService,
  ) {
    this.openai = new OpenAI({
      apiKey: this.configService.get('OPENAI_API_KEY'),
    });
  }

  async handleWebSocketConnection(
    callId: string,
    agentId: string,
    socket: WebSocketLike,
  ) {
    try {
      // Get the call and agent
      const call = await this.callsService.findOne(callId);
      const agent = await this.agentsService.findOne(
        agentId,
        call.agent.user.id,
      );

      // Set up the OpenAI realtime API call
      // Note: functions are available but not used in this mock implementation
      // const functions = this.getAvailableFunctions(call.agent.user.id);

      // Create the OpenAI realtime session
      // Using the OpenAI API directly
      // Note: apiKey is available but not used in this mock implementation
      // const apiKey = this.configService.get('OPENAI_API_KEY');

      // Mock the realtime stream for now until we can properly implement it
      const realtimeStream: MockRealtimeStream = {
        sendAudio: () => {},
        sendText: () => {},
        on: () => {},
        close: () => {},
      };

      // Handle incoming audio from the caller
      socket.on('message', (data: Buffer) => {
        // Forward the audio to OpenAI
        realtimeStream.sendAudio(data);
      });

      // Handle OpenAI responses
      realtimeStream.on('message', (message: RealtimeMessage) => {
        if (message.type === 'function_call' && message.function_call) {
          // Handle function calls from the AI
          void this.handleFunctionCall(
            message.function_call,
            call.id,
            call.agent.user.id,
          );
        } else if (message.type === 'audio' && message.audio) {
          // Forward audio response to the caller
          socket.send(message.audio);
        } else if (message.type === 'error' && message.error) {
          this.logger.error(`OpenAI error: ${message.error}`);
        }
      });

      // Handle connection close
      socket.on('close', () => {
        void (async () => {
          // Close the OpenAI connection
          realtimeStream.close();

          // Update call status
          await this.callsService.updateStatus(call.id, CallStatus.COMPLETED);

          // Get recording and transcription URLs
          // Note: In a real implementation, you might need to wait for Twilio to process these
          // const recordingUrl = await this.twilioService.getCallRecording(call.twilioCallSid);
          // const transcriptionUrl = await this.twilioService.getCallTranscription(call.twilioCallSid);

          // if (recordingUrl) {
          //   await this.callsService.updateCallRecording(call.id, recordingUrl);
          // }

          // if (transcriptionUrl) {
          //   await this.callsService.updateCallTranscription(call.id, transcriptionUrl);
          // }
        })();
      });

      // Handle errors
      realtimeStream.on('error', (errorOrMessage: RealtimeMessage | Error) => {
        if (errorOrMessage instanceof Error) {
          this.logger.error(
            `OpenAI stream error: ${errorOrMessage.message}`,
            errorOrMessage.stack,
          );
        } else {
          this.logger.error(
            `OpenAI error: ${errorOrMessage.error || 'Unknown error'}`,
          );
        }
        if (socket.close) {
          socket.close();
        } else if (socket.disconnect) {
          socket.disconnect();
        }
      });

      // Start the conversation with the agent's prompt
      realtimeStream.sendText(agent.prompt);
    } catch (error) {
      this.logger.error(
        `Error handling WebSocket connection: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      if (socket.close) {
        socket.close();
      } else if (socket.disconnect) {
        socket.disconnect();
      }
    }
  }

  private getAvailableFunctions(userId: string, agentCalendarId?: string) {
    return [
      {
        name: 'check_calendar_availability',
        description: 'Check if a time slot is available in the calendar',
        parameters: {
          type: 'object',
          properties: {
            date: {
              type: 'string',
              description: 'The date in YYYY-MM-DD format',
            },
            startTime: {
              type: 'string',
              description: 'The start time in HH:MM format (24-hour)',
            },
            endTime: {
              type: 'string',
              description: 'The end time in HH:MM format (24-hour)',
            },
          },
          required: ['date', 'startTime', 'endTime'],
        },
        function: async (args: {
          date: string;
          startTime: string;
          endTime: string;
        }) => {
          try {
            const startDateTime = new Date(`${args.date}T${args.startTime}:00`);
            const endDateTime = new Date(`${args.date}T${args.endTime}:00`);

            const isAvailable = await this.googleService.checkAvailability(
              userId,
              startDateTime,
              endDateTime,
            );

            return { available: isAvailable };
          } catch (error) {
            this.logger.error(
              `Error checking availability: ${error instanceof Error ? error.message : String(error)}`,
              error instanceof Error ? error.stack : undefined,
            );
            return {
              available: false,
              error: error instanceof Error ? error.message : String(error),
            };
          }
        },
      },
      {
        name: 'create_calendar_event',
        description: 'Create a new event in the calendar',
        parameters: {
          type: 'object',
          properties: {
            summary: {
              type: 'string',
              description: 'The title of the event',
            },
            description: {
              type: 'string',
              description: 'The description of the event',
            },
            date: {
              type: 'string',
              description: 'The date in YYYY-MM-DD format',
            },
            startTime: {
              type: 'string',
              description: 'The start time in HH:MM format (24-hour)',
            },
            endTime: {
              type: 'string',
              description: 'The end time in HH:MM format (24-hour)',
            },
            attendeeEmail: {
              type: 'string',
              description: 'The email of the attendee',
            },
            attendeeName: {
              type: 'string',
              description: 'The name of the attendee',
            },
          },
          required: ['summary', 'date', 'startTime', 'endTime'],
        },
        function: async (args: {
          summary: string;
          description?: string;
          date: string;
          startTime: string;
          endTime: string;
          attendeeEmail?: string;
          attendeeName?: string;
        }) => {
          try {
            this.logger.log(`[Function] create_calendar_event called with:`, args);

            // Parse the date and time
            const startDateTime = new Date(`${args.date}T${args.startTime}:00`);
            const endDateTime = new Date(`${args.date}T${args.endTime}:00`);

            this.logger.log(`[Function] Parsed dates: ${startDateTime.toISOString()} - ${endDateTime.toISOString()}`);

            // Validate dates
            if (isNaN(startDateTime.getTime()) || isNaN(endDateTime.getTime())) {
              throw new Error(`Invalid date/time format: ${args.date} ${args.startTime}-${args.endTime}`);
            }

            const attendees = args.attendeeEmail
              ? [{ email: args.attendeeEmail, name: args.attendeeName }]
              : undefined;

            this.logger.log(`[Function] Creating calendar event: ${args.summary} on ${args.date} ${args.startTime}-${args.endTime} for calendar ${agentCalendarId}`);

            const event = await this.googleService.createEvent(
              userId,
              args.summary,
              args.description || '',
              startDateTime,
              endDateTime,
              attendees,
              agentCalendarId,
            );

            this.logger.log(`[Function] Calendar event created successfully: ${event.id}`);

            return {
              success: true,
              eventId: event.id,
              htmlLink: event.htmlLink,
            };
          } catch (error) {
            this.logger.error(
              `[Function] Error creating event: ${error instanceof Error ? error.message : String(error)}`,
              error instanceof Error ? error.stack : undefined,
            );
            return {
              success: false,
              error: error instanceof Error ? error.message : String(error),
            };
          }
        },
      },
      {
        name: 'save_call_data',
        description: 'Save data collected during the call',
        parameters: {
          type: 'object',
          properties: {
            callId: {
              type: 'string',
              description: 'The ID of the call',
            },
            customerInfo: {
              type: 'object',
              description: 'Information about the customer',
              properties: {
                name: { type: 'string' },
                email: { type: 'string' },
                phone: { type: 'string' },
                additionalInfo: { type: 'object' },
              },
            },
            appointmentDetails: {
              type: 'object',
              description: 'Details about any appointment made',
              properties: {
                date: { type: 'string' },
                time: { type: 'string' },
                duration: { type: 'string' },
                type: { type: 'string' },
                additionalInfo: { type: 'object' },
              },
            },
            requestDetails: {
              type: 'object',
              description: 'Details about the customer request',
              properties: {
                type: { type: 'string' },
                description: { type: 'string' },
                urgency: { type: 'string' },
                additionalInfo: { type: 'object' },
              },
            },
            summary: {
              type: 'string',
              description: 'A summary of the call',
            },
            googleCalendarEventId: {
              type: 'string',
              description: 'The ID of any Google Calendar event created',
            },
          },
          required: ['callId'],
        },
        function: async (args: {
          callId: string;
          customerInfo?: CustomerInfo;
          appointmentDetails?: AppointmentDetails;
          requestDetails?: RequestDetails;
          summary?: string;
          googleCalendarEventId?: string;
        }) => {
          try {
            await this.callsService.updateCallData(args.callId, {
              customerInfo: args.customerInfo,
              appointmentDetails: args.appointmentDetails,
              requestDetails: args.requestDetails,
              summary: args.summary,
              googleCalendarEventId: args.googleCalendarEventId,
            });

            return { success: true };
          } catch (error) {
            this.logger.error(
              `Error saving call data: ${error instanceof Error ? error.message : String(error)}`,
              error instanceof Error ? error.stack : undefined,
            );
            return {
              success: false,
              error: error instanceof Error ? error.message : String(error),
            };
          }
        },
      },
    ];
  }

  private async handleFunctionCall(
    functionCall: { name: string; arguments: string },
    callId: string,
    userId: string,
  ) {
    try {
      const { name, arguments: argsString } = functionCall;
      const args = JSON.parse(argsString) as Record<string, unknown>;

      const functions = this.getAvailableFunctions(userId);
      const func = functions.find((f) => f.name === name);

      if (func) {
        // Add callId to the arguments if it's the save_call_data function
        if (name === 'save_call_data' && !args.callId) {
          args.callId = callId;
        }

        // Call the function with proper type handling
        const result = await (func.function as any)(args);
        return result;
      }

      return { error: `Function ${name} not found` };
    } catch (error) {
      this.logger.error(
        `Error handling function call: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      return { error: error instanceof Error ? error.message : String(error) };
    }
  }

  async handleTestWebSocketConnection(
    sessionId: string,
    agentId: string,
    socket: TestSocket,
    testingService: TestingService,
  ) {
    try {
      this.logger.log(
        `Starting test WebSocket connection for session ${sessionId}`,
      );

      // Get the agent (we'll need to get userId from the session)
      const session = testingService.getSession(sessionId);
      if (!session) {
        throw new Error('Test session not found');
      }

      const agent = await this.agentsService.findOne(agentId, session.userId);
      if (!agent) {
        throw new Error('Agent not found');
      }

      // For now, we'll simulate the OpenAI Realtime API
      // In a real implementation, you would connect to the actual OpenAI Realtime API
      this.logger.log(`Agent prompt: ${agent.prompt}`);

      // Send initial greeting using OpenAI
      setTimeout(() => {
        void (async () => {
          try {
            console.log(`[TEST] Starting test call for agent: ${agent.name}`);
            console.log(`[TEST] Agent calendar ID: ${agent.googleCalendarId}`);
            console.log(`[TEST] User ID: ${session.userId}`);

            const completion = await this.openai.chat.completions.create({
              model: 'gpt-4',
              messages: [
                {
                  role: 'system',
                  content: agent.prompt || 'You are a helpful AI assistant.',
                },
                {
                  role: 'user',
                  content: 'Hello! Please introduce yourself briefly.',
                },
              ],
              max_tokens: 150,
              temperature: 0.7,
            });

            const greeting =
              completion.choices[0]?.message?.content ||
              "Hello! I'm your AI assistant. How can I help you today?";
            testingService.addToTranscript(
              sessionId,
              'agent',
              greeting,
              'text',
            );

            console.log(`[TEST] Initial greeting sent: ${greeting}`);

            socket.emit('agent-message', {
              type: 'text',
              content: greeting,
              timestamp: new Date(),
            });

            // Simulate audio response (in production, use text-to-speech)
            socket.emit('agent-audio', {
              type: 'audio',
              audioData: 'tts-audio-data',
              timestamp: new Date(),
            });
          } catch (error) {
            this.logger.error(
              'Error getting initial greeting from OpenAI:',
              error,
            );
            // Fallback greeting
            const greeting =
              "Hello! I'm your AI assistant. How can I help you today?";
            testingService.addToTranscript(
              sessionId,
              'agent',
              greeting,
              'text',
            );

            socket.emit('agent-message', {
              type: 'text',
              content: greeting,
              timestamp: new Date(),
            });
          }
        })();
      }, 1000);

      // Handle incoming audio chunks
      socket.on('audio-chunk', (audioData: Buffer) => {
        this.logger.log(`Received audio chunk of size: ${audioData.length}`);

        // In a real implementation, you would:
        // 1. Send this audio to OpenAI Realtime API
        // 2. Get transcription and response
        // 3. Send back audio response

        // For now, simulate a response
        setTimeout(() => {
          const responses = [
            'I understand. Can you tell me more about that?',
            "That's interesting. What else would you like to know?",
            'I can help you with that. Let me get some more information.',
            'Thank you for that information. Is there anything else I can assist you with?',
          ];

          const response =
            responses[Math.floor(Math.random() * responses.length)];
          testingService.addToTranscript(sessionId, 'agent', response, 'text');

          socket.emit('agent-message', {
            type: 'text',
            content: response,
            timestamp: new Date(),
          });

          socket.emit('agent-audio', {
            type: 'audio',
            audioData: 'simulated-audio-response',
            timestamp: new Date(),
          });
        }, 1500);
      });

      // Handle text messages with real OpenAI
      socket.on('text-message', (data: { content: string }) => {
        void (async () => {
          console.log(`[TEST] ===== NEW MESSAGE =====`);
          console.log(`[TEST] User said: ${data.content}`);
          console.log(`[TEST] Agent calendar ID: ${agent.googleCalendarId}`);
          console.log(`[TEST] ========================`);

          this.logger.log(`Received text message: ${data.content}`);
          testingService.addToTranscript(
            sessionId,
            'user',
            data.content,
            'text',
          );

          try {
            // Get conversation history from transcript
            const session = testingService.getSession(sessionId);
            if (!session) {
              throw new Error('Session not found');
            }

            const systemMessage: OpenAI.Chat.Completions.ChatCompletionMessageParam =
              {
                role: 'system',
                content: agent.prompt || 'You are a helpful AI assistant.',
              };

            const conversationMessages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] =
              [
                systemMessage,
                ...session.transcript
                  .filter((t) => t.type === 'text')
                  .map((t) => ({
                    role:
                      t.speaker === 'user'
                        ? ('user' as const)
                        : ('assistant' as const),
                    content: t.content,
                  })),
              ];

            // Get available functions for this user
            const functions = this.getAvailableFunctions(session.userId, agent.googleCalendarId);
            const tools = functions.map(func => ({
              type: 'function' as const,
              function: {
                name: func.name,
                description: func.description,
                parameters: func.parameters,
              },
            }));

            const completion = await this.openai.chat.completions.create({
              model: 'gpt-4',
              messages: conversationMessages,
              max_tokens: 300,
              temperature: 0.7,
              tools: tools,
              tool_choice: 'auto',
            });

            const message = completion.choices[0]?.message;

            // Handle function calls if present
            if (message?.tool_calls && message.tool_calls.length > 0) {
              this.logger.log(`[OpenAI] Processing ${message.tool_calls.length} function calls`);
              for (const toolCall of message.tool_calls) {
                if (toolCall.type === 'function') {
                  this.logger.log(`[OpenAI] Calling function: ${toolCall.function.name}`);
                  this.logger.log(`[OpenAI] Function arguments: ${toolCall.function.arguments}`);

                  const functionResult = await this.handleFunctionCall(
                    {
                      name: toolCall.function.name,
                      arguments: toolCall.function.arguments,
                    },
                    sessionId, // Using sessionId as callId for test calls
                    session.userId,
                  );

                  this.logger.log(`[OpenAI] Function ${toolCall.function.name} result:`, functionResult);
                }
              }
            } else {
              this.logger.log(`[OpenAI] No function calls in response`);
            }

            const response =
              message?.content ||
              "I'm sorry, I couldn't process that request.";
            testingService.addToTranscript(
              sessionId,
              'agent',
              response,
              'text',
            );

            socket.emit('agent-message', {
              type: 'text',
              content: response,
              timestamp: new Date(),
            });

            // Simulate audio response (in production, use text-to-speech)
            socket.emit('agent-audio', {
              type: 'audio',
              audioData: 'tts-audio-response',
              timestamp: new Date(),
            });
          } catch (error) {
            this.logger.error('Error getting response from OpenAI:', error);
            const errorResponse =
              "I'm sorry, I'm having trouble processing your request right now.";
            testingService.addToTranscript(
              sessionId,
              'agent',
              errorResponse,
              'text',
            );

            socket.emit('agent-message', {
              type: 'text',
              content: errorResponse,
              timestamp: new Date(),
            });
          }
        })();
      });
    } catch (error) {
      this.logger.error(
        `Error handling test WebSocket connection: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      socket.emit('error', {
        type: 'error',
        message: 'Failed to initialize test call',
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  /**
   * Create a chat completion using OpenAI's API
   */
  async createChatCompletion(params: {
    model: string;
    messages: Array<{
      role: 'system' | 'user' | 'assistant';
      content: string;
    }>;
    tools?: Array<{
      type: 'function';
      function: {
        name: string;
        description: string;
        parameters: {
          type: 'object';
          properties: Record<
            string,
            {
              type: string;
              description?: string;
              enum?: string[];
              format?: string;
              items?: { type: string };
            }
          >;
          required?: string[];
        };
      };
    }>;
    tool_choice?:
      | 'auto'
      | 'none'
      | { type: 'function'; function: { name: string } };
  }) {
    return await this.openai.chat.completions.create(params);
  }
}
