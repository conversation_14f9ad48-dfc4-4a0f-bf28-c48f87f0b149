import { Module, forwardRef } from '@nestjs/common';
import { OpenaiService } from './openai.service';
import { OpenaiController } from './openai.controller';
import { AgentsModule } from '../../agents/agents.module';
import { CallsModule } from '../../calls/calls.module';
import { GoogleModule } from '../google/google.module';

@Module({
  imports: [AgentsModule, forwardRef(() => CallsModule), GoogleModule],
  providers: [OpenaiService],
  controllers: [OpenaiController],
  exports: [OpenaiService],
})
export class OpenaiModule {}
