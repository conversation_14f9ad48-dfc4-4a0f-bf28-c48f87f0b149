import {
  Controller,
  Get,
  Query,
  UseGuards,
  Request,
  Post,
  Body,
  Param,
  Delete,
} from '@nestjs/common';
import { GoogleService } from './google.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { UsersService } from '../../users/users.service';
import { AuthenticatedRequest } from '../../common/types/request.types';

@Controller('integrations/google')
export class GoogleController {
  constructor(
    private readonly googleService: GoogleService,
    private readonly usersService: UsersService,
  ) {}

  @UseGuards(JwtAuthGuard)
  @Get('auth-url')
  getAuthUrl(@Request() req: AuthenticatedRequest) {
    return { url: this.googleService.getAuthUrl(req.user.id) };
  }

  @Get('callback')
  async handleCallback(
    @Query('code') code: string,
    @Query('state') state?: string,
  ) {
    // Get user from state parameter (passed during auth URL generation)
    if (!state) {
      throw new Error('Missing user ID in state parameter');
    }

    const user = await this.usersService.findOne(state);

    if (!user) {
      throw new Error('User not found');
    }

    const connection = await this.googleService.handleCallback(code, user);

    // Redirect back to frontend settings page with success
    return `
      <!DOCTYPE html>
      <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Calendar Connected - TalkLoop</title>
          <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
          <style>
            * {
              margin: 0;
              padding: 0;
              box-sizing: border-box;
            }

            body {
              font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              background-attachment: fixed;
              min-height: 100vh;
              display: flex;
              align-items: center;
              justify-content: center;
              padding: 20px;
              overflow-x: hidden;
              overflow-y: auto;
            }

            .container {
              background: white;
              border-radius: 16px;
              box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
              padding: 48px;
              text-align: center;
              max-width: 500px;
              width: 100%;
              position: relative;
              margin: 20px auto;
              z-index: 10;
            }

            /* Mobile responsiveness */
            @media (max-width: 640px) {
              .container {
                margin: 10px auto;
                padding: 32px 24px;
                max-width: calc(100vw - 40px);
              }

              body {
                padding: 10px;
                align-items: flex-start;
                padding-top: 40px;
              }
            }





            .title {
              font-size: 2rem;
              font-weight: 700;
              color: #1f2937;
              margin-bottom: 16px;
              line-height: 1.2;
              display: flex;
              align-items: center;
              justify-content: center;
              gap: 12px;
            }

            .subtitle {
              font-size: 1.125rem;
              color: #6b7280;
              margin-bottom: 32px;
              line-height: 1.6;
            }

            .success-icon {
              width: 80px;
              height: 80px;
              background: linear-gradient(135deg, #10b981, #059669);
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              margin: 0 auto 24px;
              animation: pulse 2s ease-in-out infinite;
            }

            @keyframes pulse {
              0%, 100% { transform: scale(1); }
              50% { transform: scale(1.05); }
            }

            .checkmark {
              color: white;
              font-size: 2rem;
              font-weight: bold;
            }

            .features {
              background: #f9fafb;
              border-radius: 12px;
              padding: 24px;
              margin: 32px 0;
              text-align: left;
            }

            .features h3 {
              font-size: 1.125rem;
              font-weight: 600;
              color: #1f2937;
              margin-bottom: 16px;
              text-align: center;
            }

            .feature-list {
              list-style: none;
              space-y: 8px;
            }

            .feature-list li {
              display: flex;
              align-items: center;
              color: #4b5563;
              margin-bottom: 8px;
              font-size: 0.875rem;
            }

            .feature-list li::before {
              content: '✅';
              margin-right: 12px;
              font-size: 1rem;
            }

            .button {
              background: linear-gradient(135deg, #667eea, #764ba2);
              color: white;
              border: none;
              border-radius: 8px;
              padding: 12px 32px;
              font-size: 1rem;
              font-weight: 600;
              cursor: pointer;
              text-decoration: none;
              display: inline-block;
              transition: all 0.2s ease;
              box-shadow: 0 4px 14px 0 rgba(102, 126, 234, 0.4);
            }

            .button:hover {
              transform: translateY(-2px);
              box-shadow: 0 6px 20px 0 rgba(102, 126, 234, 0.6);
            }

            .countdown {
              margin-top: 24px;
              font-size: 0.875rem;
              color: #9ca3af;
            }

            .confetti {
              position: fixed;
              width: 10px;
              height: 10px;
              background: #fbbf24;
              animation: confetti-fall 3s linear infinite;
              z-index: 1;
              will-change: transform;
              pointer-events: none;
              opacity: 0;
              transition: opacity 0.5s ease-in-out;
            }

            .confetti.animate {
              opacity: 1;
            }

            .confetti:nth-child(1) { left: 10%; animation-delay: 0s; background: #ef4444; }
            .confetti:nth-child(2) { left: 20%; animation-delay: 0.5s; background: #3b82f6; }
            .confetti:nth-child(3) { left: 30%; animation-delay: 1s; background: #10b981; }
            .confetti:nth-child(4) { left: 40%; animation-delay: 1.5s; background: #f59e0b; }
            .confetti:nth-child(5) { left: 50%; animation-delay: 2s; background: #8b5cf6; }
            .confetti:nth-child(6) { left: 60%; animation-delay: 0.3s; background: #ef4444; }
            .confetti:nth-child(7) { left: 70%; animation-delay: 0.8s; background: #06b6d4; }
            .confetti:nth-child(8) { left: 80%; animation-delay: 1.3s; background: #84cc16; }
            .confetti:nth-child(9) { left: 90%; animation-delay: 1.8s; background: #f97316; }

            @keyframes confetti-fall {
              0% {
                transform: translateY(-20vh) rotate(0deg);
                opacity: 1;
              }
              100% {
                transform: translateY(120vh) rotate(720deg);
                opacity: 0;
              }
            }
          </style>
        </head>
        <body>
          <div class="confetti" id="confetti-1"></div>
          <div class="confetti" id="confetti-2"></div>
          <div class="confetti" id="confetti-3"></div>
          <div class="confetti" id="confetti-4"></div>
          <div class="confetti" id="confetti-5"></div>
          <div class="confetti" id="confetti-6"></div>
          <div class="confetti" id="confetti-7"></div>
          <div class="confetti" id="confetti-8"></div>
          <div class="confetti" id="confetti-9"></div>

          <div class="container">
            <div class="success-icon">
              <div class="checkmark">✓</div>
            </div>

            <h1 class="title">
              🎉 Calendar Connected!
            </h1>
            <p class="subtitle">
              Your Google Calendar is now seamlessly integrated with TalkLoop.
              Your AI agents can now book appointments automatically!
            </p>

            <div class="features">
              <h3>🚀 What's Now Possible</h3>
              <ul class="feature-list">
                <li>AI agents can check your calendar availability</li>
                <li>Automatic appointment booking during calls</li>
                <li>Real-time calendar conflict detection</li>
                <li>Smart scheduling based on your preferences</li>
                <li>Seamless integration with your existing workflow</li>
              </ul>
            </div>

            <a href="http://localhost:5173/settings" class="button">
              Return to Settings
            </a>

            <div class="countdown">
              <span id="countdown-text">Automatically redirecting in <span id="countdown">10</span> seconds...</span>
            </div>
          </div>

          <script>
            // Wait for page to fully load, then start confetti animation
            window.addEventListener('load', () => {
              setTimeout(() => {
                // Add animate class to all confetti elements to fade them in
                const confettiElements = document.querySelectorAll('.confetti');
                confettiElements.forEach((element, index) => {
                  setTimeout(() => {
                    element.classList.add('animate');
                  }, index * 100); // Stagger the fade-in for a nice effect
                });
              }, 500); // Wait 500ms after page load to ensure smooth start
            });

            // Start countdown timer immediately
            let countdown = 10;
            const countdownElement = document.getElementById('countdown');
            const countdownText = document.getElementById('countdown-text');

            console.log('Starting countdown timer...', { countdown, countdownElement, countdownText });

            // Update countdown every second
            const timer = setInterval(() => {
              countdown--;
              console.log('Countdown:', countdown);

              if (countdownElement) {
                countdownElement.textContent = countdown;
              }

              if (countdown <= 0) {
                clearInterval(timer);
                console.log('Countdown finished, redirecting...');

                if (countdownText) {
                  countdownText.textContent = 'Redirecting now...';
                }

                // Perform redirect immediately (no setTimeout delay)
                try {
                  console.log('Attempting redirect...', {
                    hasOpener: !!window.opener,
                    openerClosed: window.opener ? window.opener.closed : 'no opener'
                  });

                  // Always do direct redirect for reliability
                  console.log('Redirecting to settings page...');
                  window.location.href = 'http://localhost:5173/settings';

                } catch (error) {
                  console.error('Redirect failed:', error);
                  // Force redirect as last resort
                  window.location.replace('http://localhost:5173/settings');
                }
              }
            }, 1000);

            // Also add a backup timer in case the main one fails
            setTimeout(() => {
              console.log('Backup redirect timer triggered');
              window.location.href = 'http://localhost:5173/settings';
            }, 12000);
          </script>
        </body>
      </html>
    `;
  }

  @UseGuards(JwtAuthGuard)
  @Get('calendars')
  getCalendarConnections(@Request() req: AuthenticatedRequest) {
    return this.googleService.getCalendarConnections(req.user.id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('events')
  async listEvents(
    @Request() req: AuthenticatedRequest,
    @Query('timeMin') timeMin?: string,
    @Query('timeMax') timeMax?: string,
  ) {
    return this.googleService.listEvents(
      req.user.id,
      timeMin ? new Date(timeMin) : undefined,
      timeMax ? new Date(timeMax) : undefined,
    );
  }

  @UseGuards(JwtAuthGuard)
  @Post('events')
  async createEvent(
    @Request() req: AuthenticatedRequest,
    @Body()
    eventData: {
      summary: string;
      description: string;
      startDateTime: string;
      endDateTime: string;
      attendees?: { email: string; name?: string }[];
    },
  ) {
    return this.googleService.createEvent(
      req.user.id,
      eventData.summary,
      eventData.description,
      new Date(eventData.startDateTime),
      new Date(eventData.endDateTime),
      eventData.attendees,
    );
  }

  @UseGuards(JwtAuthGuard)
  @Get('availability')
  async checkAvailability(
    @Request() req: AuthenticatedRequest,
    @Query('startDateTime') startDateTime: string,
    @Query('endDateTime') endDateTime: string,
  ) {
    return {
      available: await this.googleService.checkAvailability(
        req.user.id,
        new Date(startDateTime),
        new Date(endDateTime),
      ),
    };
  }

  @UseGuards(JwtAuthGuard)
  @Delete('calendars/:id')
  async disconnectCalendar(
    @Param('id') id: string,
    @Request() req: AuthenticatedRequest,
  ) {
    await this.googleService.disconnectCalendar(id, req.user.id);
    return { success: true };
  }
}
