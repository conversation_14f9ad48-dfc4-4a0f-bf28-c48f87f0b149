import {
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { google, calendar_v3 } from 'googleapis';
import { OAuth2Client } from 'google-auth-library';
import { CalendarConnection } from './entities/calendar-connection.entity';
import { User } from '../../users/entities/user.entity';
// Google API types are handled by googleapis library

export interface AvailableSlot {
  startTime: Date;
  endTime: Date;
  duration: number; // in minutes
}

export interface AppointmentRequest {
  customerName: string;
  customerEmail: string;
  serviceType: string;
  preferredDate?: string;
  preferredTime?: string;
  duration: number; // in minutes
  notes?: string;
  timePreference?: 'morning' | 'afternoon' | 'evening' | 'any';
}

@Injectable()
export class GoogleService {
  private readonly oauth2Client: OAuth2Client;

  constructor(
    private configService: ConfigService,
    @InjectRepository(CalendarConnection)
    private calendarConnectionRepository: Repository<CalendarConnection>,
  ) {
    this.oauth2Client = new google.auth.OAuth2(
      this.configService.get('GOOGLE_CLIENT_ID'),
      this.configService.get('GOOGLE_CLIENT_SECRET'),
      this.configService.get('GOOGLE_REDIRECT_URI'),
    );
  }

  getAuthUrl(userId?: string): string {
    const scopes = [
      'https://www.googleapis.com/auth/calendar',
      'https://www.googleapis.com/auth/calendar.events',
    ];

    return this.oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: scopes,
      prompt: 'consent',
      state: userId, // Pass user ID in state parameter
    });
  }

  async handleCallback(code: string, user: User): Promise<CalendarConnection> {
    const tokenResponse = await this.oauth2Client.getToken(code);
    const tokens = tokenResponse.tokens;
    this.oauth2Client.setCredentials(tokens);

    // Get calendar list
    const calendar = google.calendar({
      version: 'v3',
      auth: this.oauth2Client,
    });
    const calendarListResponse = await calendar.calendarList.list();

    if (
      !calendarListResponse.data.items ||
      calendarListResponse.data.items.length === 0
    ) {
      throw new NotFoundException('No calendars found');
    }

    // Use the primary calendar
    const primaryCalendar =
      calendarListResponse.data.items.find((cal) => cal.primary) ||
      calendarListResponse.data.items[0];

    // Save the connection
    const calendarConnection = this.calendarConnectionRepository.create({
      calendarId: primaryCalendar?.id || '',
      calendarName: primaryCalendar?.summary || 'Google Calendar',
      accessToken: tokens.access_token || '',
      refreshToken: tokens.refresh_token || '',
      tokenExpiry: tokens.expiry_date
        ? new Date(tokens.expiry_date)
        : new Date(),
      user,
    });

    return await this.calendarConnectionRepository.save(calendarConnection);
  }

  async getCalendarClient(userId: string): Promise<calendar_v3.Calendar> {
    const connection = await this.calendarConnectionRepository.findOne({
      where: { user: { id: userId }, isActive: true },
      order: { createdAt: 'DESC' },
    });

    if (!connection) {
      throw new UnauthorizedException(
        'No active Google Calendar connection found',
      );
    }

    // Check if token is expired and refresh if needed
    if (new Date() > connection.tokenExpiry) {
      this.oauth2Client.setCredentials({
        refresh_token: connection.refreshToken,
      });

      const credentialsResponse = await this.oauth2Client.refreshAccessToken();
      const credentials = credentialsResponse.credentials;

      // Update the connection with new tokens
      connection.accessToken = credentials.access_token || '';
      connection.tokenExpiry = credentials.expiry_date
        ? new Date(credentials.expiry_date)
        : new Date();
      await this.calendarConnectionRepository.save(connection);
    }

    // Set credentials and return calendar client
    this.oauth2Client.setCredentials({
      access_token: connection.accessToken,
      refresh_token: connection.refreshToken,
    });

    return google.calendar({ version: 'v3', auth: this.oauth2Client });
  }

  async listEvents(
    userId: string,
    timeMin?: Date,
    timeMax?: Date,
  ): Promise<calendar_v3.Schema$Event[]> {
    const calendar = await this.getCalendarClient(userId);
    const connection = await this.calendarConnectionRepository.findOne({
      where: { user: { id: userId }, isActive: true },
      order: { createdAt: 'DESC' },
    });

    if (!connection) {
      throw new NotFoundException('No active calendar connection found');
    }

    const response = await calendar.events.list({
      calendarId: connection.calendarId,
      timeMin: timeMin ? timeMin.toISOString() : new Date().toISOString(),
      timeMax: timeMax ? timeMax.toISOString() : undefined,
      singleEvents: true,
      orderBy: 'startTime',
    });

    return response.data.items || [];
  }

  async createEvent(
    userId: string,
    summary: string,
    description: string,
    startDateTime: Date,
    endDateTime: Date,
    attendees?: { email: string; name?: string }[],
    specificCalendarId?: string,
  ): Promise<calendar_v3.Schema$Event> {
    console.log(`[GoogleService] Creating event for user ${userId}`);
    console.log(`[GoogleService] Event details: ${summary}, ${startDateTime.toISOString()} - ${endDateTime.toISOString()}`);
    console.log(`[GoogleService] Specific calendar ID: ${specificCalendarId}`);

    const calendar = await this.getCalendarClient(userId);

    // Find the specific calendar connection or fall back to any active one
    let connection;
    if (specificCalendarId) {
      console.log(`[GoogleService] Looking for specific calendar: ${specificCalendarId}`);
      connection = await this.calendarConnectionRepository.findOne({
        where: {
          user: { id: userId },
          calendarId: specificCalendarId,
          isActive: true
        },
      });
      console.log(`[GoogleService] Specific calendar found: ${connection ? 'YES' : 'NO'}`);
    }

    if (!connection) {
      console.log(`[GoogleService] Falling back to any active calendar`);
      connection = await this.calendarConnectionRepository.findOne({
        where: { user: { id: userId }, isActive: true },
        order: { createdAt: 'DESC' },
      });
      console.log(`[GoogleService] Fallback calendar found: ${connection ? connection.calendarId : 'NO'}`);
    }

    if (!connection) {
      throw new NotFoundException('No active calendar connection found');
    }

    const event: calendar_v3.Schema$Event = {
      summary,
      description,
      start: {
        dateTime: startDateTime.toISOString(),
        timeZone: 'UTC',
      },
      end: {
        dateTime: endDateTime.toISOString(),
        timeZone: 'UTC',
      },
      attendees: attendees?.map((attendee) => ({
        email: attendee.email,
        displayName: attendee.name,
      })),
    };

    console.log(`[GoogleService] Creating event in calendar: ${connection.calendarId}`);
    console.log(`[GoogleService] Event object:`, JSON.stringify(event, null, 2));

    const response = await calendar.events.insert({
      calendarId: connection.calendarId,
      requestBody: event,
    });

    if (!response.data) {
      throw new Error('Failed to create event');
    }

    console.log(`[GoogleService] Event created successfully: ${response.data.id}`);
    console.log(`[GoogleService] Event HTML link: ${response.data.htmlLink}`);

    return response.data;
  }

  async checkAvailability(
    userId: string,
    startDateTime: Date,
    endDateTime: Date,
  ): Promise<boolean> {
    const events = await this.listEvents(userId, startDateTime, endDateTime);
    return events.length === 0;
  }

  async getCalendarConnections(userId: string): Promise<CalendarConnection[]> {
    return this.calendarConnectionRepository.find({
      where: { user: { id: userId }, isActive: true },
      order: { createdAt: 'DESC' },
    });
  }

  async disconnectCalendar(
    connectionId: string,
    userId: string,
  ): Promise<void> {
    const connection = await this.calendarConnectionRepository.findOne({
      where: { id: connectionId, user: { id: userId } },
    });

    if (!connection) {
      throw new NotFoundException('Calendar connection not found');
    }

    connection.isActive = false;
    await this.calendarConnectionRepository.save(connection);
  }

  /**
   * Find available appointment slots for a given date and duration
   */
  async getAvailableSlots(
    userId: string,
    date: string, // YYYY-MM-DD format
    duration: number, // minutes
    timePreference?: 'morning' | 'afternoon' | 'evening' | 'any',
  ): Promise<AvailableSlot[]> {
    const targetDate = new Date(date);
    const startOfDay = new Date(targetDate);
    startOfDay.setHours(9, 0, 0, 0); // Start at 9 AM

    const endOfDay = new Date(targetDate);
    endOfDay.setHours(17, 0, 0, 0); // End at 5 PM

    // Get existing events for the day
    const events = await this.listEvents(userId, startOfDay, endOfDay);

    // Define business hours based on preference
    const businessHours = this.getBusinessHours(targetDate, timePreference);

    // Find gaps between existing events
    const availableSlots: AvailableSlot[] = [];
    let currentTime = new Date(businessHours.start);

    // Sort events by start time
    const sortedEvents = events
      .filter((event) => event.start?.dateTime && event.end?.dateTime)
      .sort(
        (a, b) =>
          new Date(a.start!.dateTime!).getTime() -
          new Date(b.start!.dateTime!).getTime(),
      );

    for (const event of sortedEvents) {
      const eventStart = new Date(event.start!.dateTime!);
      const eventEnd = new Date(event.end!.dateTime!);

      // Check if there's a gap before this event
      if (currentTime < eventStart) {
        const gapDuration =
          (eventStart.getTime() - currentTime.getTime()) / (1000 * 60);

        if (gapDuration >= duration) {
          // Create slots in this gap
          const slotsInGap = this.createSlotsInTimeRange(
            currentTime,
            eventStart,
            duration,
          );
          availableSlots.push(...slotsInGap);
        }
      }

      // Move current time to after this event
      currentTime = new Date(
        Math.max(currentTime.getTime(), eventEnd.getTime()),
      );
    }

    // Check for availability after the last event until end of business hours
    if (currentTime < businessHours.end) {
      const remainingDuration =
        (businessHours.end.getTime() - currentTime.getTime()) / (1000 * 60);

      if (remainingDuration >= duration) {
        const slotsAtEnd = this.createSlotsInTimeRange(
          currentTime,
          businessHours.end,
          duration,
        );
        availableSlots.push(...slotsAtEnd);
      }
    }

    return availableSlots;
  }

  /**
   * Book an appointment with intelligent scheduling
   */
  async bookAppointment(
    userId: string,
    appointmentRequest: AppointmentRequest,
  ): Promise<calendar_v3.Schema$Event> {
    let availableSlots: AvailableSlot[] = [];

    // If specific date/time is provided, check that slot
    if (appointmentRequest.preferredDate && appointmentRequest.preferredTime) {
      const preferredDateTime = new Date(
        `${appointmentRequest.preferredDate}T${appointmentRequest.preferredTime}`,
      );
      const endDateTime = new Date(
        preferredDateTime.getTime() + appointmentRequest.duration * 60000,
      );

      const isAvailable = await this.checkAvailability(
        userId,
        preferredDateTime,
        endDateTime,
      );

      if (isAvailable) {
        // Book the preferred slot
        return this.createEvent(
          userId,
          `${appointmentRequest.serviceType} - ${appointmentRequest.customerName}`,
          this.generateAppointmentDescription(appointmentRequest),
          preferredDateTime,
          endDateTime,
          [
            {
              email: appointmentRequest.customerEmail,
              name: appointmentRequest.customerName,
            },
          ],
        );
      } else {
        throw new Error('Preferred time slot is not available');
      }
    }

    // If preferred date is provided, find slots on that date
    if (appointmentRequest.preferredDate) {
      availableSlots = await this.getAvailableSlots(
        userId,
        appointmentRequest.preferredDate,
        appointmentRequest.duration,
        appointmentRequest.timePreference,
      );
    } else {
      // Find slots in the next 7 days
      availableSlots = await this.findSlotsInNextDays(
        userId,
        7,
        appointmentRequest.duration,
        appointmentRequest.timePreference,
      );
    }

    if (availableSlots.length === 0) {
      throw new Error('No available slots found for the requested parameters');
    }

    // Book the first available slot
    const selectedSlot = availableSlots[0];

    return this.createEvent(
      userId,
      `${appointmentRequest.serviceType} - ${appointmentRequest.customerName}`,
      this.generateAppointmentDescription(appointmentRequest),
      selectedSlot.startTime,
      selectedSlot.endTime,
      [
        {
          email: appointmentRequest.customerEmail,
          name: appointmentRequest.customerName,
        },
      ],
    );
  }

  /**
   * Get suggested appointment times for AI agent
   */
  async getSuggestedAppointmentTimes(
    userId: string,
    duration: number,
    timePreference?: 'morning' | 'afternoon' | 'evening' | 'any',
    daysAhead: number = 7,
  ): Promise<AvailableSlot[]> {
    const allSlots = await this.findSlotsInNextDays(
      userId,
      daysAhead,
      duration,
      timePreference,
    );

    // Return top 6 suggestions across different days
    const suggestions: AvailableSlot[] = [];
    const usedDates = new Set<string>();

    for (const slot of allSlots) {
      const dateKey = slot.startTime.toDateString();

      if (!usedDates.has(dateKey) || suggestions.length < 6) {
        suggestions.push(slot);
        usedDates.add(dateKey);

        if (suggestions.length >= 6) break;
      }
    }

    return suggestions;
  }

  // Helper methods
  private getBusinessHours(
    date: Date,
    timePreference?: string,
  ): { start: Date; end: Date } {
    const start = new Date(date);
    const end = new Date(date);

    switch (timePreference) {
      case 'morning':
        start.setHours(9, 0, 0, 0);
        end.setHours(12, 0, 0, 0);
        break;
      case 'afternoon':
        start.setHours(13, 0, 0, 0);
        end.setHours(17, 0, 0, 0);
        break;
      case 'evening':
        start.setHours(17, 0, 0, 0);
        end.setHours(19, 0, 0, 0);
        break;
      default:
        start.setHours(9, 0, 0, 0);
        end.setHours(17, 0, 0, 0);
    }

    return { start, end };
  }

  private createSlotsInTimeRange(
    startTime: Date,
    endTime: Date,
    duration: number,
  ): AvailableSlot[] {
    const slots: AvailableSlot[] = [];
    const slotDurationMs = duration * 60 * 1000;
    let currentTime = new Date(startTime);

    while (currentTime.getTime() + slotDurationMs <= endTime.getTime()) {
      const slotEnd = new Date(currentTime.getTime() + slotDurationMs);

      slots.push({
        startTime: new Date(currentTime),
        endTime: slotEnd,
        duration,
      });

      // Move to next 30-minute interval
      currentTime = new Date(currentTime.getTime() + 30 * 60 * 1000);
    }

    return slots;
  }

  private async findSlotsInNextDays(
    userId: string,
    days: number,
    duration: number,
    timePreference?: 'morning' | 'afternoon' | 'evening' | 'any',
  ): Promise<AvailableSlot[]> {
    const allSlots: AvailableSlot[] = [];

    for (let i = 0; i < days; i++) {
      const checkDate = new Date();
      checkDate.setDate(checkDate.getDate() + i);

      // Skip weekends
      if (checkDate.getDay() === 0 || checkDate.getDay() === 6) continue;

      const dateString = checkDate.toISOString().split('T')[0];
      const daySlots = await this.getAvailableSlots(
        userId,
        dateString,
        duration,
        timePreference,
      );

      allSlots.push(...daySlots);
    }

    return allSlots.sort(
      (a, b) => a.startTime.getTime() - b.startTime.getTime(),
    );
  }

  private generateAppointmentDescription(request: AppointmentRequest): string {
    let description = `Service: ${request.serviceType}\n`;
    description += `Customer: ${request.customerName}\n`;
    description += `Email: ${request.customerEmail}\n`;
    description += `Duration: ${request.duration} minutes\n`;

    if (request.notes) {
      description += `\nNotes: ${request.notes}`;
    }

    description +=
      '\n\nThis appointment was automatically scheduled via TalkLoop AI.';

    return description;
  }
}
