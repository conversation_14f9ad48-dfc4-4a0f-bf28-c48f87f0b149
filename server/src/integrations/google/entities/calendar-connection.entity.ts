import {
  <PERSON><PERSON>ty,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
} from 'typeorm';
import { User } from '../../../users/entities/user.entity';

@Entity()
export class CalendarConnection {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  calendarId: string;

  @Column()
  calendarName: string;

  @Column()
  accessToken: string;

  @Column()
  refreshToken: string;

  @Column()
  tokenExpiry: Date;

  @Column({ default: true })
  isActive: boolean;

  @ManyToOne(() => User)
  user: User;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
