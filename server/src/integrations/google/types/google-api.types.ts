// Google Calendar API types

export interface GoogleOAuth2Credentials {
  access_token?: string | null;
  refresh_token?: string | null;
  expiry_date?: number | null;
  token_type?: string | null;
  scope?: string | null;
}

export interface GoogleTokensResponse {
  tokens: GoogleOAuth2Credentials;
}

export interface GoogleCalendarListResponse {
  data: {
    items?: GoogleCalendarItem[];
  };
}

export interface GoogleCalendarItem {
  id?: string;
  summary?: string;
  primary?: boolean;
  accessRole?: string;
  selected?: boolean;
}

export interface GoogleEventResponse {
  data: {
    items?: GoogleEventItem[];
  };
}

export interface GoogleEventItem {
  id?: string;
  summary?: string;
  description?: string;
  location?: string;
  start?: {
    dateTime?: string;
    date?: string;
    timeZone?: string;
  };
  end?: {
    dateTime?: string;
    date?: string;
    timeZone?: string;
  };
  attendees?: Array<{
    email?: string;
    displayName?: string;
    responseStatus?: string;
  }>;
  created?: string;
  updated?: string;
  status?: string;
}

export interface CreateEventRequest {
  summary: string;
  description?: string;
  start: {
    dateTime: string;
    timeZone?: string;
  };
  end: {
    dateTime: string;
    timeZone?: string;
  };
  attendees?: Array<{
    email: string;
    displayName?: string;
  }>;
}

export interface FreeBusyRequest {
  timeMin: string;
  timeMax: string;
  items: Array<{
    id: string;
  }>;
}

export interface FreeBusyResponse {
  data: {
    calendars?: Record<
      string,
      {
        busy?: Array<{
          start: string;
          end: string;
        }>;
      }
    >;
  };
}
