import { Controller, Get, Post } from '@nestjs/common';
import { AppService } from './app.service';
import { exec } from 'child_process';
import { promisify } from 'util';

const execPromise = promisify(exec);

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Post('seed')
  async seedDatabase() {
    try {
      const { stdout, stderr } = await execPromise('npm run seed');

      if (stderr && !stderr.includes('ExperimentalWarning')) {
        console.error('Error seeding database:', stderr);
        return {
          success: false,
          message: 'Error seeding database',
          error: stderr,
        };
      }

      return {
        success: true,
        message: 'Database seeded successfully',
        details: stdout,
      };
    } catch (error) {
      console.error('Error executing seed command:', error);
      return {
        success: false,
        message: 'Error executing seed command',
        error: (error as Error).message,
      };
    }
  }
}
