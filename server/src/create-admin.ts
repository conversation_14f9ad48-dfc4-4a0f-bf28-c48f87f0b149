import { NestFactory } from '@nestjs/core';
import { TypeOrmModule, getRepositoryToken } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { User } from './users/entities/user.entity';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { Module } from '@nestjs/common';

@Module({
  imports: [
    ConfigModule.forRoot(),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get('DB_HOST'),
        port: configService.get('DB_PORT'),
        username: configService.get('DB_USERNAME'),
        password: configService.get('DB_PASSWORD'),
        database: configService.get('DB_DATABASE'),
        entities: [User],
        synchronize: true,
      }),
    }),
    TypeOrmModule.forFeature([User]),
  ],
})
class AdminModule {}

async function bootstrap() {
  // Create a standalone application
  const app = await NestFactory.createApplicationContext(AdminModule);

  try {
    // Get the user repository
    const userRepository = app.get<Repository<User>>(getRepositoryToken(User));

    // Check if admin user already exists
    const adminExists = await userRepository.findOne({
      where: { email: '<EMAIL>' },
    });

    if (adminExists) {
      console.log('Admin user already exists. Updating password...');

      // Update password
      const hashedPassword = await bcrypt.hash('password', 10);
      adminExists.password = hashedPassword;
      await userRepository.save(adminExists);

      console.log('Admin password updated successfully!');
    } else {
      console.log('Creating new admin user...');

      // Create admin user
      const hashedPassword = await bcrypt.hash('password', 10);

      const admin = userRepository.create({
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'User',
        password: hashedPassword,
        companyName: 'Talkloop Admin',
        isEmailVerified: true,
      });

      await userRepository.save(admin);
      console.log('Admin user created successfully!');
    }
  } catch (error) {
    console.error('Error creating/updating admin user:', error);
  } finally {
    await app.close();
  }
}

void bootstrap();
