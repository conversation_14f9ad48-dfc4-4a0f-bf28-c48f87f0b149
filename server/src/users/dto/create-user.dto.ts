import {
  Is<PERSON>mail,
  <PERSON>NotEmpt<PERSON>,
  <PERSON><PERSON><PERSON>al,
  IsS<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';

export class CreateUserDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsNotEmpty()
  firstName: string;

  @IsString()
  @IsNotEmpty()
  lastName: string;

  @IsString()
  @IsOptional()
  companyName?: string;

  @IsString()
  @IsOptional()
  phoneNumber?: string;

  @IsString()
  @IsNotEmpty()
  @MinLength(8)
  password: string;
}
