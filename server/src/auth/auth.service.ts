import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';
import { RefreshTokenService } from './services/refresh-token.service';

import { SecurityMonitoringService } from '../security/services/security-monitoring.service';
import { DeviceFingerprintService } from '../security/services/device-fingerprint.service';
import * as bcrypt from 'bcrypt';
import { CreateUserDto } from '../users/dto/create-user.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import {
  JwtPayload,
  LoginResponse,
  UserWithoutPassword,
} from '../common/types/auth.types';

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
    private refreshTokenService: RefreshTokenService,
    private securityMonitoringService: SecurityMonitoringService,
    private deviceFingerprintService: DeviceFingerprintService,
  ) {}

  async validateUser(
    email: string,
    password: string,
    securityContext?: { ip: string; userAgent: string }
  ): Promise<UserWithoutPassword | null> {
    const user = await this.usersService.findByEmail(email);

    // Log login attempt
    if (securityContext) {
      this.securityMonitoringService.logEvent({
        type: 'login_attempt',
        userId: user?.id,
        ip: securityContext.ip,
        userAgent: securityContext.userAgent,
        timestamp: new Date(),
        details: { email },
      });
    }

    if (user && (await bcrypt.compare(password, user.password))) {
      // Log successful validation
      if (securityContext) {
        this.securityMonitoringService.logEvent({
          type: 'login_success',
          userId: user.id,
          ip: securityContext.ip,
          userAgent: securityContext.userAgent,
          timestamp: new Date(),
          details: { email },
        });
      }

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { password: userPassword, ...result } = user;
      return result;
    }

    // Log failed validation
    if (securityContext) {
      this.securityMonitoringService.logEvent({
        type: 'login_failure',
        userId: user?.id,
        ip: securityContext.ip,
        userAgent: securityContext.userAgent,
        timestamp: new Date(),
        details: { email, reason: 'invalid_credentials' },
      });
    }

    return null;
  }

  async login(
    user: UserWithoutPassword,
    deviceInfo?: {
      userAgent?: string;
      ip?: string;
      platform?: string;
      browser?: string;
    },
  ): Promise<LoginResponse & { refresh_token: string }> {
    const payload: JwtPayload = { email: user.email, sub: user.id };

    // Create refresh token
    const { token: refreshToken } = await this.refreshTokenService.createRefreshToken(
      user.id,
      deviceInfo,
    );

    // Update last login time
    await this.usersService.updateLastLogin(user.id);

    return {
      access_token: this.jwtService.sign(payload),
      refresh_token: refreshToken,
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        companyName: user.companyName,
        phoneNumber: user.phoneNumber,
      },
    };
  }

  async register(
    createUserDto: CreateUserDto,
    deviceInfo?: {
      userAgent?: string;
      ip?: string;
      platform?: string;
      browser?: string;
    },
  ) {
    // Check if user exists
    const existingUser = await this.usersService.findByEmail(
      createUserDto.email,
    );
    if (existingUser) {
      throw new UnauthorizedException('Email already in use');
    }

    // Create new user
    const user = await this.usersService.create(createUserDto);

    // Return JWT token
    return this.login(user, deviceInfo);
  }

  async refreshAccessToken(
    refreshToken: string,
    deviceInfo?: {
      userAgent?: string;
      ip?: string;
      platform?: string;
      browser?: string;
    },
  ): Promise<{ access_token: string; refresh_token: string } | null> {
    // Log token refresh attempt
    if (deviceInfo) {
      this.securityMonitoringService.logEvent({
        type: 'token_refresh',
        ip: deviceInfo.ip || 'unknown',
        userAgent: deviceInfo.userAgent || 'unknown',
        timestamp: new Date(),
        details: { refreshToken: refreshToken.substring(0, 10) + '...' },
      });
    }

    // Validate and rotate refresh token
    const rotatedToken = await this.refreshTokenService.rotateRefreshToken(
      refreshToken,
      deviceInfo,
    );

    if (!rotatedToken) {
      throw new UnauthorizedException('Invalid refresh token');
    }

    // Get user for new access token
    const user = await this.usersService.findOne(rotatedToken.refreshToken.userId);
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    // Generate new access token
    const payload: JwtPayload = { email: user.email, sub: user.id };
    const accessToken = this.jwtService.sign(payload);

    return {
      access_token: accessToken,
      refresh_token: rotatedToken.token,
    };
  }

  async logout(refreshToken: string): Promise<void> {
    await this.refreshTokenService.revokeRefreshToken(refreshToken);
  }

  async logoutAll(userId: string): Promise<void> {
    await this.refreshTokenService.revokeAllUserTokens(userId);
  }

  async changePassword(userId: string, changePasswordDto: ChangePasswordDto) {
    const user = await this.usersService.findOne(userId);

    // Verify current password
    const isPasswordValid = await bcrypt.compare(
      changePasswordDto.currentPassword,
      user.password,
    );

    if (!isPasswordValid) {
      throw new UnauthorizedException('Current password is incorrect');
    }

    // Update password
    await this.usersService.update(userId, {
      password: changePasswordDto.newPassword,
    });

    return { success: true, message: 'Password changed successfully' };
  }
}
