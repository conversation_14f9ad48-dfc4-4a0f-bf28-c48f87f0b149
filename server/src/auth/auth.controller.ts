import {
  Controller,
  Post,
  Body,
  UseGuards,
  Request,
  Get,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { CreateUserDto } from '../users/dto/create-user.dto';
import { LoginDto } from './dto/login.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { LocalAuthGuard } from './guards/local-auth.guard';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { AuthSecurity, ApiSecurity } from '../security/decorators/security.decorator';
import { SecurityService } from '../security/services/security.service';
import { DeviceFingerprintService } from '../security/services/device-fingerprint.service';
import { SecurityMonitoringService } from '../security/services/security-monitoring.service';
import {
  AuthenticatedRequest,
  LoginRequest,
} from '../common/types/request.types';
import { User } from '../users/entities/user.entity';

@Controller('auth')
export class AuthController {
  constructor(
    private authService: AuthService,
    private securityService: SecurityService,
    private deviceFingerprintService: DeviceFingerprintService,
    private securityMonitoringService: SecurityMonitoringService,
  ) {}

  @AuthSecurity()
  @Post('register')
  async register(@Body() createUserDto: CreateUserDto, @Request() req: any) {
    const deviceInfo = {
      userAgent: req.headers['user-agent'],
      ip: req.ip,
    };
    return this.authService.register(createUserDto, deviceInfo);
  }

  @AuthSecurity()
  @UseGuards(LocalAuthGuard)
  @Post('login')
  async login(@Body() loginDto: LoginDto, @Request() req: LoginRequest) {
    const deviceInfo = {
      userAgent: req.headers['user-agent'],
      ip: req.ip,
    };
    return this.authService.login(req.user, deviceInfo);
  }

  @UseGuards(JwtAuthGuard)
  @Get('profile')
  getProfile(@Request() req: AuthenticatedRequest): User {
    return req.user;
  }

  @UseGuards(JwtAuthGuard)
  @Post('change-password')
  async changePassword(
    @Request() req: AuthenticatedRequest,
    @Body() changePasswordDto: ChangePasswordDto,
  ) {
    return this.authService.changePassword(req.user.id, changePasswordDto);
  }

  @ApiSecurity()
  @Post('refresh')
  async refresh(@Body() body: { refresh_token: string }, @Request() req: any) {
    const deviceInfo = {
      userAgent: req.headers['user-agent'],
      ip: req.ip,
    };
    return this.authService.refreshAccessToken(body.refresh_token, deviceInfo);
  }

  @ApiSecurity()
  @Post('logout')
  async logout(@Body() body: { refresh_token: string }) {
    await this.authService.logout(body.refresh_token);
    return { message: 'Logged out successfully' };
  }

  @UseGuards(JwtAuthGuard)
  @Post('logout-all')
  async logoutAll(@Request() req: AuthenticatedRequest) {
    await this.authService.logoutAll(req.user.id);
    return { message: 'Logged out from all devices successfully' };
  }
}
