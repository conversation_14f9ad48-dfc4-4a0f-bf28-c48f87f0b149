import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThan, In, MoreThan } from 'typeorm';
import { RefreshToken } from '../entities/refresh-token.entity';
import { User } from '../../users/entities/user.entity';
import * as crypto from 'crypto';

@Injectable()
export class RefreshTokenService {
  constructor(
    @InjectRepository(RefreshToken)
    private refreshTokenRepository: Repository<RefreshToken>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async createRefreshToken(
    userId: string,
    deviceInfo?: {
      userAgent?: string;
      ip?: string;
      platform?: string;
      browser?: string;
    },
  ): Promise<{ token: string; refreshToken: RefreshToken }> {
    // Generate a secure random token
    const token = crypto.randomBytes(64).toString('hex');
    const tokenHash = crypto.createHash('sha256').update(token).digest('hex');

    // Set expiration to 30 days from now
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 30);

    // Create refresh token entity
    const refreshToken = this.refreshTokenRepository.create({
      userId,
      tokenHash,
      deviceInfo,
      expiresAt,
    });

    await this.refreshTokenRepository.save(refreshToken);

    // Update user session count
    await this.userRepository.increment({ id: userId }, 'sessionCount', 1);

    return { token, refreshToken };
  }

  async validateRefreshToken(token: string): Promise<RefreshToken | null> {
    const tokenHash = crypto.createHash('sha256').update(token).digest('hex');

    const refreshToken = await this.refreshTokenRepository.findOne({
      where: {
        tokenHash,
        isRevoked: false,
        expiresAt: MoreThan(new Date()),
      },
      relations: ['user'],
    });

    if (!refreshToken) {
      return null;
    }

    // Update last used timestamp
    refreshToken.lastUsedAt = new Date();
    await this.refreshTokenRepository.save(refreshToken);

    return refreshToken;
  }

  async rotateRefreshToken(
    oldToken: string,
    deviceInfo?: {
      userAgent?: string;
      ip?: string;
      platform?: string;
      browser?: string;
    },
  ): Promise<{ token: string; refreshToken: RefreshToken } | null> {
    const oldTokenHash = crypto.createHash('sha256').update(oldToken).digest('hex');

    // Find and revoke the old token
    const oldRefreshToken = await this.refreshTokenRepository.findOne({
      where: {
        tokenHash: oldTokenHash,
        isRevoked: false,
      },
    });

    if (!oldRefreshToken) {
      return null;
    }

    // Revoke the old token
    oldRefreshToken.isRevoked = true;
    await this.refreshTokenRepository.save(oldRefreshToken);

    // Create a new refresh token
    return this.createRefreshToken(oldRefreshToken.userId, deviceInfo);
  }

  async revokeRefreshToken(token: string): Promise<boolean> {
    const tokenHash = crypto.createHash('sha256').update(token).digest('hex');

    const result = await this.refreshTokenRepository.update(
      { tokenHash, isRevoked: false },
      { isRevoked: true },
    );

    return (result.affected ?? 0) > 0;
  }

  async revokeAllUserTokens(userId: string): Promise<void> {
    await this.refreshTokenRepository.update(
      { userId, isRevoked: false },
      { isRevoked: true },
    );

    // Reset session count
    await this.userRepository.update({ id: userId }, { sessionCount: 0 });
  }

  async cleanupExpiredTokens(): Promise<void> {
    await this.refreshTokenRepository.delete({
      expiresAt: LessThan(new Date()),
    });
  }

  async getUserActiveSessions(userId: string): Promise<RefreshToken[]> {
    return this.refreshTokenRepository.find({
      where: {
        userId,
        isRevoked: false,
        expiresAt: MoreThan(new Date()),
      },
      order: {
        lastUsedAt: 'DESC',
      },
    });
  }

  async limitUserSessions(userId: string, maxSessions: number = 5): Promise<void> {
    const activeSessions = await this.getUserActiveSessions(userId);

    if (activeSessions.length > maxSessions) {
      // Revoke oldest sessions
      const sessionsToRevoke = activeSessions.slice(maxSessions);
      const idsToRevoke = sessionsToRevoke.map(session => session.id);

      await this.refreshTokenRepository.update(
        { id: In(idsToRevoke) },
        { isRevoked: true },
      );
    }
  }
}
