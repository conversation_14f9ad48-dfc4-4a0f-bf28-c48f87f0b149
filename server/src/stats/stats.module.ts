import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { StatsController } from './stats.controller';
import { StatsService } from './stats.service';
import { Agent } from '../agents/entities/agent.entity';
import { Call } from '../calls/entities/call.entity';
import { CallData } from '../calls/entities/call-data.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Agent, Call, CallData])],
  controllers: [StatsController],
  providers: [StatsService],
  exports: [StatsService],
})
export class StatsModule {}
