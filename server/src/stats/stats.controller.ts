import { Controller, Get, UseGuards, Request, Query } from '@nestjs/common';
import { StatsService } from './stats.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AuthenticatedRequest } from '../common/types/request.types';

@Controller('stats')
@UseGuards(JwtAuthGuard)
export class StatsController {
  constructor(private readonly statsService: StatsService) {}

  @Get('dashboard')
  async getDashboardStats(@Request() req: AuthenticatedRequest) {
    return this.statsService.getDashboardStats(req.user.id);
  }

  @Get('recent-activity')
  async getRecentActivity(
    @Request() req: AuthenticatedRequest,
    @Query('limit') limit?: string,
  ) {
    const limitNum = limit ? parseInt(limit, 10) : 5;
    return this.statsService.getRecentActivity(req.user.id, limitNum);
  }
}
