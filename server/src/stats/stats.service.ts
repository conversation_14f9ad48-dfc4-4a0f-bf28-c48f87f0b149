import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { Agent } from '../agents/entities/agent.entity';
import { Call } from '../calls/entities/call.entity';
import { CallData } from '../calls/entities/call-data.entity';

export interface DashboardStats {
  totalAgents: number;
  totalCalls: number;
  callsToday: number;
  appointmentsBooked: number;
}

export interface RecentActivity {
  type: 'call' | 'appointment' | 'agent';
  id: string;
  title: string;
  description: string;
  timestamp: Date;
}

@Injectable()
export class StatsService {
  constructor(
    @InjectRepository(Agent)
    private agentRepository: Repository<Agent>,
    @InjectRepository(Call)
    private callRepository: Repository<Call>,
    @InjectRepository(CallData)
    private callDataRepository: Repository<CallData>,
  ) {}

  async getDashboardStats(userId: string): Promise<DashboardStats> {
    try {
      // Get total agents
      const totalAgents = await this.agentRepository.count({
        where: { user: { id: userId } },
      });

      // If user has no agents, return zeros for all stats
      if (totalAgents === 0) {
        return {
          totalAgents: 0,
          totalCalls: 0,
          callsToday: 0,
          appointmentsBooked: 0,
        };
      }

      // Get total calls
      const totalCalls = await this.callRepository.count({
        where: { agent: { user: { id: userId } } },
      });

      // Get calls today
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      const callsToday = await this.callRepository.count({
        where: {
          agent: { user: { id: userId } },
          createdAt: Between(today, tomorrow),
        },
      });

      // Get appointments booked
      let appointmentsBooked = 0;

      // Only try to get appointments if there are calls
      if (totalCalls > 0) {
        // We need to use a query builder for this since we need to join with the call table
        const appointmentsQuery = this.callRepository
          .createQueryBuilder('call')
          .innerJoin('call.callData', 'callData')
          .innerJoin('call.agent', 'agent')
          .innerJoin('agent.user', 'user')
          .where('user.id = :userId', { userId })
          .andWhere('callData.appointmentDetails IS NOT NULL');

        appointmentsBooked = await appointmentsQuery.getCount();
      }

      return {
        totalAgents,
        totalCalls,
        callsToday,
        appointmentsBooked,
      };
    } catch (error) {
      console.error('Error getting dashboard stats:', error);
      // Return default values instead of throwing error
      return {
        totalAgents: 0,
        totalCalls: 0,
        callsToday: 0,
        appointmentsBooked: 0,
      };
    }
  }

  async getRecentActivity(
    userId: string,
    limit = 5,
  ): Promise<RecentActivity[]> {
    try {
      // Check if user has any agents first
      const hasAgents = await this.agentRepository.count({
        where: { user: { id: userId } },
      });

      // If user has no agents, return empty array
      if (hasAgents === 0) {
        return [];
      }

      // Get recent calls with their call data
      const recentCalls = await this.callRepository
        .createQueryBuilder('call')
        .leftJoinAndSelect('call.agent', 'agent')
        .leftJoinAndSelect('call.callData', 'callData')
        .leftJoinAndSelect('agent.user', 'user')
        .where('user.id = :userId', { userId })
        .orderBy('call.createdAt', 'DESC')
        .take(limit)
        .getMany();

      // Transform calls into activity items
      const activities: RecentActivity[] = recentCalls.map((call) => {
        let type: 'call' | 'appointment' = 'call';
        let title = 'New call received';
        let description = `From: ${call.callerName || call.callerNumber}`;

        // Check if this call resulted in an appointment
        if (call.callData?.appointmentDetails) {
          type = 'appointment';
          title = 'Appointment booked';

          const appointmentDetails = call.callData.appointmentDetails;
          const date = appointmentDetails?.date
            ? new Date(String(appointmentDetails.date)).toLocaleString()
            : 'Unknown date';

          description = `For: ${call.callerName || call.callerNumber} • ${date}`;
        }

        return {
          type,
          id: call.id,
          title,
          description,
          timestamp: call.createdAt,
        };
      });

      return activities;
    } catch (error) {
      console.error('Error getting recent activity:', error);
      return []; // Return empty array instead of throwing error
    }
  }
}
