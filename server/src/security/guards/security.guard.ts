import { Injectable, CanActivate, ExecutionContext, ForbiddenException, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';
import { SecurityService } from '../services/security.service';
import { SecurityMonitoringService } from '../services/security-monitoring.service';

export const SECURITY_OPTIONS_KEY = 'security_options';

export interface SecurityOptions {
  checkSuspiciousIP?: boolean;
  requireDeviceConsistency?: boolean;
  logActivity?: boolean;
  blockSuspiciousUserAgents?: boolean;
}

@Injectable()
export class SecurityGuard implements CanActivate {
  private readonly logger = new Logger(SecurityGuard.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly securityService: SecurityService,
    private readonly monitoringService: SecurityMonitoringService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const options = this.reflector.get<SecurityOptions>(
      SECURITY_OPTIONS_KEY,
      context.getHandler(),
    ) || {};

    const request = context.switchToHttp().getRequest<Request>();
    const securityContext = this.securityService.extractSecurityContext(request);

    // Log activity if requested
    if (options.logActivity) {
      this.monitoringService.logEvent({
        type: 'suspicious_activity',
        ip: securityContext.ip,
        userAgent: securityContext.userAgent,
        timestamp: new Date(),
        details: {
          endpoint: request.url,
          method: request.method,
        },
      });
    }

    // Check for suspicious IP
    if (options.checkSuspiciousIP && this.securityService.isSuspiciousIP(securityContext.ip)) {
      this.logger.warn(`Blocked request from suspicious IP: ${securityContext.ip}`);
      throw new ForbiddenException('Access denied from suspicious IP');
    }

    // Check for suspicious user agents
    if (options.blockSuspiciousUserAgents) {
      const suspiciousPatterns = this.securityService.detectSuspiciousActivity(securityContext);
      if (suspiciousPatterns.includes('suspicious_user_agent')) {
        this.logger.warn(`Blocked request from suspicious user agent: ${securityContext.userAgent}`);
        throw new ForbiddenException('Access denied from suspicious client');
      }
    }

    // Additional security checks can be added here

    return true;
  }
}
