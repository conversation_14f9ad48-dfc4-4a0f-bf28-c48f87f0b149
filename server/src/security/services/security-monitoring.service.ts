import { Injectable, Logger } from '@nestjs/common';
import { SecurityEvent, SecurityContext } from './security.service';

export interface SecurityAlert {
  id: string;
  type: 'brute_force' | 'suspicious_location' | 'device_change' | 'rapid_requests' | 'token_abuse';
  severity: 'low' | 'medium' | 'high' | 'critical';
  userId?: string;
  ip: string;
  userAgent: string;
  description: string;
  details: any;
  timestamp: Date;
  resolved: boolean;
}

export interface SecurityMetrics {
  totalLoginAttempts: number;
  failedLoginAttempts: number;
  successfulLogins: number;
  suspiciousActivities: number;
  blockedIPs: number;
  activeAlerts: number;
  tokenRefreshes: number;
  uniqueDevices: number;
  timeRange: {
    start: Date;
    end: Date;
  };
}

@Injectable()
export class SecurityMonitoringService {
  private readonly logger = new Logger(SecurityMonitoringService.name);
  private readonly events: SecurityEvent[] = [];
  private readonly alerts: SecurityAlert[] = [];
  private readonly metrics = {
    loginAttempts: 0,
    failedLogins: 0,
    successfulLogins: 0,
    suspiciousActivities: 0,
    blockedIPs: new Set<string>(),
    tokenRefreshes: 0,
    devices: new Set<string>(),
  };

  /**
   * Log security event
   */
  logEvent(event: SecurityEvent): void {
    this.events.push(event);
    this.updateMetrics(event);
    this.analyzeEvent(event);
    
    this.logger.log(`Security event: ${event.type} from ${event.ip}`, {
      type: event.type,
      userId: event.userId,
      ip: event.ip,
      userAgent: event.userAgent,
    });

    // Keep only last 1000 events in memory
    if (this.events.length > 1000) {
      this.events.splice(0, this.events.length - 1000);
    }
  }

  /**
   * Create security alert
   */
  createAlert(alert: Omit<SecurityAlert, 'id' | 'timestamp' | 'resolved'>): SecurityAlert {
    const newAlert: SecurityAlert = {
      ...alert,
      id: this.generateAlertId(),
      timestamp: new Date(),
      resolved: false,
    };

    this.alerts.push(newAlert);
    
    this.logger.warn(`Security alert created: ${alert.type} - ${alert.description}`, {
      alertId: newAlert.id,
      severity: alert.severity,
      ip: alert.ip,
      userId: alert.userId,
    });

    // Keep only last 100 alerts in memory
    if (this.alerts.length > 100) {
      this.alerts.splice(0, this.alerts.length - 100);
    }

    return newAlert;
  }

  /**
   * Analyze security event for potential threats
   */
  private analyzeEvent(event: SecurityEvent): void {
    switch (event.type) {
      case 'login_failure':
        this.analyzeFailedLogin(event);
        break;
      case 'login_success':
        this.analyzeSuccessfulLogin(event);
        break;
      case 'token_refresh':
        this.analyzeTokenRefresh(event);
        break;
      case 'suspicious_activity':
        this.analyzeSuspiciousActivity(event);
        break;
    }
  }

  /**
   * Analyze failed login attempts
   */
  private analyzeFailedLogin(event: SecurityEvent): void {
    const recentFailures = this.getRecentEvents(event.ip, 'login_failure', 15 * 60 * 1000); // 15 minutes
    
    if (recentFailures.length >= 5) {
      this.createAlert({
        type: 'brute_force',
        severity: 'high',
        userId: event.userId,
        ip: event.ip,
        userAgent: event.userAgent,
        description: `Brute force attack detected: ${recentFailures.length} failed login attempts`,
        details: {
          attempts: recentFailures.length,
          timeWindow: '15 minutes',
          firstAttempt: recentFailures[0].timestamp,
          lastAttempt: event.timestamp,
        },
      });
    }
  }

  /**
   * Analyze successful login for anomalies
   */
  private analyzeSuccessfulLogin(event: SecurityEvent): void {
    if (!event.userId) return;

    // Check for login from new location/device
    const userHistory = this.getUserEvents(event.userId, 'login_success');
    const knownIPs = new Set(userHistory.map(e => e.ip));
    
    if (!knownIPs.has(event.ip) && userHistory.length > 0) {
      this.createAlert({
        type: 'suspicious_location',
        severity: 'medium',
        userId: event.userId,
        ip: event.ip,
        userAgent: event.userAgent,
        description: 'Login from new IP address',
        details: {
          newIP: event.ip,
          knownIPs: Array.from(knownIPs),
          previousLogins: userHistory.length,
        },
      });
    }

    // Check for rapid logins from different IPs
    const recentLogins = this.getRecentUserEvents(event.userId, 'login_success', 5 * 60 * 1000); // 5 minutes
    const uniqueIPs = new Set(recentLogins.map(e => e.ip));
    
    if (uniqueIPs.size > 2) {
      this.createAlert({
        type: 'suspicious_location',
        severity: 'high',
        userId: event.userId,
        ip: event.ip,
        userAgent: event.userAgent,
        description: 'Multiple logins from different locations in short time',
        details: {
          uniqueIPs: Array.from(uniqueIPs),
          timeWindow: '5 minutes',
          loginCount: recentLogins.length,
        },
      });
    }
  }

  /**
   * Analyze token refresh patterns
   */
  private analyzeTokenRefresh(event: SecurityEvent): void {
    const recentRefreshes = this.getRecentEvents(event.ip, 'token_refresh', 60 * 1000); // 1 minute
    
    if (recentRefreshes.length > 10) {
      this.createAlert({
        type: 'token_abuse',
        severity: 'medium',
        userId: event.userId,
        ip: event.ip,
        userAgent: event.userAgent,
        description: 'Excessive token refresh requests',
        details: {
          refreshCount: recentRefreshes.length,
          timeWindow: '1 minute',
        },
      });
    }
  }

  /**
   * Analyze suspicious activity
   */
  private analyzeSuspiciousActivity(event: SecurityEvent): void {
    this.createAlert({
      type: 'suspicious_location',
      severity: 'medium',
      userId: event.userId,
      ip: event.ip,
      userAgent: event.userAgent,
      description: 'Suspicious activity detected',
      details: event.details,
    });
  }

  /**
   * Get recent events from specific IP
   */
  private getRecentEvents(ip: string, type: string, timeWindow: number): SecurityEvent[] {
    const cutoff = new Date(Date.now() - timeWindow);
    return this.events.filter(
      event => event.ip === ip && event.type === type && event.timestamp >= cutoff
    );
  }

  /**
   * Get recent events for specific user
   */
  private getRecentUserEvents(userId: string, type: string, timeWindow: number): SecurityEvent[] {
    const cutoff = new Date(Date.now() - timeWindow);
    return this.events.filter(
      event => event.userId === userId && event.type === type && event.timestamp >= cutoff
    );
  }

  /**
   * Get all events for specific user
   */
  private getUserEvents(userId: string, type?: string): SecurityEvent[] {
    return this.events.filter(
      event => event.userId === userId && (!type || event.type === type)
    );
  }

  /**
   * Update internal metrics
   */
  private updateMetrics(event: SecurityEvent): void {
    switch (event.type) {
      case 'login_attempt':
        this.metrics.loginAttempts++;
        break;
      case 'login_failure':
        this.metrics.failedLogins++;
        break;
      case 'login_success':
        this.metrics.successfulLogins++;
        if (event.details?.fingerprint) {
          this.metrics.devices.add(event.details.fingerprint);
        }
        break;
      case 'token_refresh':
        this.metrics.tokenRefreshes++;
        break;
      case 'suspicious_activity':
        this.metrics.suspiciousActivities++;
        break;
    }
  }

  /**
   * Get current security metrics
   */
  getMetrics(): SecurityMetrics {
    const now = new Date();
    const dayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    return {
      totalLoginAttempts: this.metrics.loginAttempts,
      failedLoginAttempts: this.metrics.failedLogins,
      successfulLogins: this.metrics.successfulLogins,
      suspiciousActivities: this.metrics.suspiciousActivities,
      blockedIPs: this.metrics.blockedIPs.size,
      activeAlerts: this.alerts.filter(alert => !alert.resolved).length,
      tokenRefreshes: this.metrics.tokenRefreshes,
      uniqueDevices: this.metrics.devices.size,
      timeRange: {
        start: dayAgo,
        end: now,
      },
    };
  }

  /**
   * Get active alerts
   */
  getActiveAlerts(): SecurityAlert[] {
    return this.alerts.filter(alert => !alert.resolved);
  }

  /**
   * Resolve alert
   */
  resolveAlert(alertId: string): boolean {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.resolved = true;
      this.logger.log(`Security alert resolved: ${alertId}`);
      return true;
    }
    return false;
  }

  /**
   * Generate unique alert ID
   */
  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Clean up old data
   */
  cleanup(): void {
    const cutoff = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago

    // Remove old events
    const oldEventCount = this.events.length;
    this.events.splice(0, this.events.findIndex(event => event.timestamp >= cutoff));
    
    // Remove old resolved alerts
    const oldAlertCount = this.alerts.length;
    this.alerts.splice(0, this.alerts.findIndex(alert => 
      !alert.resolved || alert.timestamp >= cutoff
    ));

    this.logger.debug(`Cleanup completed: removed ${oldEventCount - this.events.length} events and ${oldAlertCount - this.alerts.length} alerts`);
  }
}
