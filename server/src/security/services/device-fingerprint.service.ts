import { Injectable, Logger } from '@nestjs/common';
import { Request } from 'express';
import * as crypto from 'crypto';

export interface DeviceFingerprint {
  id: string;
  userAgent: string;
  acceptLanguage: string;
  acceptEncoding: string;
  platform?: string;
  screenResolution?: string;
  timezone?: string;
  plugins?: string[];
  fonts?: string[];
  canvas?: string;
  webgl?: string;
  hash: string;
  confidence: number;
  createdAt: Date;
}

export interface EnhancedDeviceInfo {
  userAgent: string;
  ip: string;
  platform?: string;
  browser?: string;
  browserVersion?: string;
  os?: string;
  osVersion?: string;
  device?: string;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  fingerprint: string;
}

@Injectable()
export class DeviceFingerprintService {
  private readonly logger = new Logger(DeviceFingerprintService.name);

  /**
   * Generate enhanced device fingerprint from request
   */
  generateFingerprint(req: Request, clientFingerprint?: any): DeviceFingerprint {
    const userAgent = req.headers['user-agent'] || '';
    const acceptLanguage = req.headers['accept-language'] || '';
    const acceptEncoding = req.headers['accept-encoding'] || '';

    // Basic server-side fingerprinting
    const serverComponents = [
      userAgent,
      acceptLanguage,
      acceptEncoding,
      req.headers['accept'] || '',
      req.headers['connection'] || '',
      req.headers['upgrade-insecure-requests'] || '',
      req.headers['sec-fetch-site'] || '',
      req.headers['sec-fetch-mode'] || '',
      req.headers['sec-fetch-dest'] || '',
    ];

    // Combine with client-side fingerprint if available
    let allComponents = serverComponents;
    if (clientFingerprint) {
      allComponents = [
        ...serverComponents,
        clientFingerprint.platform || '',
        clientFingerprint.screenResolution || '',
        clientFingerprint.timezone || '',
        JSON.stringify(clientFingerprint.plugins || []),
        JSON.stringify(clientFingerprint.fonts || []),
        clientFingerprint.canvas || '',
        clientFingerprint.webgl || '',
      ];
    }

    const combinedFingerprint = allComponents.join('|');
    const hash = crypto.createHash('sha256').update(combinedFingerprint).digest('hex');

    // Calculate confidence based on available data
    let confidence = 0.3; // Base confidence from server-side data
    if (clientFingerprint) {
      confidence += 0.2; // Platform info
      if (clientFingerprint.screenResolution) confidence += 0.1;
      if (clientFingerprint.timezone) confidence += 0.1;
      if (clientFingerprint.plugins?.length > 0) confidence += 0.1;
      if (clientFingerprint.fonts?.length > 0) confidence += 0.1;
      if (clientFingerprint.canvas) confidence += 0.1;
      if (clientFingerprint.webgl) confidence += 0.1;
    }

    return {
      id: crypto.randomUUID(),
      userAgent,
      acceptLanguage,
      acceptEncoding,
      platform: clientFingerprint?.platform,
      screenResolution: clientFingerprint?.screenResolution,
      timezone: clientFingerprint?.timezone,
      plugins: clientFingerprint?.plugins,
      fonts: clientFingerprint?.fonts,
      canvas: clientFingerprint?.canvas,
      webgl: clientFingerprint?.webgl,
      hash,
      confidence: Math.min(confidence, 1.0),
      createdAt: new Date(),
    };
  }

  /**
   * Extract enhanced device information
   */
  extractDeviceInfo(req: Request): EnhancedDeviceInfo {
    const userAgent = req.headers['user-agent'] || '';
    const ip = this.getClientIP(req);

    const deviceInfo = this.parseUserAgent(userAgent);
    const fingerprint = this.generateBasicFingerprint(req);

    return {
      userAgent,
      ip,
      ...deviceInfo,
      fingerprint,
    };
  }

  /**
   * Parse user agent string to extract device information
   */
  private parseUserAgent(userAgent: string): Omit<EnhancedDeviceInfo, 'userAgent' | 'ip' | 'fingerprint'> {
    const info: Omit<EnhancedDeviceInfo, 'userAgent' | 'ip' | 'fingerprint'> = {
      isMobile: false,
      isTablet: false,
      isDesktop: false,
    };

    // Browser detection
    if (userAgent.includes('Chrome')) {
      info.browser = 'Chrome';
      const match = userAgent.match(/Chrome\/(\d+\.\d+)/);
      if (match) info.browserVersion = match[1];
    } else if (userAgent.includes('Firefox')) {
      info.browser = 'Firefox';
      const match = userAgent.match(/Firefox\/(\d+\.\d+)/);
      if (match) info.browserVersion = match[1];
    } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
      info.browser = 'Safari';
      const match = userAgent.match(/Version\/(\d+\.\d+)/);
      if (match) info.browserVersion = match[1];
    } else if (userAgent.includes('Edge')) {
      info.browser = 'Edge';
      const match = userAgent.match(/Edge\/(\d+\.\d+)/);
      if (match) info.browserVersion = match[1];
    }

    // OS detection
    if (userAgent.includes('Windows')) {
      info.os = 'Windows';
      if (userAgent.includes('Windows NT 10.0')) info.osVersion = '10';
      else if (userAgent.includes('Windows NT 6.3')) info.osVersion = '8.1';
      else if (userAgent.includes('Windows NT 6.1')) info.osVersion = '7';
    } else if (userAgent.includes('Mac OS X')) {
      info.os = 'macOS';
      const match = userAgent.match(/Mac OS X (\d+[._]\d+)/);
      if (match) info.osVersion = match[1].replace('_', '.');
    } else if (userAgent.includes('Linux')) {
      info.os = 'Linux';
    } else if (userAgent.includes('Android')) {
      info.os = 'Android';
      const match = userAgent.match(/Android (\d+\.\d+)/);
      if (match) info.osVersion = match[1];
    } else if (userAgent.includes('iOS')) {
      info.os = 'iOS';
      const match = userAgent.match(/OS (\d+_\d+)/);
      if (match) info.osVersion = match[1].replace('_', '.');
    }

    // Device type detection
    if (userAgent.includes('Mobile') || userAgent.includes('Android')) {
      info.isMobile = true;
      info.isTablet = false;
      info.isDesktop = false;
      if (userAgent.includes('iPad') || userAgent.includes('Tablet')) {
        info.isTablet = true;
        info.isMobile = false;
      }
    } else {
      info.isDesktop = true;
      info.isMobile = false;
      info.isTablet = false;
    }

    // Platform detection
    if (userAgent.includes('iPhone')) {
      info.platform = 'iPhone';
      info.device = 'iPhone';
    } else if (userAgent.includes('iPad')) {
      info.platform = 'iPad';
      info.device = 'iPad';
    } else if (userAgent.includes('Android')) {
      info.platform = 'Android';
    } else if (userAgent.includes('Windows')) {
      info.platform = 'Windows';
    } else if (userAgent.includes('Macintosh')) {
      info.platform = 'macOS';
    } else if (userAgent.includes('Linux')) {
      info.platform = 'Linux';
    }

    return info;
  }

  /**
   * Generate basic fingerprint from request headers
   */
  private generateBasicFingerprint(req: Request): string {
    const components = [
      req.headers['user-agent'] || '',
      req.headers['accept-language'] || '',
      req.headers['accept-encoding'] || '',
      req.headers['accept'] || '',
    ];

    const fingerprint = components.join('|');
    return crypto.createHash('sha256').update(fingerprint).digest('hex');
  }

  /**
   * Get client IP address
   */
  private getClientIP(req: Request): string {
    const forwarded = req.headers['x-forwarded-for'] as string;
    const realIP = req.headers['x-real-ip'] as string;

    if (forwarded) {
      return forwarded.split(',')[0].trim();
    }

    if (realIP) {
      return realIP;
    }

    return req.connection.remoteAddress || req.socket.remoteAddress || 'unknown';
  }

  /**
   * Compare two fingerprints for similarity
   */
  compareFingerprints(fp1: DeviceFingerprint, fp2: DeviceFingerprint): number {
    if (fp1.hash === fp2.hash) {
      return 1.0;
    }

    let score = 0;
    let factors = 0;

    // User agent similarity
    if (fp1.userAgent === fp2.userAgent) {
      score += 0.3;
    } else if (this.isSimilarUserAgent(fp1.userAgent, fp2.userAgent)) {
      score += 0.15;
    }
    factors++;

    // Language similarity
    if (fp1.acceptLanguage === fp2.acceptLanguage) {
      score += 0.1;
    }
    factors++;

    // Platform similarity
    if (fp1.platform && fp2.platform) {
      if (fp1.platform === fp2.platform) {
        score += 0.2;
      }
      factors++;
    }

    // Screen resolution similarity
    if (fp1.screenResolution && fp2.screenResolution) {
      if (fp1.screenResolution === fp2.screenResolution) {
        score += 0.2;
      }
      factors++;
    }

    // Timezone similarity
    if (fp1.timezone && fp2.timezone) {
      if (fp1.timezone === fp2.timezone) {
        score += 0.1;
      }
      factors++;
    }

    return Math.min(score, 1.0);
  }

  /**
   * Check if two user agents are similar (same browser, different version)
   */
  private isSimilarUserAgent(ua1: string, ua2: string): boolean {
    const browsers = ['Chrome', 'Firefox', 'Safari', 'Edge'];

    for (const browser of browsers) {
      if (ua1.includes(browser) && ua2.includes(browser)) {
        return true;
      }
    }

    return false;
  }
}
