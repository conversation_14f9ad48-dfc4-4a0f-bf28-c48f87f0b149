import { Injectable, Logger } from '@nestjs/common';
import { Request } from 'express';
import * as crypto from 'crypto';

export interface SecurityEvent {
  type: 'login_attempt' | 'login_success' | 'login_failure' | 'token_refresh' | 'suspicious_activity';
  userId?: string;
  ip: string;
  userAgent: string;
  timestamp: Date;
  details?: any;
}

export interface SecurityContext {
  ip: string;
  userAgent: string;
  fingerprint: string;
  country?: string;
  city?: string;
  isp?: string;
}

@Injectable()
export class SecurityService {
  private readonly logger = new Logger(SecurityService.name);
  private readonly suspiciousIPs = new Set<string>();
  private readonly loginAttempts = new Map<string, { count: number; lastAttempt: Date }>();

  /**
   * Extract security context from request
   */
  extractSecurityContext(req: Request): SecurityContext {
    const ip = this.getClientIP(req);
    const userAgent = req.headers['user-agent'] || 'Unknown';
    
    // Create a basic fingerprint from available headers
    const fingerprint = this.createFingerprint(req);

    return {
      ip,
      userAgent,
      fingerprint,
    };
  }

  /**
   * Get real client IP address
   */
  private getClientIP(req: Request): string {
    const forwarded = req.headers['x-forwarded-for'] as string;
    const realIP = req.headers['x-real-ip'] as string;
    const cfConnectingIP = req.headers['cf-connecting-ip'] as string;
    
    if (forwarded) {
      return forwarded.split(',')[0].trim();
    }
    
    if (realIP) {
      return realIP;
    }
    
    if (cfConnectingIP) {
      return cfConnectingIP;
    }
    
    return req.connection.remoteAddress || req.socket.remoteAddress || 'unknown';
  }

  /**
   * Create device fingerprint from request headers
   */
  private createFingerprint(req: Request): string {
    const components = [
      req.headers['user-agent'] || '',
      req.headers['accept-language'] || '',
      req.headers['accept-encoding'] || '',
      req.headers['accept'] || '',
      req.headers['connection'] || '',
    ];

    const fingerprint = components.join('|');
    return crypto.createHash('sha256').update(fingerprint).digest('hex');
  }

  /**
   * Check if IP is suspicious
   */
  isSuspiciousIP(ip: string): boolean {
    return this.suspiciousIPs.has(ip);
  }

  /**
   * Mark IP as suspicious
   */
  markIPAsSuspicious(ip: string, reason: string): void {
    this.suspiciousIPs.add(ip);
    this.logger.warn(`IP ${ip} marked as suspicious: ${reason}`);
  }

  /**
   * Track login attempt
   */
  trackLoginAttempt(ip: string, success: boolean): boolean {
    const key = `login_${ip}`;
    const now = new Date();
    const attempt = this.loginAttempts.get(key);

    if (!attempt) {
      this.loginAttempts.set(key, { count: 1, lastAttempt: now });
      return true;
    }

    // Reset counter if last attempt was more than 15 minutes ago
    const timeDiff = now.getTime() - attempt.lastAttempt.getTime();
    if (timeDiff > 15 * 60 * 1000) {
      this.loginAttempts.set(key, { count: 1, lastAttempt: now });
      return true;
    }

    if (success) {
      // Reset counter on successful login
      this.loginAttempts.delete(key);
      return true;
    }

    // Increment failed attempts
    attempt.count++;
    attempt.lastAttempt = now;

    // Block after 5 failed attempts
    if (attempt.count >= 5) {
      this.markIPAsSuspicious(ip, `Too many failed login attempts: ${attempt.count}`);
      return false;
    }

    return true;
  }

  /**
   * Validate device consistency
   */
  validateDeviceConsistency(
    storedFingerprint: string,
    currentFingerprint: string,
    threshold: number = 0.8
  ): boolean {
    if (!storedFingerprint || !currentFingerprint) {
      return false;
    }

    // Simple similarity check - in production, use more sophisticated algorithms
    const similarity = this.calculateSimilarity(storedFingerprint, currentFingerprint);
    return similarity >= threshold;
  }

  /**
   * Calculate similarity between two fingerprints
   */
  private calculateSimilarity(fp1: string, fp2: string): number {
    if (fp1 === fp2) return 1.0;
    
    // Simple Hamming distance for hex strings
    let matches = 0;
    const length = Math.min(fp1.length, fp2.length);
    
    for (let i = 0; i < length; i++) {
      if (fp1[i] === fp2[i]) {
        matches++;
      }
    }
    
    return matches / Math.max(fp1.length, fp2.length);
  }

  /**
   * Generate CSRF token
   */
  generateCSRFToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Validate CSRF token
   */
  validateCSRFToken(token: string, sessionToken: string): boolean {
    if (!token || !sessionToken) {
      return false;
    }
    
    return crypto.timingSafeEqual(
      Buffer.from(token, 'hex'),
      Buffer.from(sessionToken, 'hex')
    );
  }

  /**
   * Detect suspicious patterns
   */
  detectSuspiciousActivity(context: SecurityContext, userId?: string): string[] {
    const suspiciousPatterns: string[] = [];

    // Check for suspicious user agents
    if (this.isSuspiciousUserAgent(context.userAgent)) {
      suspiciousPatterns.push('suspicious_user_agent');
    }

    // Check for known bad IPs (simplified - in production use threat intelligence)
    if (this.isKnownBadIP(context.ip)) {
      suspiciousPatterns.push('known_bad_ip');
    }

    // Check for rapid requests from same IP
    if (this.isRapidRequests(context.ip)) {
      suspiciousPatterns.push('rapid_requests');
    }

    return suspiciousPatterns;
  }

  private isSuspiciousUserAgent(userAgent: string): boolean {
    const suspiciousPatterns = [
      /bot/i,
      /crawler/i,
      /spider/i,
      /scraper/i,
      /curl/i,
      /wget/i,
      /python/i,
    ];

    return suspiciousPatterns.some(pattern => pattern.test(userAgent));
  }

  private isKnownBadIP(ip: string): boolean {
    // In production, integrate with threat intelligence feeds
    const knownBadIPs = [
      '127.0.0.1', // Example - remove in production
    ];
    
    return knownBadIPs.includes(ip);
  }

  private isRapidRequests(ip: string): boolean {
    // Simplified rate limiting check
    // In production, use Redis or similar for distributed rate limiting
    return false;
  }

  /**
   * Clean up old data
   */
  cleanup(): void {
    const now = new Date();
    const cutoff = new Date(now.getTime() - 24 * 60 * 60 * 1000); // 24 hours ago

    // Clean up old login attempts
    for (const [key, attempt] of this.loginAttempts.entries()) {
      if (attempt.lastAttempt < cutoff) {
        this.loginAttempts.delete(key);
      }
    }

    this.logger.debug('Security service cleanup completed');
  }
}
