import { SetMetadata, UseGuards, applyDecorators } from '@nestjs/common';
import { ThrottlerGuard, Throttle } from '@nestjs/throttler';
import { SecurityGuard, SecurityOptions, SECURITY_OPTIONS_KEY } from '../guards/security.guard';

/**
 * Apply security measures to an endpoint
 */
export function Security(options: SecurityOptions = {}) {
  return applyDecorators(
    SetMetadata(SECURITY_OPTIONS_KEY, options),
    UseGuards(SecurityGuard),
  );
}

/**
 * Apply rate limiting to an endpoint
 */
export function RateLimit(name: string, limit?: number, ttl?: number) {
  const decorators = [UseGuards(ThrottlerGuard)];
  
  if (limit && ttl) {
    decorators.push(Throttle({ [name]: { limit, ttl } }));
  } else {
    decorators.push(Throttle({ [name]: { limit: 10, ttl: 60000 } })); // Default: 10 requests per minute
  }
  
  return applyDecorators(...decorators);
}

/**
 * Apply strict security for authentication endpoints
 */
export function AuthSecurity() {
  return applyDecorators(
    Security({
      checkSuspiciousIP: true,
      blockSuspiciousUserAgents: true,
      logActivity: true,
    }),
    RateLimit('auth', 5, 900000), // 5 attempts per 15 minutes
  );
}

/**
 * Apply moderate security for API endpoints
 */
export function ApiSecurity() {
  return applyDecorators(
    Security({
      checkSuspiciousIP: true,
      logActivity: false,
    }),
    RateLimit('medium', 20, 10000), // 20 requests per 10 seconds
  );
}

/**
 * Apply light security for public endpoints
 */
export function PublicSecurity() {
  return applyDecorators(
    Security({
      checkSuspiciousIP: false,
      blockSuspiciousUserAgents: true,
      logActivity: false,
    }),
    RateLimit('long', 100, 60000), // 100 requests per minute
  );
}
