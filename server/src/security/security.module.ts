import { Module } from '@nestjs/common';
import { ThrottlerModule } from '@nestjs/throttler';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { SecurityService } from './services/security.service';
import { DeviceFingerprintService } from './services/device-fingerprint.service';
import { SecurityMonitoringService } from './services/security-monitoring.service';

@Module({
  imports: [
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => [
        {
          name: 'short',
          ttl: 1000, // 1 second
          limit: 3, // 3 requests per second
        },
        {
          name: 'medium',
          ttl: 10000, // 10 seconds
          limit: 20, // 20 requests per 10 seconds
        },
        {
          name: 'long',
          ttl: 60000, // 1 minute
          limit: 100, // 100 requests per minute
        },
        {
          name: 'auth',
          ttl: 900000, // 15 minutes
          limit: 5, // 5 login attempts per 15 minutes
        },
      ],
    }),
  ],
  providers: [
    SecurityService,
    DeviceFingerprintService,
    SecurityMonitoringService,
  ],
  exports: [
    SecurityService,
    DeviceFingerprintService,
    SecurityMonitoringService,
  ],
})
export class SecurityModule {}
