import { DataSource, DataSourceOptions } from 'typeorm';
import { config } from 'dotenv';
import { User } from '../users/entities/user.entity';
import { Agent } from '../agents/entities/agent.entity';
import { Call } from '../calls/entities/call.entity';
import { CallData } from '../calls/entities/call-data.entity';
import { CallTranscript } from '../calls/entities/call-transcript.entity';
import { ExtractedInformation } from '../calls/entities/extracted-information.entity';
import { CallAnalysis } from '../calls/entities/call-analysis.entity';
import { CalendarConnection } from '../integrations/google/entities/calendar-connection.entity';
import { RefreshToken } from '../auth/entities/refresh-token.entity';

// Load environment variables from .env file
config();

export const dataSourceOptions: DataSourceOptions = {
  type: 'postgres',
  host: process.env.DATABASE_HOST || 'localhost',
  port: parseInt(process.env.DATABASE_PORT || '5432', 10),
  username: process.env.DATABASE_USERNAME || 'postgres',
  password: process.env.DATABASE_PASSWORD || 'postgres',
  database: process.env.DATABASE_NAME || 'talkloop',
  entities: [
    User,
    Agent,
    Call,
    CallData,
    CallTranscript,
    ExtractedInformation,
    CallAnalysis,
    CalendarConnection,
    RefreshToken,
  ],
  migrations: ['dist/database/migrations/*.js'],
  synchronize: process.env.NODE_ENV === 'development',
  logging: process.env.NODE_ENV === 'development',
};

const dataSource = new DataSource(dataSourceOptions);
export default dataSource;
