import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Call, CallStatus } from '../../calls/entities/call.entity';
import { CallData } from '../../calls/entities/call-data.entity';
import { Agent } from '../../agents/entities/agent.entity';
import {
  CustomerInfo,
  AppointmentDetails,
} from '../../calls/types/call-data.types';

@Injectable()
export class CallSeeder {
  constructor(
    @InjectRepository(Call)
    private callRepository: Repository<Call>,
    @InjectRepository(CallData)
    private callDataRepository: Repository<CallData>,
    @InjectRepository(Agent)
    private agentRepository: Repository<Agent>,
  ) {}

  async seed(): Promise<void> {
    // Get all agents
    const agents = await this.agentRepository.find({
      relations: ['user'],
    });

    if (agents.length === 0) {
      console.log('No agents found. Please run the agent seeder first.');
      return;
    }

    // For each agent, create some calls
    for (const agent of agents) {
      // Check if agent already has calls
      const existingCalls = await this.callRepository.count({
        where: { agent: { id: agent.id } },
      });

      if (existingCalls > 0) {
        console.log(
          `Agent "${agent.name}" already has ${existingCalls} calls. Skipping.`,
        );
        continue;
      }

      // Create 3-5 calls for each agent
      const numCalls = Math.floor(3 + Math.random() * 3);

      for (let i = 0; i < numCalls; i++) {
        // Create a call
        const call = this.callRepository.create({
          agent: agent,
          twilioCallSid: `CA${Math.random().toString(36).substring(2, 15)}`,
          callerNumber: `+1${Math.floor(1000000000 + Math.random() * 9000000000)}`,
          callerName: this.getRandomCallerName(),
          status: this.getRandomCallStatus(),
          duration: Math.floor(30 + Math.random() * 600), // 30 seconds to 10 minutes
          recordingUrl: 'https://example.com/recording.mp3',
          transcriptionUrl: 'https://example.com/transcription.txt',
        });

        const savedCall = await this.callRepository.save(call);

        // Create call data
        const callData = this.callDataRepository.create({
          customerInfo: this.getRandomCustomerInfo(),
          summary: this.getRandomSummary(),
        });

        // Save call data first
        const savedCallData = await this.callDataRepository.save(callData);

        // Update the call with the call data
        savedCall.callData = savedCallData;
        await this.callRepository.save(savedCall);

        // Add appointment details to some calls
        if (Math.random() > 0.6) {
          savedCallData.appointmentDetails = this.getRandomAppointmentDetails();
          await this.callDataRepository.save(savedCallData);
        }
      }

      console.log(`Created ${numCalls} calls for agent "${agent.name}"`);
    }
  }

  private getRandomCallerName(): string {
    const firstNames = [
      'Michael',
      'Sarah',
      'David',
      'Emma',
      'James',
      'Olivia',
      'Robert',
      'Sophia',
    ];
    const lastNames = [
      'Johnson',
      'Smith',
      'Williams',
      'Brown',
      'Jones',
      'Miller',
      'Davis',
      'Garcia',
    ];

    return `${firstNames[Math.floor(Math.random() * firstNames.length)]} ${lastNames[Math.floor(Math.random() * lastNames.length)]}`;
  }

  private getRandomCallStatus(): CallStatus {
    const statuses = [
      CallStatus.COMPLETED,
      CallStatus.COMPLETED,
      CallStatus.COMPLETED,
      CallStatus.FAILED,
    ];
    return statuses[Math.floor(Math.random() * statuses.length)];
  }

  private getRandomCustomerInfo(): CustomerInfo {
    return {
      name: this.getRandomCallerName(),
      email: `customer${Math.floor(Math.random() * 1000)}@example.com`,
      phone: `+1${Math.floor(1000000000 + Math.random() * 9000000000)}`,
      notes: 'Customer called about our services.',
    };
  }

  private getRandomSummary(): string {
    const summaries = [
      'Customer called to inquire about pricing for our services.',
      'Customer wanted to schedule an appointment for next week.',
      'Customer had questions about our product features.',
      'Customer called to reschedule their existing appointment.',
      'Customer wanted information about our business hours.',
    ];

    return summaries[Math.floor(Math.random() * summaries.length)];
  }

  private getRandomAppointmentDetails(): AppointmentDetails {
    // Generate a random date in the next 14 days
    const date = new Date();
    date.setDate(date.getDate() + Math.floor(1 + Math.random() * 14));
    date.setHours(
      9 + Math.floor(Math.random() * 8),
      Math.floor(Math.random() * 4) * 15,
      0,
      0,
    );

    return {
      date: date.toISOString(),
      duration: 30 + Math.floor(Math.random() * 4) * 15, // 30, 45, 60, or 75 minutes
      type: ['Consultation', 'Follow-up', 'Initial Meeting', 'Demo'][
        Math.floor(Math.random() * 4)
      ],
      notes: 'Customer requested this specific time slot.',
    };
  }
}
