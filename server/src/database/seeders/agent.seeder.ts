import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Agent } from '../../agents/entities/agent.entity';
import { User } from '../../users/entities/user.entity';

@Injectable()
export class AgentSeeder {
  constructor(
    @InjectRepository(Agent)
    private agentRepository: Repository<Agent>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async seed(): Promise<void> {
    // Get admin user
    const adminUser = await this.userRepository.findOne({
      where: { email: '<EMAIL>' },
    });

    if (!adminUser) {
      console.log('Admin user not found. Please run the user seeder first.');
      return;
    }

    // Sample agents for admin
    const adminAgents = [
      {
        name: 'Dental Receptionist',
        prompt:
          'You are a helpful receptionist for a dental clinic. Your job is to answer calls, provide information about services, and book appointments. Be friendly and professional.',
        twilioPhoneNumber: '+15551234567',
        isActive: true,
      },
      {
        name: 'Sales Representative',
        prompt:
          'You are a sales representative for a software company. Your job is to answer calls, explain our products, and schedule demos with potential clients. Be persuasive but not pushy.',
        twilioPhoneNumber: '+15552345678',
        isActive: true,
      },
    ];

    for (const agentData of adminAgents) {
      const agentExists = await this.agentRepository.findOne({
        where: {
          name: agentData.name,
          user: { id: adminUser.id },
        },
        relations: ['user'],
      });

      if (!agentExists) {
        const agent = this.agentRepository.create({
          ...agentData,
          user: adminUser,
        });

        await this.agentRepository.save(agent);
        console.log(`Agent "${agentData.name}" created for admin user`);
      } else {
        console.log(`Agent "${agentData.name}" already exists for admin user`);
      }
    }

    // Get regular users and create agents for them
    const regularUsers = await this.userRepository.find({
      where: [{ email: '<EMAIL>' }, { email: '<EMAIL>' }],
    });

    for (const user of regularUsers) {
      const agentName = `${user.firstName}'s Assistant`;

      const agentExists = await this.agentRepository.findOne({
        where: {
          name: agentName,
          user: { id: user.id },
        },
        relations: ['user'],
      });

      if (!agentExists) {
        const agent = this.agentRepository.create({
          name: agentName,
          prompt: `You are ${user.firstName}'s personal assistant. Your job is to answer calls, schedule meetings, and take messages. Be efficient and professional.`,
          twilioPhoneNumber: `+1555${Math.floor(1000000 + Math.random() * 9000000)}`,
          isActive: true,
          user: user,
        });

        await this.agentRepository.save(agent);
        console.log(`Agent "${agentName}" created for user ${user.email}`);
      } else {
        console.log(
          `Agent "${agentName}" already exists for user ${user.email}`,
        );
      }
    }
  }
}
