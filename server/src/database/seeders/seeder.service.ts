import { Injectable } from '@nestjs/common';
import { UserSeeder } from './user.seeder';
import { AgentSeeder } from './agent.seeder';
import { CallSeeder } from './call.seeder';

@Injectable()
export class SeederService {
  constructor(
    private readonly userSeeder: UserSeeder,
    private readonly agentSeeder: AgentSeeder,
    private readonly callSeeder: CallSeeder,
  ) {}

  async seed() {
    try {
      console.log('Starting database seeding...');

      // Run seeders in order
      await this.userSeeder.seed();
      await this.agentSeeder.seed();
      await this.callSeeder.seed();

      console.log('Database seeding completed successfully!');
    } catch (error) {
      console.error('Error during database seeding:', error);
      throw error;
    }
  }
}
