import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import * as bcrypt from 'bcrypt';

@Injectable()
export class UserSeeder {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async seed(): Promise<void> {
    // Check if admin user already exists
    const adminExists = await this.userRepository.findOne({
      where: { email: '<EMAIL>' },
    });

    if (!adminExists) {
      // Create admin user
      const hashedPassword = await bcrypt.hash('password', 10);

      const admin = this.userRepository.create({
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'User',
        password: hashedPassword,
        companyName: 'Talkloop Admin',
        isEmailVerified: true,
      });

      await this.userRepository.save(admin);
      console.log('Admin user created successfully');
    } else {
      console.log('Admin user already exists');
    }

    // Create some regular users if they don't exist
    const regularUsers = [
      {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        companyName: 'Acme Inc',
      },
      {
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Smith',
        companyName: 'Tech Solutions',
      },
    ];

    for (const userData of regularUsers) {
      const userExists = await this.userRepository.findOne({
        where: { email: userData.email },
      });

      if (!userExists) {
        const hashedPassword = await bcrypt.hash('password123', 10);

        const user = this.userRepository.create({
          ...userData,
          password: hashedPassword,
          isEmailVerified: true,
        });

        await this.userRepository.save(user);
        console.log(`User ${userData.email} created successfully`);
      } else {
        console.log(`User ${userData.email} already exists`);
      }
    }
  }
}
