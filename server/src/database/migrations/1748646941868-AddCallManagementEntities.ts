import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCallManagementEntities1748646941868
  implements MigrationInterface
{
  name = 'AddCallManagementEntities1748646941868';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."call_transcript_speaker_enum" AS ENUM('user', 'agent', 'system')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."call_transcript_messagetype_enum" AS ENUM('text', 'audio', 'function_call', 'extracted_info')`,
    );
    await queryRunner.query(
      `CREATE TABLE "call_transcript" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "speaker" "public"."call_transcript_speaker_enum" NOT NULL, "messageType" "public"."call_transcript_messagetype_enum" NOT NULL DEFAULT 'text', "content" text NOT NULL, "metadata" jsonb, "extractedData" jsonb, "timestamp" TIMESTAMP WITH TIME ZONE NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "callId" uuid, CONSTRAINT "PK_73f8a93c97548ebfe9a9e72b407" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_70c8ee2bbd11df22f871d0cb0e" ON "call_transcript" ("callId", "timestamp") `,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."extracted_information_extractiontype_enum" AS ENUM('contact_info', 'appointment', 'problem_description', 'action_items', 'sentiment', 'intent', 'custom')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."extracted_information_extractionsource_enum" AS ENUM('real_time', 'post_call', 'manual')`,
    );
    await queryRunner.query(
      `CREATE TABLE "extracted_information" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "extractionType" "public"."extracted_information_extractiontype_enum" NOT NULL, "extractionSource" "public"."extracted_information_extractionsource_enum" NOT NULL DEFAULT 'real_time', "key" character varying NOT NULL, "value" jsonb NOT NULL, "confidence" double precision, "context" text, "verified" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "callId" uuid, CONSTRAINT "PK_47f08979ec7f769af5e2ea09b82" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_6c8eef5ef123c55b2455aedb47" ON "extracted_information" ("callId", "extractionType") `,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."call_analysis_status_enum" AS ENUM('pending', 'in_progress', 'completed', 'failed')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."call_analysis_outcome_enum" AS ENUM('successful', 'needs_followup', 'issue_resolved', 'appointment_scheduled', 'information_gathered', 'escalated', 'disconnected')`,
    );
    await queryRunner.query(
      `CREATE TABLE "call_analysis" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "status" "public"."call_analysis_status_enum" NOT NULL DEFAULT 'pending', "summary" text, "keyTakeaways" text, "actionItems" jsonb, "outcome" "public"."call_analysis_outcome_enum", "sentiment" jsonb, "topics" jsonb, "callQuality" jsonb, "recommendations" jsonb, "errorMessage" text, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "callId" uuid, CONSTRAINT "REL_d9230703bde4273355d645f6bb" UNIQUE ("callId"), CONSTRAINT "PK_99ce8b74f8537736cdd6fe916b7" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."call_type_enum" AS ENUM('phone', 'test', 'simulation')`,
    );
    await queryRunner.query(
      `ALTER TABLE "call" ADD "type" "public"."call_type_enum" NOT NULL DEFAULT 'phone'`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."call_source_enum" AS ENUM('inbound', 'outbound', 'test')`,
    );
    await queryRunner.query(
      `ALTER TABLE "call" ADD "source" "public"."call_source_enum" NOT NULL DEFAULT 'inbound'`,
    );
    await queryRunner.query(
      `ALTER TABLE "call" ADD "testSessionId" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "call" ADD "provider" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "call" ALTER COLUMN "twilioCallSid" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "call" ALTER COLUMN "callerNumber" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."call_status_enum" RENAME TO "call_status_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."call_status_enum" AS ENUM('ongoing', 'completed', 'failed', 'analyzing')`,
    );
    await queryRunner.query(
      `ALTER TABLE "call" ALTER COLUMN "status" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "call" ALTER COLUMN "status" TYPE "public"."call_status_enum" USING "status"::"text"::"public"."call_status_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "call" ALTER COLUMN "status" SET DEFAULT 'ongoing'`,
    );
    await queryRunner.query(`DROP TYPE "public"."call_status_enum_old"`);
    await queryRunner.query(
      `ALTER TABLE "call_transcript" ADD CONSTRAINT "FK_9d2836fd4991839942c934f35c3" FOREIGN KEY ("callId") REFERENCES "call"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "extracted_information" ADD CONSTRAINT "FK_646977d9c372f1dbd74b8bdc595" FOREIGN KEY ("callId") REFERENCES "call"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "call_analysis" ADD CONSTRAINT "FK_d9230703bde4273355d645f6bb7" FOREIGN KEY ("callId") REFERENCES "call"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "call_analysis" DROP CONSTRAINT "FK_d9230703bde4273355d645f6bb7"`,
    );
    await queryRunner.query(
      `ALTER TABLE "extracted_information" DROP CONSTRAINT "FK_646977d9c372f1dbd74b8bdc595"`,
    );
    await queryRunner.query(
      `ALTER TABLE "call_transcript" DROP CONSTRAINT "FK_9d2836fd4991839942c934f35c3"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."call_status_enum_old" AS ENUM('ongoing', 'completed', 'failed')`,
    );
    await queryRunner.query(
      `ALTER TABLE "call" ALTER COLUMN "status" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "call" ALTER COLUMN "status" TYPE "public"."call_status_enum_old" USING "status"::"text"::"public"."call_status_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "call" ALTER COLUMN "status" SET DEFAULT 'ongoing'`,
    );
    await queryRunner.query(`DROP TYPE "public"."call_status_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."call_status_enum_old" RENAME TO "call_status_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "call" ALTER COLUMN "callerNumber" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "call" ALTER COLUMN "twilioCallSid" SET NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "call" DROP COLUMN "provider"`);
    await queryRunner.query(`ALTER TABLE "call" DROP COLUMN "testSessionId"`);
    await queryRunner.query(`ALTER TABLE "call" DROP COLUMN "source"`);
    await queryRunner.query(`DROP TYPE "public"."call_source_enum"`);
    await queryRunner.query(`ALTER TABLE "call" DROP COLUMN "type"`);
    await queryRunner.query(`DROP TYPE "public"."call_type_enum"`);
    await queryRunner.query(`DROP TABLE "call_analysis"`);
    await queryRunner.query(`DROP TYPE "public"."call_analysis_outcome_enum"`);
    await queryRunner.query(`DROP TYPE "public"."call_analysis_status_enum"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_6c8eef5ef123c55b2455aedb47"`,
    );
    await queryRunner.query(`DROP TABLE "extracted_information"`);
    await queryRunner.query(
      `DROP TYPE "public"."extracted_information_extractionsource_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."extracted_information_extractiontype_enum"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_70c8ee2bbd11df22f871d0cb0e"`,
    );
    await queryRunner.query(`DROP TABLE "call_transcript"`);
    await queryRunner.query(
      `DROP TYPE "public"."call_transcript_messagetype_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."call_transcript_speaker_enum"`,
    );
  }
}
