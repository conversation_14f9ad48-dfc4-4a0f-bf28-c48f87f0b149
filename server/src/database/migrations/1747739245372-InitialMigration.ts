import { MigrationInterface, QueryRunner } from 'typeorm';

export class InitialMigration1747739245372 implements MigrationInterface {
  name = 'InitialMigration1747739245372';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "call_data" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "customerInfo" jsonb, "appointmentDetails" jsonb, "requestDetails" jsonb, "summary" text, "googleCalendarEventId" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_0356064e6c7a5b2aa761d07486f" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."call_status_enum" AS ENUM('ongoing', 'completed', 'failed')`,
    );
    await queryRunner.query(
      `CREATE TABLE "call" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "twilioCallSid" character varying NOT NULL, "callerNumber" character varying NOT NULL, "callerName" character varying, "status" "public"."call_status_enum" NOT NULL DEFAULT 'ongoing', "duration" integer, "recordingUrl" character varying, "transcriptionUrl" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "agentId" uuid, "callDataId" uuid, CONSTRAINT "REL_9d47979adf866ebdb76c489c7a" UNIQUE ("callDataId"), CONSTRAINT "PK_2098af0169792a34f9cfdd39c47" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "agent" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL, "prompt" text NOT NULL, "twilioPhoneNumber" character varying, "isActive" boolean NOT NULL DEFAULT false, "googleCalendarId" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "userId" uuid, CONSTRAINT "PK_1000e989398c5d4ed585cf9a46f" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "user" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "email" character varying NOT NULL, "firstName" character varying NOT NULL, "lastName" character varying NOT NULL, "companyName" character varying, "phoneNumber" character varying, "password" character varying NOT NULL, "isEmailVerified" boolean NOT NULL DEFAULT false, "googleId" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_e12875dfb3b1d92d7d7c5377e22" UNIQUE ("email"), CONSTRAINT "PK_cace4a159ff9f2512dd42373760" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "calendar_connection" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "calendarId" character varying NOT NULL, "calendarName" character varying NOT NULL, "accessToken" character varying NOT NULL, "refreshToken" character varying NOT NULL, "tokenExpiry" TIMESTAMP NOT NULL, "isActive" boolean NOT NULL DEFAULT true, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "userId" uuid, CONSTRAINT "PK_0a1c6279c8e8e5ccd43f9455650" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "call" ADD CONSTRAINT "FK_bb48df8756cdbbfb48ca0f054cf" FOREIGN KEY ("agentId") REFERENCES "agent"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "call" ADD CONSTRAINT "FK_9d47979adf866ebdb76c489c7a7" FOREIGN KEY ("callDataId") REFERENCES "call_data"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "agent" ADD CONSTRAINT "FK_15baaa1eb6dd8d1f0a92a17d667" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "calendar_connection" ADD CONSTRAINT "FK_dd0599010905e879846d27aa9c5" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "calendar_connection" DROP CONSTRAINT "FK_dd0599010905e879846d27aa9c5"`,
    );
    await queryRunner.query(
      `ALTER TABLE "agent" DROP CONSTRAINT "FK_15baaa1eb6dd8d1f0a92a17d667"`,
    );
    await queryRunner.query(
      `ALTER TABLE "call" DROP CONSTRAINT "FK_9d47979adf866ebdb76c489c7a7"`,
    );
    await queryRunner.query(
      `ALTER TABLE "call" DROP CONSTRAINT "FK_bb48df8756cdbbfb48ca0f054cf"`,
    );
    await queryRunner.query(`DROP TABLE "calendar_connection"`);
    await queryRunner.query(`DROP TABLE "user"`);
    await queryRunner.query(`DROP TABLE "agent"`);
    await queryRunner.query(`DROP TABLE "call"`);
    await queryRunner.query(`DROP TYPE "public"."call_status_enum"`);
    await queryRunner.query(`DROP TABLE "call_data"`);
  }
}
