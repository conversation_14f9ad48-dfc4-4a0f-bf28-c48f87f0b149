import { MigrationInterface, QueryRunner } from "typeorm";

export class AddRefreshTokenSystem1749084128231 implements MigrationInterface {
    name = 'AddRefreshTokenSystem1749084128231'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "refresh_tokens" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" character varying NOT NULL, "token_hash" character varying(255) NOT NULL, "device_info" jsonb, "expires_at" TIMESTAMP NOT NULL, "last_used_at" TIMESTAMP NOT NULL DEFAULT now(), "is_revoked" boolean NOT NULL DEFAULT false, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "userId" uuid, CONSTRAINT "PK_7d8bee0204106019488c4c50ffa" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_ba3bd69c8ad1e799c0256e9e50" ON "refresh_tokens" ("expires_at") `);
        await queryRunner.query(`CREATE INDEX "IDX_a7838d2ba25be1342091b6695f" ON "refresh_tokens" ("token_hash") `);
        await queryRunner.query(`CREATE INDEX "IDX_14187aa4d2d58318c82c62c7ea" ON "refresh_tokens" ("user_id", "is_revoked") `);
        await queryRunner.query(`ALTER TABLE "user" ADD "last_login_at" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "user" ADD "session_count" integer NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "refresh_tokens" ADD CONSTRAINT "FK_610102b60fea1455310ccd299de" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "refresh_tokens" DROP CONSTRAINT "FK_610102b60fea1455310ccd299de"`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "session_count"`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "last_login_at"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_14187aa4d2d58318c82c62c7ea"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_a7838d2ba25be1342091b6695f"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_ba3bd69c8ad1e799c0256e9e50"`);
        await queryRunner.query(`DROP TABLE "refresh_tokens"`);
    }

}
