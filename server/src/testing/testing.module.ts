import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TestingGateway } from './testing.gateway';
import { TestingService } from './testing.service';
import { TestingController } from './testing.controller';
import { AgentsModule } from '../agents/agents.module';
import { RealtimeModule } from '../integrations/realtime/realtime.module';
import { CallsModule } from '../calls/calls.module';
import { Call } from '../calls/entities/call.entity';
import { CallTranscript } from '../calls/entities/call-transcript.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Call, CallTranscript]),
    AgentsModule,
    RealtimeModule,
    CallsModule,
  ],
  providers: [TestingGateway, TestingService],
  controllers: [TestingController],
  exports: [TestingService],
})
export class TestingModule {}
