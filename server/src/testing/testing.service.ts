import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AgentsService } from '../agents/agents.service';
import { InformationExtractionService } from '../calls/services/information-extraction.service';
import { TestCallSession } from './dto/test-call.dto';
import {
  Call,
  CallType,
  CallSource,
  CallStatus,
} from '../calls/entities/call.entity';
import {
  CallTranscript,
  SpeakerType,
  MessageType,
} from '../calls/entities/call-transcript.entity';
import { CallOutcome } from '../calls/entities/call-analysis.entity';
import {
  ExtractedInformation,
  ExtractionType,
} from '../calls/entities/extracted-information.entity';
import { Agent } from '../agents/entities/agent.entity';
import { v4 as uuidv4 } from 'uuid';
import {
  TranscriptMetadata,
  ExtractedData,
} from '../calls/types/call-data.types';

// Define interfaces for better type safety
interface ExtractedContactInfo {
  email?: string;
  name?: string;
  phone?: string;
  confidence: number;
}

interface ExtractedProblemInfo {
  problem: string;
  category: string;
  urgency: string;
  confidence: number;
}

interface SentimentScores {
  positive: number;
  negative: number;
  neutral: number;
}

interface TopicAnalysis {
  topic: string;
  confidence: number;
  mentions: number;
}

interface CallQualityMetrics {
  audioQuality: number;
  interruptionCount: number;
  silencePeriods: number;
  speakingTime: {
    user: number;
    agent: number;
  };
}

interface SentimentAnalysis {
  overall: 'positive' | 'negative' | 'neutral';
  scores: SentimentScores;
  timeline: Array<{
    timestamp: Date;
    sentiment: 'positive' | 'negative' | 'neutral';
    score: number;
  }>;
}

@Injectable()
export class TestingService {
  private readonly logger = new Logger(TestingService.name);
  private activeSessions = new Map<string, TestCallSession>();

  constructor(
    private readonly agentsService: AgentsService,
    private readonly informationExtractionService: InformationExtractionService,
    @InjectRepository(Call)
    private readonly callRepository: Repository<Call>,
    @InjectRepository(CallTranscript)
    private readonly transcriptRepository: Repository<CallTranscript>,
  ) {}

  async createTestSession(
    agentId: string,
    userId: string,
    provider?: string,
  ): Promise<TestCallSession> {
    // Verify agent exists and belongs to user
    const agent = await this.agentsService.findOne(agentId, userId);
    if (!agent) {
      throw new NotFoundException('Agent not found');
    }

    const sessionId = uuidv4();

    // Create persistent call record
    const call = new Call();
    call.type = CallType.TEST;
    call.source = CallSource.TEST;
    call.testSessionId = sessionId;
    call.status = CallStatus.ONGOING;
    call.provider = provider ?? '';
    call.agent = agent;

    const savedCall = await this.callRepository.save(call);

    // Add initial transcript entry
    const startTranscript = new CallTranscript();
    startTranscript.call = savedCall;
    startTranscript.speaker = SpeakerType.SYSTEM;
    startTranscript.messageType = MessageType.TEXT;
    startTranscript.content = `Test session started with ${provider} provider`;
    startTranscript.timestamp = new Date();

    await this.transcriptRepository.save(startTranscript);

    const session: TestCallSession = {
      id: sessionId,
      agentId,
      userId,
      startedAt: new Date(),
      status: 'active',
      transcript: [],
      callId: savedCall.id, // Link to persistent call
    };

    this.activeSessions.set(sessionId, session);
    this.logger.log(
      `Created test session ${sessionId} for agent ${agentId} (Call ID: ${savedCall.id})`,
    );

    return session;
  }

  getSession(sessionId: string): TestCallSession | undefined {
    return this.activeSessions.get(sessionId);
  }

  async addToTranscript(
    sessionId: string,
    speaker: 'user' | 'agent' | 'system',
    content: string,
    type: 'text' | 'audio' | 'function_call' = 'text',
    metadata?: Record<string, unknown>,
    extractedData?: Record<string, unknown>,
  ): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (session) {
      // Add to in-memory session
      session.transcript.push({
        timestamp: new Date(),
        speaker,
        content,
        type,
      });

      // Persist to database if we have a call ID
      if (session.callId) {
        const call = await this.callRepository.findOne({
          where: { id: session.callId },
          relations: ['agent'],
        });

        if (call) {
          const transcript = new CallTranscript();
          transcript.call = call;
          transcript.speaker = speaker as SpeakerType;
          transcript.messageType = type as MessageType;
          transcript.content = content;
          transcript.timestamp = new Date();
          if (metadata) {
            transcript.metadata = metadata as TranscriptMetadata;
          }
          if (extractedData) {
            transcript.extractedData = extractedData as ExtractedData;
          }

          await this.transcriptRepository.save(transcript);

          // Trigger hybrid information extraction for user messages
          if (speaker === 'user' && type === 'text' && content.trim()) {
            await this.triggerHybridInformationExtraction(
              call,
              content,
              session,
            );
          }
        }
      }
    }
  }

  private async triggerHybridInformationExtraction(
    call: Call,
    userMessage: string,
    session: TestCallSession,
  ): Promise<void> {
    try {
      // Get conversation history for context
      const conversationHistory = session.transcript
        .slice(-5) // Last 5 messages for context
        .map((t) => `${t.speaker}: ${t.content}`)
        .join('\n');

      // Try AI-powered extraction first
      let extractedItems =
        await this.informationExtractionService.extractFromMessage(
          call,
          userMessage,
          conversationHistory,
        );

      // If AI extraction didn't find anything, fall back to pattern matching
      if (extractedItems.length === 0) {
        extractedItems = await this.fallbackPatternExtraction(
          call,
          userMessage,
        );
      }

      this.logger.log(
        `Extracted ${extractedItems.length} items from message: "${userMessage}"`,
      );
    } catch (error) {
      this.logger.error('Error during hybrid information extraction:', error);

      // Final fallback to simple pattern matching
      try {
        await this.fallbackPatternExtraction(call, userMessage);
      } catch (fallbackError) {
        this.logger.error('Fallback extraction also failed:', fallbackError);
      }
    }
  }

  private async fallbackPatternExtraction(
    call: Call,
    userMessage: string,
  ): Promise<ExtractedInformation[]> {
    const extractedItems: ExtractedInformation[] = [];

    try {
      // Extract contact information
      const emailMatch = userMessage.match(/[\w.-]+@[\w.-]+\.\w+/);
      const nameMatch = userMessage.match(
        /(?:I'm|I am|my name is|this is)\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)/i,
      );
      const phoneMatch = userMessage.match(
        /(\+?1?\s*\(?[0-9]{3}\)?[\s-]?[0-9]{3}[\s-]?[0-9]{4})/,
      );

      if (emailMatch || nameMatch || phoneMatch) {
        const contactInfo: ExtractedContactInfo = {
          confidence: 0.6, // Lower confidence for pattern matching
        };

        if (emailMatch) contactInfo.email = emailMatch[0];
        if (nameMatch) contactInfo.name = nameMatch[1];
        if (phoneMatch) contactInfo.phone = phoneMatch[0];

        const items =
          await this.informationExtractionService.processFunctionCall(
            call,
            'extract_contact_info',
            contactInfo,
            `Pattern extracted from: "${userMessage}"`,
          );
        extractedItems.push(...items);
      }

      // Extract problem descriptions
      const problemKeywords = [
        'problem',
        'issue',
        'trouble',
        'help',
        'question',
        'billing',
        'account',
        'technical',
      ];
      const hasProblems = problemKeywords.some((keyword) =>
        userMessage.toLowerCase().includes(keyword),
      );

      if (hasProblems) {
        const category = this.categorizeMessage(userMessage);
        const problemInfo: ExtractedProblemInfo = {
          problem: userMessage,
          category,
          urgency: 'medium',
          confidence: 0.5, // Lower confidence for pattern matching
        };

        const items =
          await this.informationExtractionService.processFunctionCall(
            call,
            'extract_problem_description',
            problemInfo,
            `Pattern extracted problem from: "${userMessage}"`,
          );
        extractedItems.push(...items);
      }
    } catch (error) {
      this.logger.error('Error in fallback pattern extraction:', error);
    }

    return extractedItems;
  }

  private categorizeMessage(message: string): string {
    const lowerMessage = message.toLowerCase();

    if (
      lowerMessage.includes('billing') ||
      lowerMessage.includes('bill') ||
      lowerMessage.includes('payment')
    ) {
      return 'billing';
    }
    if (
      lowerMessage.includes('account') ||
      lowerMessage.includes('login') ||
      lowerMessage.includes('password')
    ) {
      return 'account';
    }
    if (
      lowerMessage.includes('technical') ||
      lowerMessage.includes('error') ||
      lowerMessage.includes('bug')
    ) {
      return 'technical';
    }
    if (
      lowerMessage.includes('appointment') ||
      lowerMessage.includes('schedule') ||
      lowerMessage.includes('booking')
    ) {
      return 'appointment';
    }

    return 'other';
  }

  async endSession(sessionId: string): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (session) {
      session.status = 'ended';

      // Update call status in database
      if (session.callId) {
        const call = await this.callRepository.findOne({
          where: { id: session.callId },
          relations: ['transcripts', 'extractedInformation'],
        });

        if (call) {
          call.status = CallStatus.ANALYZING;
          call.duration = Math.floor(
            (new Date().getTime() - session.startedAt.getTime()) / 1000,
          );
          await this.callRepository.save(call);

          // Add end transcript entry
          const endTranscript = new CallTranscript();
          endTranscript.call = call;
          endTranscript.speaker = SpeakerType.SYSTEM;
          endTranscript.messageType = MessageType.TEXT;
          endTranscript.content = `Test session ended after ${call.duration} seconds`;
          endTranscript.timestamp = new Date();

          await this.transcriptRepository.save(endTranscript);

          // Generate comprehensive post-call analysis
          await this.generateComprehensiveCallAnalysis(call);

          // Update call status to completed
          call.status = CallStatus.COMPLETED;
          await this.callRepository.save(call);
        }
      }

      this.logger.log(`Ended test session ${sessionId}`);

      // Clean up after 5 minutes
      setTimeout(
        () => {
          this.activeSessions.delete(sessionId);
          this.logger.log(`Cleaned up test session ${sessionId}`);
        },
        5 * 60 * 1000,
      );
    }
  }

  private async generateComprehensiveCallAnalysis(call: Call): Promise<void> {
    try {
      // First, run comprehensive AI analysis to extract any missed information
      await this.informationExtractionService.performPostCallAnalysis(call);

      // Then generate the call analysis
      await this.generateCallAnalysis(call);
    } catch (error) {
      this.logger.error('Error in comprehensive call analysis:', error);
      // Fallback to basic analysis
      await this.generateCallAnalysis(call);
    }
  }

  private async generateCallAnalysis(call: Call): Promise<void> {
    try {
      // Import the CallAnalysis entity
      const { CallAnalysis } = await import(
        '../calls/entities/call-analysis.entity'
      );

      // Get repository for CallAnalysis
      const analysisRepository =
        this.callRepository.manager.getRepository(CallAnalysis);

      // Analyze transcripts
      const transcripts = call.transcripts || [];
      const extractedInfo = call.extractedInformation || [];

      // Count user and agent messages
      const userMessages = transcripts.filter(
        (t) => t.speaker === SpeakerType.USER,
      );
      // const agentMessages = transcripts.filter((t) => t.speaker === SpeakerType.AGENT);

      // Calculate speaking time (rough estimate based on message count)
      const userSpeakingTime = Math.floor((call.duration || 0) * 0.4); // 40% user
      const agentSpeakingTime = Math.floor((call.duration || 0) * 0.6); // 60% agent

      // Determine sentiment based on keywords
      const allUserText = userMessages
        .map((m) => m.content.toLowerCase())
        .join(' ');
      const sentiment = this.analyzeSentiment(allUserText);

      // Extract topics from conversation
      const topics = this.extractTopics(transcripts);

      // Generate summary
      const summary = this.generateSummary(transcripts, extractedInfo);

      // Determine outcome based on extracted information
      const outcome = this.determineOutcome(extractedInfo);

      const analysis = new CallAnalysis();
      analysis.call = call;
      analysis.summary = summary;
      analysis.keyTakeaways = `Call completed successfully with ${extractedInfo.length} pieces of information extracted.`;
      analysis.actionItems = this.generateActionItems(extractedInfo);
      analysis.outcome = outcome;
      const sentimentAnalysis: SentimentAnalysis = {
        overall: sentiment,
        scores: this.getSentimentScores(sentiment),
        timeline: [],
      };
      analysis.sentiment = sentimentAnalysis;
      analysis.topics = topics;
      const callQuality: CallQualityMetrics = {
        audioQuality: 0.95, // Simulated for test calls
        interruptionCount: 0,
        silencePeriods: 1,
        speakingTime: {
          user: userSpeakingTime,
          agent: agentSpeakingTime,
        },
      };
      analysis.callQuality = callQuality;
      analysis.recommendations = this.generateRecommendations(
        transcripts,
        extractedInfo,
      );

      await analysisRepository.save(analysis);

      this.logger.log(`Generated analysis for call ${call.id}`);
    } catch (error) {
      this.logger.error('Error generating call analysis:', error);
    }
  }

  private analyzeSentiment(text: string): 'positive' | 'negative' | 'neutral' {
    const positiveWords = [
      'good',
      'great',
      'excellent',
      'perfect',
      'thanks',
      'thank you',
      'helpful',
    ];
    const negativeWords = [
      'bad',
      'terrible',
      'awful',
      'horrible',
      'angry',
      'frustrated',
      'problem',
    ];

    const positiveCount = positiveWords.filter((word) =>
      text.includes(word),
    ).length;
    const negativeCount = negativeWords.filter((word) =>
      text.includes(word),
    ).length;

    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  private getSentimentScores(sentiment: string): SentimentScores {
    switch (sentiment) {
      case 'positive':
        return { positive: 0.7, negative: 0.1, neutral: 0.2 };
      case 'negative':
        return { positive: 0.1, negative: 0.7, neutral: 0.2 };
      default:
        return { positive: 0.3, negative: 0.2, neutral: 0.5 };
    }
  }

  private extractTopics(transcripts: CallTranscript[]): TopicAnalysis[] {
    const topics = new Map<string, number>();
    const allText = transcripts.map((t) => t.content.toLowerCase()).join(' ');

    const topicKeywords = {
      billing: ['bill', 'billing', 'payment', 'charge', 'money'],
      account: ['account', 'login', 'password', 'profile'],
      technical: ['technical', 'error', 'bug', 'issue', 'problem'],
      appointment: ['appointment', 'schedule', 'booking', 'time'],
      support: ['help', 'support', 'assistance', 'question'],
    };

    for (const [topic, keywords] of Object.entries(topicKeywords)) {
      const mentions = keywords.filter((keyword) =>
        allText.includes(keyword),
      ).length;
      if (mentions > 0) {
        topics.set(topic, mentions);
      }
    }

    return Array.from(topics.entries()).map(([topic, mentions]) => ({
      topic,
      confidence: Math.min(mentions * 0.2, 1.0),
      mentions,
    }));
  }

  private generateSummary(
    transcripts: CallTranscript[],
    extractedInfo: ExtractedInformation[],
  ): string {
    const contactInfo = extractedInfo.filter(
      (info) => info.extractionType === ExtractionType.CONTACT_INFO,
    );
    const problemInfo = extractedInfo.filter(
      (info) => info.extractionType === ExtractionType.PROBLEM_DESCRIPTION,
    );

    let summary = 'Test call completed successfully.';

    if (contactInfo.length > 0) {
      summary += ` Customer contact information was collected.`;
    }

    if (problemInfo.length > 0) {
      const categories = problemInfo
        .map((p) => {
          // Safely access the value property with proper type checking
          if (typeof p.value === 'string') {
            return p.value;
          }
          // Fallback to extraction type if value is not a string
          return p.extractionType.toString();
        })
        .join(', ');
      summary += ` Issues related to ${categories} were discussed.`;
    }

    summary += ` Total conversation length: ${transcripts.length} messages.`;

    return summary;
  }

  private determineOutcome(extractedInfo: ExtractedInformation[]): CallOutcome {
    if (
      extractedInfo.some(
        (info) => info.extractionType === ExtractionType.APPOINTMENT,
      )
    ) {
      return CallOutcome.APPOINTMENT_SCHEDULED;
    }
    if (
      extractedInfo.some(
        (info) => info.extractionType === ExtractionType.PROBLEM_DESCRIPTION,
      )
    ) {
      return CallOutcome.ISSUE_RESOLVED;
    }
    if (
      extractedInfo.some(
        (info) => info.extractionType === ExtractionType.CONTACT_INFO,
      )
    ) {
      return CallOutcome.INFORMATION_GATHERED;
    }
    return CallOutcome.SUCCESSFUL;
  }

  private generateActionItems(extractedInfo: ExtractedInformation[]): string[] {
    const items: string[] = [];

    const emailInfo = extractedInfo.find((info) => info.key === 'email');
    if (emailInfo && typeof emailInfo.value === 'string') {
      items.push(`Follow up via email at ${emailInfo.value}`);
    }

    const problemInfo = extractedInfo.find(
      (info) => info.extractionType === ExtractionType.PROBLEM_DESCRIPTION,
    );
    if (problemInfo && typeof problemInfo.value === 'string') {
      items.push(`Address ${problemInfo.value} issue`);
    } else if (
      problemInfo &&
      typeof problemInfo.value === 'object' &&
      problemInfo.value !== null
    ) {
      items.push(`Address ${JSON.stringify(problemInfo.value)} issue`);
    }

    if (extractedInfo.length === 0) {
      items.push('No specific action items identified');
    }

    return items;
  }

  private generateRecommendations(
    transcripts: CallTranscript[],
    extractedInfo: ExtractedInformation[],
  ): string[] {
    const recommendations: string[] = [];

    if (extractedInfo.length > 0) {
      recommendations.push(
        'Information extraction worked effectively during this call',
      );
    }

    if (transcripts.length < 5) {
      recommendations.push(
        'Consider encouraging more detailed customer responses',
      );
    }

    recommendations.push(
      'Test call completed successfully - system is functioning as expected',
    );

    return recommendations;
  }

  getActiveSessions(): TestCallSession[] {
    return Array.from(this.activeSessions.values()).filter(
      (session) => session.status === 'active',
    );
  }

  async getAgentForSession(sessionId: string): Promise<Agent> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      throw new NotFoundException('Session not found');
    }

    const agent = await this.agentsService.findOne(
      session.agentId,
      session.userId,
    );
    if (!agent) {
      throw new NotFoundException('Agent not found for session');
    }

    return agent;
  }
}
