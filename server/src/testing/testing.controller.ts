import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { TestingService } from './testing.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('testing')
@UseGuards(JwtAuthGuard)
export class TestingController {
  constructor(private readonly testingService: TestingService) {}

  @Get('sessions')
  getActiveSessions() {
    // For now, return all active sessions
    // In production, filter by user
    return this.testingService.getActiveSessions();
  }

  @Get('sessions/:sessionId')
  getSession(@Param('sessionId') sessionId: string) {
    return this.testingService.getSession(sessionId);
  }
}
