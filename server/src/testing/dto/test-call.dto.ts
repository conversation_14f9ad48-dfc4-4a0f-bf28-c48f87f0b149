import { IsString, IsUUID, IsOptional, IsIn } from 'class-validator';

export class StartTestCallDto {
  @IsUUID()
  agentId: string;

  @IsOptional()
  @IsString()
  @IsIn(['openai', 'gemini'])
  provider?: 'openai' | 'gemini';
}

export class TestMessageDto {
  @IsString()
  type: 'audio' | 'text' | 'control';

  @IsString()
  data: string;
}

export interface TestCallSession {
  id: string;
  agentId: string;
  userId: string;
  startedAt: Date;
  status: 'active' | 'ended';
  callId?: string; // Link to persistent call record
  transcript: Array<{
    timestamp: Date;
    speaker: 'user' | 'agent' | 'system';
    content: string;
    type: 'text' | 'audio' | 'function_call';
  }>;
}
