import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  MessageBody,
  ConnectedSocket,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger } from '@nestjs/common';
import { TestingService } from './testing.service';
import { RealtimeService } from '../integrations/realtime/realtime.service';
import { ProviderType } from '../integrations/realtime/realtime-provider.factory';
import { StartTestCallDto } from './dto/test-call.dto';

interface SocketWithData extends Socket {
  data: {
    userId?: string;
  };
}

@WebSocketGateway({
  namespace: '/testing',
  cors: {
    origin: ['http://localhost:5173', 'http://localhost:3000'],
    methods: ['GET', 'POST'],
    credentials: true,
  },
})
export class TestingGateway
  implements OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(TestingGateway.name);
  private clientSessions = new Map<string, string>(); // clientId -> sessionId

  constructor(
    private readonly testingService: TestingService,
    private readonly realtimeService: RealtimeService,
  ) {}

  handleConnection(client: SocketWithData): void {
    this.logger.log(`Client connected: ${client.id}`);

    // TODO: Add authentication here
    // For now, we'll extract userId from query params or headers
    const userId = client.handshake.query.userId as string;
    if (!userId) {
      this.logger.error('No userId provided in connection');
      client.disconnect();
      return;
    }

    client.data.userId = userId;
  }

  handleDisconnect(client: SocketWithData): void {
    this.logger.log(`Client disconnected: ${client.id}`);

    // End any active session for this client
    const sessionId = this.clientSessions.get(client.id);
    if (sessionId) {
      void this.testingService.endSession(sessionId);
      this.clientSessions.delete(client.id);
    }
  }

  @SubscribeMessage('start-test-call')
  async handleStartTestCall(
    @MessageBody() data: StartTestCallDto,
    @ConnectedSocket() client: SocketWithData,
  ) {
    try {
      const userId = client.data.userId;
      if (!userId) {
        throw new Error('User ID not found in client data');
      }
      this.logger.log(
        `Starting test call for agent ${data.agentId}, user ${userId}`,
      );

      // Create test session
      const session = await this.testingService.createTestSession(
        data.agentId,
        userId,
      );
      this.clientSessions.set(client.id, session.id);

      // Get agent details for prompt
      const agent = await this.testingService.getAgentForSession(session.id);
      if (!agent) {
        throw new Error('Agent not found');
      }

      // Create realtime session with specified provider
      await this.realtimeService.createSession(
        session.id,
        client,
        agent.prompt || 'You are a helpful assistant.',
        data.provider || 'openai', // Default to OpenAI for calendar functions
        userId, // Pass userId for calendar integration
      );

      client.emit('test-call-started', {
        sessionId: session.id,
        agentId: data.agentId,
        provider: data.provider || 'openai',
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;

      this.logger.error(
        `Error starting test call: ${errorMessage}`,
        errorStack,
      );
      client.emit('error', {
        message: 'Failed to start test call',
        error: errorMessage,
      });
    }
  }

  @SubscribeMessage('audio-chunk')
  async handleAudioChunk(
    @MessageBody() audioData: Buffer,
    @ConnectedSocket() client: SocketWithData,
  ) {
    const sessionId = this.clientSessions.get(client.id);
    if (!sessionId) {
      client.emit('error', { message: 'No active test session' });
      return;
    }

    try {
      // Forward audio to realtime service
      await this.realtimeService.sendAudio(sessionId, audioData);
      client.emit('audio-received', { received: true });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;

      this.logger.error(`Error sending audio: ${errorMessage}`, errorStack);
      client.emit('error', { message: 'Failed to send audio' });
    }
  }

  @SubscribeMessage('text-message')
  async handleTextMessage(
    @MessageBody() data: { content: string },
    @ConnectedSocket() client: SocketWithData,
  ) {
    const sessionId = this.clientSessions.get(client.id);
    if (!sessionId) {
      client.emit('error', { message: 'No active test session' });
      return;
    }

    try {
      // Forward text to realtime service
      await this.realtimeService.sendText(sessionId, data.content);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;

      this.logger.error(`Error sending text: ${errorMessage}`, errorStack);
      client.emit('error', { message: 'Failed to send text' });
    }
  }

  @SubscribeMessage('switch-provider')
  async handleSwitchProvider(
    @MessageBody() data: { provider: ProviderType },
    @ConnectedSocket() client: SocketWithData,
  ) {
    const sessionId = this.clientSessions.get(client.id);
    if (!sessionId) {
      client.emit('error', { message: 'No active test session' });
      return;
    }

    try {
      this.logger.log(
        `Switching provider for session ${sessionId} to ${data.provider}`,
      );

      // Get the agent for the current session
      const agent = await this.testingService.getAgentForSession(sessionId);
      if (!agent) {
        client.emit('error', { message: 'Agent not found' });
        return;
      }

      // End current realtime session
      void this.realtimeService.endSession(sessionId);

      // Get userId from client data
      const userId = client.data.userId;

      // Create new session with different provider
      await this.realtimeService.createSession(
        sessionId,
        client,
        agent.prompt || 'You are a helpful assistant.',
        data.provider,
        userId, // Pass userId for calendar integration
      );

      client.emit('provider-switched', {
        sessionId: sessionId,
        provider: data.provider,
      });

      this.logger.log(
        `Successfully switched to provider ${data.provider} for session ${sessionId}`,
      );
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;

      this.logger.error(
        `Error switching provider: ${errorMessage}`,
        errorStack,
      );
      client.emit('error', {
        message: 'Failed to switch provider',
        error: errorMessage,
      });
    }
  }

  @SubscribeMessage('end-test-call')
  handleEndTestCall(@ConnectedSocket() client: SocketWithData) {
    const sessionId = this.clientSessions.get(client.id);
    if (sessionId) {
      // End realtime session
      this.realtimeService.endSession(sessionId).catch((error) => {
        this.logger.error('Error ending realtime session:', error);
      });

      // End testing session
      this.testingService.endSession(sessionId).catch((error) => {
        this.logger.error('Error ending testing session:', error);
      });
      this.clientSessions.delete(client.id);

      client.emit('test-call-ended', {
        sessionId,
        endedAt: new Date(),
      });
    }
  }
}
