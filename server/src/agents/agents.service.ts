import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Agent } from './entities/agent.entity';
import { CreateAgentDto } from './dto/create-agent.dto';
import { UpdateAgentDto } from './dto/update-agent.dto';
import { User } from '../users/entities/user.entity';

@Injectable()
export class AgentsService {
  constructor(
    @InjectRepository(Agent)
    private agentsRepository: Repository<Agent>,
  ) {}

  async create(createAgentDto: CreateAgentDto, user: User): Promise<Agent> {
    const agent = this.agentsRepository.create({
      ...createAgentDto,
      user,
    });
    return this.agentsRepository.save(agent);
  }

  async findAll(userId: string): Promise<Agent[]> {
    return this.agentsRepository.find({
      where: { user: { id: userId } },
      relations: ['user'],
    });
  }

  async findOne(id: string, userId: string): Promise<Agent> {
    const agent = await this.agentsRepository.findOne({
      where: { id, user: { id: userId } },
      relations: ['user'],
    });

    if (!agent) {
      throw new NotFoundException(`Agent with ID ${id} not found`);
    }

    return agent;
  }

  async update(
    id: string,
    updateAgentDto: UpdateAgentDto,
    userId: string,
  ): Promise<Agent> {
    const agent = await this.findOne(id, userId);
    Object.assign(agent, updateAgentDto);
    return this.agentsRepository.save(agent);
  }

  async remove(id: string, userId: string): Promise<void> {
    const agent = await this.findOne(id, userId);
    await this.agentsRepository.remove(agent);
  }
}
