import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
} from '@nestjs/common';
import { AgentsService } from './agents.service';
import { CreateAgentDto } from './dto/create-agent.dto';
import { UpdateAgentDto } from './dto/update-agent.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { UsersService } from '../users/users.service';
import { AuthenticatedRequest } from '../common/types/request.types';

@Controller('agents')
@UseGuards(JwtAuthGuard)
export class AgentsController {
  constructor(
    private readonly agentsService: AgentsService,
    private readonly usersService: UsersService,
  ) {}

  @Post()
  async create(
    @Body() createAgentDto: CreateAgentDto,
    @Request() req: AuthenticatedRequest,
  ) {
    const user = await this.usersService.findOne(req.user.id);
    return this.agentsService.create(createAgentDto, user);
  }

  @Get()
  findAll(@Request() req: AuthenticatedRequest) {
    return this.agentsService.findAll(req.user.id);
  }

  @Get(':id')
  findOne(@Param('id') id: string, @Request() req: AuthenticatedRequest) {
    return this.agentsService.findOne(id, req.user.id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateAgentDto: UpdateAgentDto,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.agentsService.update(id, updateAgentDto, req.user.id);
  }

  @Delete(':id')
  remove(@Param('id') id: string, @Request() req: AuthenticatedRequest) {
    return this.agentsService.remove(id, req.user.id);
  }
}
