import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Call } from '../../calls/entities/call.entity';

@Entity()
export class Agent {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ type: 'text' })
  prompt: string;

  @Column({ nullable: true })
  twilioPhoneNumber: string;

  @Column({ default: false })
  isActive: boolean;

  @Column({ nullable: true })
  googleCalendarId: string;

  @ManyToOne(() => User, (user) => user.agents)
  user: User;

  @OneToMany(() => Call, (call) => call.agent)
  calls: Call[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
