const { Client } = require('pg');
const bcrypt = require('bcrypt');
require('dotenv').config();

async function createAdminUser() {
  const client = new Client({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    user: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || 'postgres',
    database: process.env.DB_DATABASE || 'talkloop',
  });

  try {
    await client.connect();
    console.log('Connected to database');

    // Check if admin user exists
    const checkResult = await client.query(
      'SELECT * FROM "user" WHERE email = $1',
      ['<EMAIL>']
    );

    if (checkResult.rows.length > 0) {
      console.log('Admin user already exists. Updating password...');
      
      // Hash the password
      const hashedPassword = await bcrypt.hash('password', 10);
      
      // Update the password
      await client.query(
        'UPDATE "user" SET password = $1 WHERE email = $2',
        [hashedPassword, '<EMAIL>']
      );
      
      console.log('Admin password updated successfully!');
    } else {
      console.log('Creating new admin user...');
      
      // Hash the password
      const hashedPassword = await bcrypt.hash('password', 10);
      
      // Insert the admin user
      await client.query(
        'INSERT INTO "user" (email, "firstName", "lastName", password, "companyName", "isEmailVerified", "createdAt", "updatedAt") VALUES ($1, $2, $3, $4, $5, $6, $7, $8)',
        [
          '<EMAIL>',
          'Admin',
          'User',
          hashedPassword,
          'Talkloop Admin',
          true,
          new Date(),
          new Date(),
        ]
      );
      
      console.log('Admin user created successfully!');
    }
  } catch (error) {
    console.error('Error creating/updating admin user:', error);
  } finally {
    await client.end();
    console.log('Disconnected from database');
  }
}

createAdminUser();
